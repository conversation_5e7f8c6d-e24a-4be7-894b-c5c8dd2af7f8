(() => {
    const isVisible = (elem) => {
        const style = window.getComputedStyle(elem);
        return style.display !== "none" && style.visibility !== "hidden" && style.opacity !== "0";
    };

    // Common selectors for popups and overlays
    const commonSelectors = [
        // Close buttons first
        'button[class*="close" i]',
        'button[class*="dismiss" i]',
        'button[aria-label*="close" i]',
        'button[title*="close" i]',
        'a[class*="close" i]',
        'span[class*="close" i]',

        // Cookie notices
        '[class*="cookie-banner" i]',
        '[id*="cookie-banner" i]',
        '[class*="cookie-consent" i]',
        '[id*="cookie-consent" i]',

        // Newsletter/subscription dialogs
        '[class*="newsletter" i]',
        '[class*="subscribe" i]',

        // Generic popups/modals
        '[class*="popup" i]',
        '[class*="modal" i]',
        '[class*="overlay" i]',
        '[class*="dialog" i]',
        '[role="dialog"]',
        '[role="alertdialog"]',
    ];

    const removeCommon = () => {
        for (const selector of commonSelectors.slice(0, 6)) {
            const closeButtons = document.querySelectorAll(selector);
            for (const button of closeButtons) {
                if (isVisible(button)) {
                    try {
                        console.log("Click close Btn:", button)
                        button.click();
                    } catch (e) {
                        console.log("Error clicking button:", e);
                    }
                }
            }
        }
    }

    const emptyBlock = [
        // relax now
        "#shopify-section-announcement-bar",
    ]
    const removeEmptyBlockElements = () => {
        for (const selector of emptyBlock) {
            const blockElements = document.querySelectorAll(selector);
            console.log(blockElements)
            blockElements.forEach((elem) => {
                elem.remove();
            });
        }
    };

    setTimeout(removeEmptyBlockElements, 9000)
    setTimeout(removeCommon, 9000)
})()