from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "page_content_summary" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "content" TEXT NOT NULL,
    "summary" TEXT NOT NULL,
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
COMMENT ON TABLE "page_content_summary" IS '和一个 shopify 站点相关，维护的是页面摘要缓存。因为 LLM 对文字生成摘要的结果不稳定，需要维护一个‘长文本’和‘摘要’的关系';
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "page_content_summary";"""
