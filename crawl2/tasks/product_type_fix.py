from loguru import logger

from crawl2.clients import llm
from crawl2.db import operations, models
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync

PRODUCT_TYPE_FIX_PROMPT_TEMPLATE = """
Your task is to predict the product type of the product.
The predicted product type must appear in the given product title.
Absolutely no explanations are allowed.

The product type should be one of the following:
{product_type_list}

The product title is as follows:
{product_title}

Please return the product type in the following format:
```json
{{"product_type": ""}}
```
"""

llm_conf = llm.LLMConfig('usq', 'qwen2.5-32b-chat-fp8-offline', 0, 5000)


async def fix_product_type(metadata: models.ProductMetadata, product_type_list: list[str]) -> str | None:
    if len(product_type_list) == 1:
        return product_type_list[0]
    prompt = PRODUCT_TYPE_FIX_PROMPT_TEMPLATE.format(product_type_list=product_type_list, product_title=metadata.title)
    logger.info(f"Fixing product type for {metadata.url} with prompt: {prompt}")
    extra_body = {
        "guided_json":  {
            "type": "object",
            "properties": {
                "product_type": {
                    "type": "string"
                }
            },
            "required": ["product_type"],
            "additionalProperties": False
        }
    }
    result = await llm.call_llm(llm_conf, prompt, extra_body=extra_body)
    logger.info(f"Fixed product type for {metadata.url} with result: {result}")
    if result is None:
        return None
    if result.get("product_type", "") in product_type_list:
        return result["product_type"]
    return None


@celery_task
async def fix_product_type_for_single_product(product_url: str):
    """修复单个商品的产品类型"""
    metadata = await operations.get_product_metadata(product_url)
    if not metadata:
        logger.error(f"No metadata found for product URL: {product_url}")
        return
    if metadata.product_type and len(metadata.product_type.strip()) > 0:
        logger.info(f"Product type already exists for {metadata.url}, skipping")
        return
    site = await metadata.site.first()
    if site is None:
        logger.error(f"No site found for product URL: {product_url}")
        return
    product_type_list = await operations.get_product_type_list(site.domain)
    logger.info(f"Fixing product type for {metadata.url} with product type list: {product_type_list}")
    product_type = await fix_product_type(metadata, product_type_list)
    await operations.save_product_type(metadata, product_type)


@celery_task
async def fix_product_type_for_all_products(domain: str):
    """修复指定站点所有商品的产品类型"""
    metadata_list = await operations.list_product_metadata(domain)
    for metadata in CeleryTaskTqdmAsync(metadata_list, desc="Fixing product type"):
        await fix_product_type_for_single_product(metadata.url)
