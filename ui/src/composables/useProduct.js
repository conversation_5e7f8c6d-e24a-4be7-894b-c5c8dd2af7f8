import { api } from '@/api/api.js'

export function useProduct() {
  const getProductFaqs = async (domain, productId) => {
    try {
      const response = await api.get(`/api/v1/sites/${domain}/products/${productId}/faqs`)
      return response.data
    } catch (err) {
      console.error('Error fetching product FAQs:', err)
      throw err
    }
  }

  const getProductConcerns = async (domain, productId) => {
    try {
      const response = await api.get(`/api/v1/sites/${domain}/products/${productId}/concerns`)
      return response.data
    } catch (err) {
      console.error('Error fetching product concerns:', err)
      throw err
    }
  }

  const getProductSellingPoints = async (domain, productId) => {
    try {
      const response = await api.get(`/api/v1/sites/${domain}/products/${productId}/selling_points`)
      return response.data
    } catch (err) {
      console.error('Error fetching product selling points:', err)
      throw err
    }
  }

  return {
    getProductFaqs,
    getProductConcerns,
    getProductSellingPoints
  }
}
