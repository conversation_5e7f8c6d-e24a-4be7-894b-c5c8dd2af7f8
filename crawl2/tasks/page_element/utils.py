import importlib.resources
from copy import deepcopy

import numpy as np
from bs4 import BeautifulSoup, Tag
from jinja2 import Template
from crawl2.clients import llm

XPATH_ATTRIBUTE = "data-shop-genius-xpath"
BOUNDING_BOX_ATTRIBUTE = "data-shop-genius-bounding-box"
HIDDEN_MARK_ATTRIBUTE = "data-shop-genius-visibility-status"
HIDDEN_MARK_VALUE = "hidden_or_tiny"
LLM_CONF = llm.LLMConfig('usq', 'gpt-4o-mini', 0.4, 16384)


def load_js_script(js_name: str):
    return importlib.resources.read_text("crawl2.tasks.page_element.resource.js", js_name)


def load_c4a_script(script_name: str):
    return importlib.resources.read_text("crawl2.tasks.page_element.resource.c4a", script_name)


def load_general_prompt(html_content: str):
    template = Template(importlib.resources.read_text(
        "crawl2.tasks.page_element.resource.prompt",
        "NER.md"
    ))
    return template.render(html=html_content)


def load_theme_coherence_prompt(html_content: str):
    template = Template(importlib.resources.read_text(
        "crawl2.tasks.page_element.resource.prompt",
        "theme_coherence.md"
    ))
    return template.render(html=html_content)


def load_selling_point_summary_prompt(html_content: str):
    template = Template(importlib.resources.read_text(
        "crawl2.tasks.page_element.resource.prompt",
        "selling_point_summary.md"
    ))
    return template.render(html=html_content)


def screenshot_to_image(screenshot_b64: str):
    from base64 import b64decode
    from io import BytesIO
    from PIL import Image
    image = Image.open(BytesIO(b64decode(screenshot_b64)))
    return image


def norm_img_src(img_src: str):
    from urllib.parse import urlparse

    if img_src.startswith("//"):
        img_src = f"https:{img_src}"
    parsed = urlparse(img_src)
    if not parsed.path:
        return None
    if not parsed.scheme:
        return None
    if not parsed.hostname:
        return None
    norm_src = parsed.scheme + "://" + parsed.hostname + parsed.path
    return norm_src


def extract_xpath(element: Tag):
    """根据自定义的 xpath 标记获取元素的 xpath 信息"""
    import json

    if XPATH_ATTRIBUTE not in element.attrs:
        raise ValueError(f"缺少 {XPATH_ATTRIBUTE} 属性, element {element}")
    xpath_list = json.loads(element.attrs[XPATH_ATTRIBUTE])
    assert isinstance(xpath_list, list)
    return xpath_list


def extract_bounding_box(element: Tag):
    """根据自定义的 bounding-box 标记获取元素坐标信息"""
    import json
    if BOUNDING_BOX_ATTRIBUTE not in element.attrs:
        raise ValueError(f"缺少 {BOUNDING_BOX_ATTRIBUTE} 属性, element {element}")
    return json.loads(element.attrs[BOUNDING_BOX_ATTRIBUTE])


def tag_to_llm_content(node: Tag):
    node = deepcopy(node)
    for tag in node.find_all(["script", "link", "style", "svg", "path", "iframe"]):
        tag.decompose()
    for tag in node.find_all(attrs={HIDDEN_MARK_ATTRIBUTE: HIDDEN_MARK_VALUE}):
        tag.decompose()
    for tag in node.find_all(True):
        allow = ["id", "class", "title"]
        for attr in tag.attrs:
            if attr.startswith("data-") or attr.startswith("aria-"):
                if attr.startswith("data-shop-genius-"):
                    continue
                allow.append(attr)
        tag.attrs = {k: v for k, v in tag.attrs.items() if k in allow}
    return "".join(line.strip() for line in node.prettify().split("\n"))


def tag_need_ignore(node):
    """DOM 节点是否需要被处理"""
    if not isinstance(node, Tag):
        return True
    if node.attrs.get(HIDDEN_MARK_ATTRIBUTE) == HIDDEN_MARK_VALUE:
        return True
    if node.name.lower() in ["script", "link", "style", "svg", "path", "iframe"]:
        return True
    return False


class ProductGalleryPartitioner:
    """
    通过分析 DOM 节点内图片的渲染尺寸，将一个商品主图模块的 HTML
    分割为主图展示区域和图片列表选择区域。

    依赖于在 HTML 元素上预先绑定的 'data-shop-genius-bounding-box' 属性，
    该属性包含了从浏览器 computedStyle 中获取的 width 和 height。
    """

    def __init__(self, area_ratio_threshold: float = 4.0, percentile: int = 70, ratio_tolerance: float = 0.3):
        """
        初始化分区器。

        Args:
            area_ratio_threshold (float): 面积比率阈值。当最大面积与最小面积的比率超过此值时，
                                          我们认为找到了分裂点。例如，4.0 意味着大图区域的图片
                                          面积至少是小图区域的4倍。
            percentile (int): 用于计算代表性面积的百分位数值。70 (P70) 是一个很好的选择，
                              可以忽略离群的较小值。
            ratio_tolerance (float): 执行 prune 策略时，允许图片的大小在容差范围内。例如 0.1 表示允许 ±10% 波动
        """
        if not (0 < percentile <= 100):
            raise ValueError("Percentile must be between 0 and 100.")
        self.AREA_RATIO_THRESHOLD = area_ratio_threshold
        self.PERCENTILE = percentile
        self.RATIO_TOLERANCE = ratio_tolerance

    def _get_bounding_box_area(self, element: Tag) -> float:
        """从元素的 data 属性中解析 width 和 height，并计算面积。"""
        try:
            bounding_box = extract_bounding_box(element)
        except ValueError:
            return 0.0
        return float(bounding_box["width"]) * float(bounding_box["height"])

    def _get_image_areas_in_node(self, node: Tag) -> list[float]:
        """查找给定节点下的所有图片，并返回它们的有效面积列表。"""
        if not isinstance(node, Tag):
            return []
        imgs = node.find_all('img')
        areas = [self._get_bounding_box_area(img) for img in imgs]
        # 只返回大于0的面积，过滤掉不可见或无尺寸的图片
        return [area for area in areas if area > 0]

    def _find_split_node(self, current_node: Tag) -> tuple[Tag | None, Tag | None]:
        """
        递归地查找分裂节点。
        分裂节点是主图区域和缩略图区域的最近共同父节点。
        """
        if not isinstance(current_node, Tag):
            return None, None

        children_stats = []
        # 1. 分析所有直属子节点
        for child in current_node.children:
            if not isinstance(child, Tag):
                continue

            image_areas = self._get_image_areas_in_node(child)

            if image_areas:
                # 2. 计算每个包含图片的子节点的代表性面积 (P70)
                representative_area = np.percentile(image_areas, self.PERCENTILE)
                children_stats.append({
                    'node': child,
                    'area': representative_area
                })

        # 3. 判断当前节点是否为分裂点
        if len(children_stats) == 2:
            children_stats.sort(key=lambda x: x['area'])
            min_area_child = children_stats[0]
            max_area_child = children_stats[-1]

            # 如果最小面积为0，则无法计算比率，跳过
            if min_area_child['area'] > 0:
                ratio = max_area_child['area'] / min_area_child['area']
                # 如果面积比率超过阈值，则认为找到了分裂点
                if ratio >= self.AREA_RATIO_THRESHOLD:
                    # 大的是主图区域，小的是缩略图区域
                    return max_area_child['node'], min_area_child['node']

        # 4. 如果当前不是分裂点，则向下一层递归
        # print(f"-> No split at <{current_node.name}>, recursing deeper...")
        for child in current_node.children:
            if isinstance(child, Tag):
                main_area, thumb_area = self._find_split_node(child)
                # 如果在更深的层级找到了，就将结果向上传递
                if main_area is not None and thumb_area is not None:
                    return main_area, thumb_area

        return None, None

    def partition(self, html_content: str | Tag) -> tuple[Tag | None, Tag | None]:
        """
        执行分区的主方法。

        Args:
            html_content (str | Tag): 包含了绑定好 bounding box 信息的 HTML 字符串。

        Returns:
            tuple[Tag | None, Tag | None]: 一个元组，第一个元素是主图展示区域的
                                           BeautifulSoup Tag，第二个是图片列表选择区域的
                                           Tag。如果找不到，则为 None。
        """
        if isinstance(html_content, str):
            soup = BeautifulSoup(html_content, 'html.parser')
        elif isinstance(html_content, Tag):
            soup = deepcopy(html_content)
        else:
            raise TypeError()

        main_area_tree, thumb_area_tree = self._find_split_node(soup)

        return main_area_tree, thumb_area_tree

    def is_same_size_group(self, html_content: str | Tag) -> bool:
        """
        执行区域检查，如果区域内图片大小基本一致（在 ratio_tolerance 内）
        """
        if isinstance(html_content, str):
            soup = BeautifulSoup(html_content, 'html.parser')
        elif isinstance(html_content, Tag):
            soup = deepcopy(html_content)
        else:
            raise TypeError()

        imgs = soup.find_all('img')
        # 只返回大于0的面积，过滤掉不可见或无尺寸的图片
        areas = [area for img in imgs if (area := self._get_bounding_box_area(img)) > 0]
        if not areas:
            return False
        if len(areas) == 1:
            return True
        ratio = max(areas) / min(areas) if min(areas) > 0 else float("inf")
        return ratio <= (1 + self.RATIO_TOLERANCE)


def flow_to_mermaid(flow, name="Flow"):
    """
    将 PocketFlow 的 Flow 转换为 Mermaid 流程图
    """
    import warnings

    visited = set()
    node_ids = {}
    counter = {}

    def node_name(node):
        cls = node.__class__.__name__
        counter[cls] = counter.get(cls, 0) + 1
        node_id = f"{cls}_{counter[cls]}"
        node_ids[id(node)] = node_id
        return node_id

    lines = ["flowchart TD"]

    def traverse(node):
        if id(node) in visited:
            return
        visited.add(id(node))

        nid = node_ids.get(id(node)) or node_name(node)
        lines.append(f'    {nid}["{node.__class__.__name__}"]')

        # 遍历 successors
        for action, succ in node.successors.items():
            if succ is None:
                msg = f"Node {node.__class__.__name__} has successor '{action}' but it's None"
                warnings.warn(msg)
                continue

            sid = node_ids.get(id(succ)) or node_name(succ)
            lines.append(f'    {nid} -->|{action}| {sid}["{succ.__class__.__name__}"]')
            traverse(succ)

        # 自动检查「未接收的 action」
        # 规则：如果 Node 有可能返回 "default"，但 successors 没有接收，就报错
        if "default" not in node.successors:
            msg = f"Node {node.__class__.__name__} 没有定义 default successor，可能导致 Flow 中断"
            warnings.warn(msg)

    start_id = "Start"
    first_id = node_name(flow.start_node)
    lines.append(f'    {start_id}(["{name} Start"]) --> {first_id}')
    traverse(flow.start_node)

    return "```mermaid\n" + "\n".join(lines) + "\n```"
