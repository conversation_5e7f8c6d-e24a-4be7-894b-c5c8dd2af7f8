from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "product_type_pv_schema" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "product_type" VARCHAR(255) NOT NULL,
    "properties" JSONB NOT NULL,
    "llm_record_id" INT NOT NULL REFERENCES "llm_record" ("id") ON DELETE CASCADE,
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_493ff1" ON "product_type_pv_schema" ("site_id", "product_type", "llm_record_id");
COMMENT ON COLUMN "product_type_pv_schema"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_type_pv_schema"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_type_pv_schema"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_type_pv_schema"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_type_pv_schema"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_type_pv_schema"."product_type" IS '网站展示的字面产品类型';
COMMENT ON COLUMN "product_type_pv_schema"."properties" IS '产品类型的属性点';
COMMENT ON TABLE "product_type_pv_schema" IS '产品类型的属性点';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "product_type_pv_schema";"""
