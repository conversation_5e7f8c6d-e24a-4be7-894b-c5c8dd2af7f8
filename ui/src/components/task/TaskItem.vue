<template>
  <div class="p-3 border-b border-gray-200 last:border-b-0">
    <div class="flex gap-4">
      <!-- 第一栏：状态和时间信息 -->
      <div class="flex flex-col gap-2 w-48 flex-shrink-0">
        <!-- 选择复选框 -->
        <div v-if="canSelect" class="flex items-center gap-2">
          <input
            type="checkbox"
            :checked="isSelected"
            @change="$emit('toggle-selection', task.task_id)"
            class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
          />
          <span class="text-xs text-gray-500">选择</span>
        </div>
        
        <!-- 任务状态 -->
        <div class="flex items-center gap-2">
          <span class="text-sm font-medium px-2 py-1 rounded bg-gray-50" :style="{ color: getStatusColor(task.status) }">
            {{ formatStatus(task.status) }}
          </span>
        </div>
        
        <!-- 时间信息 -->
        <div class="flex flex-col gap-1 text-xs text-gray-500">
          <span>创建: {{ formatDateTime(task.created_at) }}</span>
          <span v-if="task.finished_at" class="text-green-600">
            完成: {{ formatDateTime(task.finished_at) }}
          </span>
          <span v-if="taskDuration && task.finished_at" class="text-blue-600 font-medium">
            耗时: {{ taskDuration }}
          </span>
          <span v-if="isRunning" class="text-orange-600 font-medium">
            已运行: {{ runningDuration }}
          </span>
        </div>
      </div>

      <!-- 第二栏：日志入口和进度条 -->
      <div class="flex flex-col gap-2 w-40 flex-shrink-0">
        <!-- 日志查看入口 -->
        <div v-if="task.log_url" class="flex justify-center">
          <a 
            :href="task.log_url"
            target="_blank"
            class="text-blue-600 no-underline text-sm font-medium px-3 py-1 rounded bg-blue-50 border border-blue-200 transition-all duration-200 hover:bg-blue-100 hover:border-blue-300"
            title="查看日志"
          >
            查看日志
          </a>
        </div>

        <!-- 工作流详情入口 -->
        <div v-if="task.workflow_id" class="flex justify-center">
          <router-link
            :to="`/workflows/${task.workflow_id}`"
            class="text-purple-600 no-underline text-sm font-medium px-3 py-1 rounded bg-purple-50 border border-purple-200 transition-all duration-200 hover:bg-purple-100 hover:border-purple-300"
            title="查看工作流详情"
          >
            查看工作流
          </router-link>
        </div>
        
        <!-- 取消任务按钮 -->
        <div v-if="canCancelTask" class="flex justify-center">
          <button
            @click="openCancelDialog"
            class="text-red-600 no-underline text-sm font-medium px-3 py-1 rounded bg-red-50 border border-red-200 transition-all duration-200 hover:bg-red-100 hover:border-red-300"
            title="取消任务"
          >
            取消任务
          </button>
        </div>

        <!-- 重试按钮：FAILED/CANCELED 才显示 -->
        <div v-if="canRetryTask" class="flex justify-center">
          <button
            @click="handleRetry"
            class="text-green-600 no-underline text-sm font-medium px-3 py-1 rounded bg-green-50 border border-green-200 transition-all duration-200 hover:bg-green-100 hover:border-green-300"
            title="重试任务"
          >
            重试
          </button>
        </div>
        
        <!-- 进度条 -->
        <div v-if="task.progress" class="flex flex-col gap-1">
          <div class="w-full h-2 bg-gray-200 rounded overflow-hidden">
            <div class="h-full bg-green-500 transition-all duration-300" :style="{ width: progressPercent + '%' }"></div>
          </div>
          <span class="text-xs text-gray-600 text-center">
            {{ progressText }}
          </span>
        </div>
      </div>

      <!-- 第三栏：任务详细信息 -->
      <div class="flex-1 min-w-0">
        <!-- 任务名称和参数（函数调用形式） -->
        <div class="mb-2">
          <div class="text-gray-800 break-all leading-relaxed min-w-0 group relative" :title="task.name">
            <span class="font-bold relative">
              {{ task.name }}
              <!-- 悬浮显示的搜索图标 -->
              <button
                v-if="canSearch"
                class="absolute -right-6 -top-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded"
                @click="handleSearchTaskName"
                title="搜索同名任务"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </button>
            </span><span v-if="task.params && Object.keys(task.params).length > 0">({{ formatParams(task.params) }})</span>
          </div>
        </div>
        
        <!-- 任务描述 -->
        <div v-if="taskDescription" class="text-xs text-gray-500">
          <span class="break-all leading-relaxed task-description" :title="taskDescription">
            {{ taskDescription }}
          </span>
        </div>
      </div>

      <!-- 第四栏：维护者信息 -->
      <div class="w-32 flex-shrink-0">
        <div v-if="taskMaintainer" class="flex flex-col gap-2">
          <span class="text-xs text-gray-500 text-center">维护者</span>
          <div class="flex items-center justify-center gap-1">
            <span class="text-blue-600">👤</span>
            <span class="text-blue-600 text-xs font-medium break-all text-center" :title="`维护者: ${taskMaintainer}`">{{ taskMaintainer }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 取消确认对话框 -->
  <CancelConfirmDialog
    :show="showCancelDialog"
    title="取消任务"
    :description="`确定要取消任务「${props.task.name}」吗？此操作不可撤销。`"
    :tasks="[{ id: props.task.task_id, displayInfo: `${props.task.name} (${formatParams(props.task.params)})` }]"
    @close="closeCancelDialog"
    @cancel-completed="handleCancelCompleted"
  />
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useTasks } from '@/composables/useTasks.js'
import CancelConfirmDialog from './CancelConfirmDialog.vue'

const props = defineProps({
  task: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  },
  canSelect: {
    type: Boolean,
    default: false
  },
  canSearch: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['task-canceled', 'toggle-selection', 'search-task-name', 'task-retried'])

// 取消确认对话框状态
const showCancelDialog = ref(false)

// 使用tasks composable
const { getTaskDescription, getTaskMaintainer, fetchTaskDefinitions, retryTask, getTaskDetails } = useTasks()

// 获取任务描述和维护者信息
const taskDescription = computed(() => getTaskDescription(props.task.name))
const taskMaintainer = computed(() => getTaskMaintainer(props.task.name))

// 用于触发运行时长更新的响应式变量
const now = ref(new Date())
let timer = null

// 格式化任务状态
const formatStatus = (status) => {
  const statusMap = {
    'PENDING': '等待中',
    'STARTED': '进行中',
    'SUCCESS': '已完成',
    'FAILURE': '失败',
    'FAILED': '失败',
    'CANCELED': '已取消',
    'RETRIED': '已重试'
  }
  return statusMap[status] || status
}

// 获取状态对应的颜色
const getStatusColor = (status) => {
  const colorMap = {
    'PENDING': '#f0ad4e',
    'STARTED': '#5bc0de',
    'SUCCESS': '#5cb85c',
    'FAILURE': '#d9534f',
    'FAILED': '#d9534f',
    'CANCELED': '#6c757d',
    'RETRIED': '#6f42c1'
  }
  return colorMap[status] || '#999'
}

// 格式化参数为紧凑的字符串
const formatParams = (params) => {
  if (!params || Object.keys(params).length === 0) return '无'
  
  return Object.entries(params)
    .map(([key, value]) => {
      const formattedValue = typeof value === 'string' ? value : JSON.stringify(value)
      return `${key}: ${formattedValue}`
    })
    .join(', ')
}

// 格式化日期时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const isToday = date.toDateString() === now.toDateString()
  
  if (isToday) {
    // 今天只显示时间
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  } else {
    // 其他日期显示日期和时间
    return date.toLocaleDateString('zh-CN', { 
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  }
}

// 进度百分比和文本
const progressPercent = computed(() => {
  if (!props.task.progress || !props.task.progress.total) return 0
  return Math.round((props.task.progress.n / props.task.progress.total) * 100)
})
const progressText = computed(() => {
  if (!props.task.progress) return ''
  const { n, total, desc } = props.task.progress
  if (typeof n === 'number' && typeof total === 'number') {
    return `${n}/${total}${desc ? ' ' + desc : ''}`
  }
  return desc || ''
})

// 计算任务总耗时
const taskDuration = computed(() => {
  if (!props.task.created_at || !props.task.finished_at) return ''
  const createdAt = new Date(props.task.created_at)
  const finishedAt = new Date(props.task.finished_at)
  const duration = finishedAt.getTime() - createdAt.getTime()
  const hours = Math.floor(duration / (1000 * 60 * 60))
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟`
  } else {
    return '小于1分钟'
  }
})

// 判断任务是否正在运行
const isRunning = computed(() => {
  return ['PENDING', 'STARTED'].includes(props.task.status)
})

// 计算任务已运行时长
const runningDuration = computed(() => {
  if (!isRunning.value || !props.task.created_at) return ''
  const createdAt = new Date(props.task.created_at)
  const duration = now.value.getTime() - createdAt.getTime()
  const hours = Math.floor(duration / (1000 * 60 * 60))
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟`
  } else {
    return '小于1分钟'
  }
})

// 是否可以取消任务（基于任务状态和父组件设置）
const canCancelTask = computed(() => {
  return ['PENDING', 'STARTED'].includes(props.task.status)
})

// 是否可以重试任务（FAILED 或 CANCELED）
const canRetryTask = computed(() => {
  return ['FAILED', 'CANCELED'].includes(props.task.status)
})

// 打开取消对话框
const openCancelDialog = () => {
  showCancelDialog.value = true
}

// 关闭取消对话框
const closeCancelDialog = () => {
  showCancelDialog.value = false
}

// 处理取消完成事件
const handleCancelCompleted = (result) => {
  // 检查是否有成功的取消操作
  const successResults = result.results.filter(r => r.success)
  if (successResults.length > 0) {
    // 更新本地任务状态
    successResults.forEach(r => {
      if (r.taskId === props.task.task_id) {
        props.task.status = 'CANCELED'
        props.task.error = 'Task was canceled by user'
        props.task.finished_at = new Date().toISOString()
        
        // 触发事件通知父组件
        emit('task-canceled', props.task.task_id)
      }
    })
  }
  
  // 关闭对话框
  showCancelDialog.value = false
}

// 处理重试
const handleRetry = async () => {
  try {
    const res = await retryTask(props.task.task_id)
    // 后端会把原任务标记为 RETRIED，拿返回的任务详情刷新本地显示
    if (res && res.status) {
      props.task.status = res.status
      props.task.finished_at = res.finished_at || new Date().toISOString()
    } else {
      // 如果返回为空，兜底设置状态
      props.task.status = 'RETRIED'
      props.task.finished_at = new Date().toISOString()
    }
    // 通知父组件发生了重试，父组件可以据此刷新数据
    emit('task-retried', props.task.task_id)
  } catch (e) {
    console.error('Retry task failed', e)
    // 可选：给出 UI 提示
  }
}

// 处理搜索同名任务
const handleSearchTaskName = () => {
  emit('search-task-name', props.task.name)
}

// 组件挂载时确保有task definitions数据
onMounted(async () => {
  try {
    await fetchTaskDefinitions()
  } catch (error) {
    console.warn('Failed to fetch task definitions for TaskItem:', error)
  }
  
  // 设置定时器，每分钟更新一次运行时长
  if (isRunning.value) {
    timer = setInterval(() => {
      now.value = new Date()
    }, 60000) // 每分钟更新一次
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})
</script>

<style scoped>
.task-description {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 2.4em; /* 2行文本的高度 */
  line-height: 1.2em;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>