import axios from "axios";
import { ref, watchEffect, toValue } from "vue";
import { unpack } from '@rrweb/packer'
import dayjs from "dayjs";

export function useReplayData(sessionId) {
  const data = ref([]);
  const error = ref(null);
  const loading = ref(false);

  watchEffect(async () => {
    // reset state before fetching..
    data.value = [];
    error.value = null;

    // resolve the url value synchronously so it's tracked as a
    // dependency by watchEffect()
    const sessionIdValue = toValue(sessionId);
    console.log('session update', sessionIdValue)
    if (sessionIdValue.length === 0) {
      return {
        data,
        error,
      }
    }
    loading.value = true;

    const url = `https://usq-apiv3.jolect.com/session-replays/sessions/${sessionIdValue}`;

    try {
      // unref() will return the ref value if it's a ref
      // otherwise the value will be returned as-is
      const { data: sessionData } = await axios.get(url, {
        headers: {
          // apikey: 'xGiNMxHRs4UJnVUM7HGL'
        }
      });
      console.log("session result", sessionData);

      const eventPromises = sessionData.replays.map(async (r) => {
        const { download_urls: urls, replay_id } = r;
        // console.log('uu', urls)
        const dls = urls.map((url) => {
          return axios.get(url);
        })
        const jsonResList = await Promise.all(dls);
        const events =  jsonResList.reduce((arr, res) => {
          const events = res.data?.events ?? res.data?.data.events ?? []
          const oneFileEvents = events.map((ev) => {
            return unpack(ev.rawEvent);
          });
          return [
            ...arr,
            ...oneFileEvents,
          ]
        }, []).sort((a, b) => {
          return a.timestamp - b.timestamp;
        })

        const initalEvent = events[0]

        return {
          replay_id,
          events,
          dataValid: initalEvent?.type === 4,
          startTime: initalEvent ? dayjs(initalEvent.timestamp).format('YYYY-MM-DD HH:mm:ss') : ''
        }
      })

      const result = await Promise.all(eventPromises)

      console.log('result', result)

      data.value = result;
    } catch (e) {
      error.value = e;
    } finally {
      loading.value = false;
    }
  });

  return { data, error, loading };
}
