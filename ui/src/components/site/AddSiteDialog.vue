<script setup>
import { ref } from 'vue'
import { api } from '@/api/api.js'

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['update:show', 'site-added'])

const url = ref('')
const loading = ref(false)
const error = ref(null)

const close = () => {
  emit('update:show', false)
  url.value = ''
  error.value = null
}

const addSite = async () => {
  if (!url.value) {
    error.value = '请输入站点 URL'
    return
  }

  try {
    loading.value = true
    error.value = null
    
    // 调用后端 API 添加站点
    const response = await api.post('/api/v1/sites', {
      site_url: url.value
    })
    
    emit('site-added', response.data)
    close()
  } catch (err) {
    error.value = err.response?.data?.detail || err.message || '添加站点失败'
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div v-if="show" class="fixed inset-0 bg-opacity-80 flex items-center justify-center z-[1000]" @click="close">
    <div class="bg-white rounded-lg w-full max-w-[500px] shadow-lg" @click.stop>
      <div class="flex justify-between items-center p-4 px-6 border-b border-gray-200">
        <h3 class="m-0 text-gray-800 text-xl">添加新站点</h3>
        <button class="bg-transparent border-none text-2xl text-gray-500 cursor-pointer p-1 leading-none hover:text-green-500" @click="close">×</button>
      </div>
      
      <div class="p-6">
        <div class="mb-4">
          <label for="site-url" class="block mb-2 text-gray-800 font-medium">站点 URL</label>
          <input
            id="site-url"
            v-model="url"
            type="text"
            placeholder="请输入 Shopify 站点 URL"
            :disabled="loading"
            class="w-full p-3 border border-gray-300 rounded text-base transition-colors duration-200 focus:outline-none focus:border-green-500"
            @keyup.enter="addSite"
          />
        </div>
        
        <div v-if="error" class="text-red-500 text-sm mt-2">
          {{ error }}
        </div>
      </div>
      
      <div class="p-4 px-6 border-t border-gray-200 flex justify-end gap-4">
        <button class="px-6 py-2 rounded text-base cursor-pointer transition-all duration-200 bg-gray-50 border border-gray-300 text-gray-700 hover:bg-gray-100 disabled:opacity-70 disabled:cursor-not-allowed" @click="close" :disabled="loading">取消</button>
        <button class="px-6 py-2 rounded text-base cursor-pointer transition-all duration-200 bg-green-500 border border-green-500 text-white hover:bg-green-600 disabled:opacity-70 disabled:cursor-not-allowed" @click="addSite" :disabled="loading">
          <span v-if="loading">添加中...</span>
          <span v-else>添加</span>
        </button>
      </div>
    </div>
  </div>
</template> 