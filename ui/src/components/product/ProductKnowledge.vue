<script setup>
import CrawlTaskTrigger from '../task/CrawlTaskTrigger.vue'
import { ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { api } from '@/api/api.js'

const props = defineProps({
  markdown: {
    type: String,
    required: true
  },
  documents: {
    type: Array,
    required: true
  },
  productUrl: {
    type: String,
    required: true
  }
})

const route = useRoute()
const grouped = computed(() => {
  const groups = {}
  ;(props.documents || []).forEach((doc, idx) => {
    const topic = doc.topic || 'Others'
    if (!groups[topic]) groups[topic] = []
    groups[topic].push({ ...doc, __index: idx })
  })
  return groups
})

// Keep only a single saving state for bulk operations
const saving = ref(false)

// Bulk edit mode
const editMode = ref(false)
const editableDocs = ref([])

watch(() => props.documents, (newDocs) => {
  if (!editMode.value) return
  editableDocs.value = (newDocs || []).map(d => ({
    topic: d.topic || '',
    title: d.title || '',
    content: d.content || ''
  }))
}, { immediate: false, deep: true })

const enterEditMode = () => {
  editableDocs.value = (props.documents || []).map(d => ({
    topic: d.topic || '',
    title: d.title || '',
    content: d.content || ''
  }))
  editMode.value = true
}

const cancelEdit = () => {
  editMode.value = false
  editableDocs.value = []
}

const addDocument = () => {
  editableDocs.value.push({ topic: '', title: '', content: '' })
}

const removeDocument = (index) => {
  editableDocs.value.splice(index, 1)
}

const saveAll = async () => {
  try {
    saving.value = true
    const domain = route.params.domain
    const productId = route.params.productId
    // 过滤掉完全空白的条目
    const payload = editableDocs.value.filter(d => (d.content || '').trim() !== '')
    await api.put(`/api/v1/products/sites/${domain}/products/${productId}/knowledge_documents`, payload)
    // 同步更新到 props.documents（注意：直接修改 props 不是最佳实践，但与现有代码保持一致）
    // 清空后按新内容填充
    props.documents.length = 0
    payload.forEach(d => props.documents.push({ ...d }))
    editMode.value = false
  } catch (e) {
    console.error('Failed to save knowledge documents', e)
    alert('Save failed')
  } finally {
    saving.value = false
  }
}
</script>

<template>
  <div class="bg-white rounded-lg p-6 shadow">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-gray-800 text-xl m-0">Knowledge Documents</h3>
      <div class="flex items-center gap-2">
        <CrawlTaskTrigger
          taskName="extract_single_product_knowledge"
          displayName="Re-extract Knowledge"
          :defaultParams="{ product_url: productUrl, clear_existing: true }"
        />
        <button v-if="!editMode" class="px-3 py-1 rounded bg-blue-500 text-white hover:bg-blue-600" @click="enterEditMode">
          Edit All
        </button>
        <CrawlTaskTrigger
          taskName="sync_single_product_knowledge_to_qdrant"
          displayName="Re-sync knowledge to qdrant"
          :defaultParams="{ product_url: productUrl}"
        />
      </div>
    </div>

    <div v-if="!editMode">
      <div v-if="documents && documents.length" class="mt-4 space-y-6">
        <div v-for="(docs, topic) in grouped" :key="topic" class="">
          <div class="text-lg font-semibold text-gray-800 mb-2">{{ topic }} ({{ docs.length }})</div>
          <div class="space-y-3">
            <div v-for="doc in docs" :key="doc.__index" class="p-4 rounded border border-gray-200 bg-gray-50">
              <div class="flex items-start justify-between gap-4">
                <div class="min-w-0">
                  <div class="font-medium text-gray-900 truncate">{{ doc.title || 'Untitled' }}</div>
                  <div class="text-sm text-gray-600 mt-1 whitespace-pre-wrap break-words">
                    {{ doc.content?.slice(0, 300) }}<span v-if="doc.content && doc.content.length > 300">...</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="text-gray-500">No knowledge documents.</div>
    </div>

    <div v-else class="space-y-4">
      <div class="flex items-center justify-between">
        <div class="text-lg font-semibold text-gray-800">Edit All Documents ({{ editableDocs.length }})</div>
        <div class="flex items-center gap-2">
          <button class="px-3 py-1 rounded bg-gray-200 hover:bg-gray-300" @click="cancelEdit" :disabled="saving">Cancel</button>
          <button class="px-3 py-1 rounded bg-green-500 text-white hover:bg-green-600" @click="saveAll" :disabled="saving">{{ saving ? 'Saving...' : 'Save All' }}</button>
        </div>
      </div>

      <div class="space-y-3">
        <div v-for="(doc, idx) in editableDocs" :key="idx" class="p-4 rounded border border-gray-200 bg-gray-50">
          <div class="grid grid-cols-1 gap-3">
            <div>
              <label class="text-sm font-medium text-gray-700">Topic</label>
              <input v-model="doc.topic" class="border rounded px-3 py-2 w-full" placeholder="Topic" />
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700">Title</label>
              <input v-model="doc.title" class="border rounded px-3 py-2 w-full" placeholder="Title" />
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700">Content</label>
              <textarea v-model="doc.content" class="border rounded px-3 py-2 min-h-[180px] w-full" placeholder="Content"></textarea>
            </div>
          </div>
          <div class="flex justify-end mt-2">
            <button class="px-3 py-1 rounded bg-red-500 text-white hover:bg-red-600" @click="removeDocument(idx)" :disabled="saving">Delete</button>
          </div>
        </div>
      </div>

      <div class="flex justify-center">
        <button class="px-4 py-2 rounded bg-blue-500 text-white hover:bg-blue-600" @click="addDocument" :disabled="saving">+ Add Document</button>
      </div>
    </div>
  </div>
</template> 