def i18n(message: str) -> str:
    mappings = {
        'product_url': '产品URL',
        'product_id': '产品ID',
        'product_type': '产品类型',
        'product_price': '产品价格',
        'product_title': '产品名称',
        'selling_point_id': '卖点ID',
        'selling_point_name': '卖点分类',
        'selling_point_name_chn': '卖点分类(中文)',
        'selling_point_value': '卖点描述',
        'selling_point_value_chn': '卖点描述(中文)',
        'selling_point_marketing_copies': '营销文案',
        'selling_point_marketing_copies_chn': '营销文案(中文)',
        'question': '问题',
        'question_chn': '问题(中文)',
        'answer': '答案',
        'answer_chn': '答案(中文)',
        'status': '标注状态',
        'last_edited_at': '上次标注时间',
        'version_id': '版本ID',
        'faq_id': 'FAQ ID',
    }
    return mappings.get(message, message)
