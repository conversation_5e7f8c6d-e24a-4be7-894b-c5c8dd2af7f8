import asyncio
from crawl4ai import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>onfig, CacheMode
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from crawl2.clients import browser
from crawl2.clients import http
from crawl2.db import operations
from crawl2.schema import CollectionMetaData
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync


# 定义重试装饰器，针对网络错误和超时进行重试
retry_on_network_error = retry(
    stop=stop_after_attempt(3),  # 最多重试3次
    wait=wait_exponential(multiplier=1, min=2, max=10),  # 指数退避，2-10秒
    retry=retry_if_exception_type((asyncio.TimeoutError, ConnectionError, OSError)),
)


async def _crawl_json_meta(url: str, suffix: str) -> dict:
    url = url + suffix
    response = await http.http_get(url)
    response.raise_for_status()
    return response.json()


@retry_on_network_error
async def crawl_raw_content(url: str, timeout: int = 60):
    """
    只爬取页面的原始 markdown 内容，不做 LLM 抽取
    Args:
        url: 要爬取的URL
        timeout: 超时时间（秒），默认60秒
    """

    crawl_config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        remove_overlay_elements=True,
        exclude_external_links=True,
        exclude_social_media_links=True,
        excluded_tags=["nav", "header", "footer"],
        remove_forms=True,
    )

    try:
        result = await browser.crawl_with_timeout(url, config=crawl_config, timeout=timeout)
        if result and hasattr(result, 'success') and result.success:
            return result.markdown if hasattr(result, 'markdown') else None
        else:
            error_msg = result.error_message if result and hasattr(result, 'error_message') else "Unknown error"
            logger.error(f"Crawl page markdown failed for {result.url}: {error_msg}")
            return None
    except asyncio.TimeoutError:
        logger.error(f"Crawl page markdown timeout for {url} after {timeout} seconds")
        raise  # 重新抛出异常以触发重试
    except Exception as e:
        logger.opt(exception=e).error(f"Crawl page markdown failed for {url}: {str(e)}")
        return None


async def _crawl_collection_metadata(
        url: str,
        clear_existing: bool = False,
        timeout: int = 60,
) -> CollectionMetaData | None:
    """
    Crawl collection metadata from a single collection URL.

    :param url: The collection URL to crawl.
    :param timeout: 超时时间（秒），默认60秒
    :return: A CollectionMetaData object containing the collection metadata or None if an error occurs.
    """
    try:
        existed = await operations.get_collection_metadata(url)
        if existed and not clear_existing:
            logger.info(f"Collection metadata already exists for {url}, skipping crawl.")
            return None
        data = await _crawl_json_meta(url, ".json")
        collection_products_data = await _crawl_json_meta(url, "/products.json")
        data["collection"]["products"] = collection_products_data["products"]
        raw_content = await crawl_raw_content(url, timeout=timeout)
        return CollectionMetaData.from_dict(url, data, raw_content=raw_content)
    except Exception as e:
        logger.opt(exception=e).error(f"Error crawling collection metadata from {url}: {e}")
        return None


@celery_task
async def crawl_single_collection_metadata(domain: str, collection_url: str, timeout: int = 60):
    """
    爬取单个指定商品列表页的标题，描述，商品 id 列表等元信息.
    """
    collection = await _crawl_collection_metadata(collection_url, timeout=timeout)
    if collection:
        await operations.save_collection_metadata(domain, collection)


@celery_task
async def crawl_multiple_collections_metadata(
    domain: str,
    collection_urls: list[str],
    clear_existing: bool = False,
    timeout: int = 60
):
    """
    爬取多个指定商品列表页的标题，描述，商品 id 列表等元信息.
    """
    for url in CeleryTaskTqdmAsync(collection_urls, desc="Crawling collection metadata"):
        collection = await _crawl_collection_metadata(url, clear_existing=clear_existing, timeout=timeout)
        if collection:
            await operations.save_collection_metadata(domain, collection)


@celery_task
async def crawl_all_collections_metadata(domain: str, clear_existing: bool = False, timeout: int = 60):
    """
    爬取指定站点所有商品列表页的标题，描述，商品 id 列表等元信息.
    """
    logger.info("开始crawl_all_collections_metadata任务")
    site = await operations.get_site(domain)
    await crawl_multiple_collections_metadata(
        domain,
        site.collection_urls,
        clear_existing=clear_existing,
        timeout=timeout
    )
