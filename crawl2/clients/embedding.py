from typing import List, Union
import httpx
import asyncio


class HttpEmbeddingClient:
    def __init__(self, embed_url: str, max_retries: int = 3, base_delay: float = 1.0):
        """
        Initialize HttpEmbeddingClient
        Args:
            embed_url: URL of the local embedding service
            max_retries: Maximum number of retry attempts
            base_delay: Base delay for exponential backoff in seconds
        """
        self.embed_url = embed_url
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.client = httpx.AsyncClient(timeout=60)  # 增加超时时间从10秒到30秒

    async def embed_async(self, texts: Union[str, List[str]]) -> List[List[float]]:
        """
        Get embeddings from http service asynchronously with retry mechanism
        """
        if isinstance(texts, str):
            texts = [texts]

        for attempt in range(self.max_retries + 1):
            try:
                response = await self.client.post(
                    self.embed_url,
                    json={"inputs": texts},
                )
                if response.status_code != 200:
                    raise Exception(
                        f"Error getting embeddings from local service: {response.text}, inputs: {texts}"
                    )
                return response.json()
            except Exception as e:
                last_exception = e

                # 如果不是最后一次尝试，则等待后重试
                if attempt < self.max_retries:
                    delay = self.base_delay * (2 ** attempt)  # 指数退避
                    await asyncio.sleep(delay)
                else:
                    print(f"Embedding error after {self.max_retries + 1} attempts with texts: {texts}")
                    print(f"Final error details: {str(last_exception)}")
                    raise last_exception


if __name__ == "__main__":
    embedder = HttpEmbeddingClient(embed_url="http://180.76.239.97:8002/sparse_embed")
    print(asyncio.run(embedder.embed_async(["Hello, world!"])))
