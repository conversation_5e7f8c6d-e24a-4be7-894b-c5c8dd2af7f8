from typing import List
from loguru import logger

from crawl2 import schema
from crawl2.db import models


async def get_product_faqs_by_url(product_url: str) -> schema.ProductMetaAndFaq | None:
    """
    根据产品URL获取产品的FAQ信息
    
    Args:
        product_url: 产品URL
        
    Returns:
        产品FAQ信息，如果未找到则返回None
    """
    try:
        # 获取产品元数据
        product = await models.ProductMetadata.filter(url=product_url).get_or_none()
        if not product:
            logger.warning(f"Product metadata for {product_url} not found")
            return None
            
        # 获取FAQ记录
        faq_record = await models.ProductFaqs.filter(product=product).order_by('-id').first()
        if not faq_record:
            logger.info(f"No FAQ record found for product {product_url}")
            return None
            
        # 获取站点信息
        site = await product.site
        
        # 转换FAQ数据
        faq_items = []
        if faq_record.faqs:
            for faq_item in faq_record.faqs:
                faq_items.append(schema.FaqItem(
                    question=faq_item.get('question', ''),
                    answer=faq_item.get('answer', ''),
                    question_chn=faq_item.get('question_chn', ''),
                    answer_chn=faq_item.get('answer_chn', ''),
                ))
                
        return schema.ProductMetaAndFaq(
            id=faq_record.id,
            shopify_domain=site.shopify_domain,
            product_url=product.url,
            product_id=product.product_id,
            product_type=product.product_type,
            product_title=product.title,
            product_price=product.price,
            faqs=faq_items,
            status=faq_record.status,
            last_edited_at=faq_record.last_edited_at,
            version_id=faq_record.version_id,
        )
        
    except Exception as e:
        logger.error(f"Error getting product FAQs for {product_url}: {e}")
        return None


async def get_product_concerns_by_url(product_url: str) -> List[schema.ProductMetaAndConcern]:
    """
    根据产品URL获取产品的顾虑点信息
    
    Args:
        product_url: 产品URL
        
    Returns:
        产品顾虑点信息列表
    """
    try:
        # 获取产品元数据
        product = await models.ProductMetadata.filter(url=product_url).get_or_none()
        if not product:
            logger.warning(f"Product metadata for {product_url} not found")
            return []
            
        # 获取顾虑点记录
        concern_points = await product.concern_points.all()
        
        result = []
        for cp in concern_points:
            result.append(schema.ProductMetaAndConcern(
                id=cp.id,
                product_url=product.url,
                product_id=product.product_id,
                product_type=product.product_type,
                product_title=product.title,
                product_price=product.price,
                concern=cp.name,
                concern_chn=cp.name_chn or '',
                marketing_copies=cp.marketing_copies or [],
                marketing_copies_chn=cp.marketing_copies_chn or [],
                status=cp.status,
                last_edited_at=cp.last_edited_at,
                version_id=cp.version_id,
            ))
            
        return result
        
    except Exception as e:
        logger.error(f"Error getting product concerns for {product_url}: {e}")
        return []


async def get_product_selling_points_by_url(product_url: str) -> list[schema.ProductMetaAndSellingPoint]:
    """
    根据产品URL获取产品的卖点信息
    
    Args:
        product_url: 产品URL
        
    Returns:
        产品卖点信息列表
    """
    try:
        # 获取产品元数据
        product = await models.ProductMetadata.filter(url=product_url).get_or_none()
        if not product:
            logger.warning(f"Product metadata for {product_url} not found")
            return []
            
        # 获取卖点记录
        selling_points = await product.selling_points.all()
        
        result = []
        for sp in selling_points:
                    result.append(schema.ProductMetaAndSellingPoint(
            id=sp.id,
            product_url=product.url,
            product_id=product.product_id,
            product_type=product.product_type,
            product_title=product.title,
            product_price=product.price,
            selling_point_name=sp.name,
            selling_point_name_chn=sp.name_chn or '',
            selling_point_value=sp.description,
            selling_point_value_chn=sp.description_chn or '',
            selling_point_marketing_copies=sp.marketing_copies or [],
            selling_point_marketing_copies_chn=sp.marketing_copies_chn or [],
            status=sp.status,
            created_at=sp.created_at,
            last_edited_at=sp.last_edited_at,
            version_id=sp.version_id,
        ))
            
        return result
        
    except Exception as e:
        logger.error(f"Error getting product selling points for {product_url}: {e}")
        return []
