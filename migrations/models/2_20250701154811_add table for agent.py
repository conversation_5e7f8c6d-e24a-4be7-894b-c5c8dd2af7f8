from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "product_keyword" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "keywords" JSONB NOT NULL,
    "llm_record_id" INT NOT NULL REFERENCES "llm_record" ("id") ON DELETE CASCADE,
    "product_id" INT NOT NULL REFERENCES "product_metadata" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_key_product_023dd3" ON "product_keyword" ("product_id", "llm_record_id");
COMMENT ON COLUMN "product_keyword"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_keyword"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_keyword"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_keyword"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_keyword"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_keyword"."keywords" IS '产品关键词,用来做指代的理解';
COMMENT ON TABLE "product_keyword" IS '产品关键词';
        CREATE TABLE IF NOT EXISTS "product_property_and_value" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "property_values" JSONB NOT NULL,
    "llm_record_id" INT NOT NULL REFERENCES "llm_record" ("id") ON DELETE CASCADE,
    "metadata_id" INT NOT NULL REFERENCES "product_metadata" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_pro_metadat_be1d06" ON "product_property_and_value" ("metadata_id", "llm_record_id");
COMMENT ON COLUMN "product_property_and_value"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_property_and_value"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_property_and_value"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_property_and_value"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_property_and_value"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_property_and_value"."property_values" IS '产品页面的属性值抽取结果';
COMMENT ON TABLE "product_property_and_value" IS '产品页面的属性值抽取结果';
        CREATE TABLE IF NOT EXISTS "product_summary" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "summary" TEXT NOT NULL,
    "llm_record_id" INT NOT NULL REFERENCES "llm_record" ("id") ON DELETE CASCADE,
    "product_id" INT NOT NULL REFERENCES "product_metadata" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_sum_product_bfaad9" ON "product_summary" ("product_id", "llm_record_id");
COMMENT ON COLUMN "product_summary"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_summary"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_summary"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_summary"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_summary"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_summary"."summary" IS '产品摘要, 用于描述产品的主要特点和卖点, 主要在推荐子agent中使用';
COMMENT ON TABLE "product_summary" IS '产品摘要';
        CREATE TABLE IF NOT EXISTS "product_type_pv_schema" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "product_type" VARCHAR(255) NOT NULL,
    "properties" JSONB NOT NULL,
    "llm_record_id" INT NOT NULL REFERENCES "llm_record" ("id") ON DELETE CASCADE,
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_493ff1" ON "product_type_pv_schema" ("site_id", "product_type", "llm_record_id");
COMMENT ON COLUMN "product_type_pv_schema"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_type_pv_schema"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_type_pv_schema"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_type_pv_schema"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_type_pv_schema"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_type_pv_schema"."product_type" IS '网站展示的字面产品类型';
COMMENT ON COLUMN "product_type_pv_schema"."properties" IS '产品类型的属性点';
COMMENT ON TABLE "product_type_pv_schema" IS '产品类型的属性点';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "product_keyword";
        DROP TABLE IF EXISTS "product_summary";
        DROP TABLE IF EXISTS "product_property_and_value";
        DROP TABLE IF EXISTS "product_type_pv_schema";"""
