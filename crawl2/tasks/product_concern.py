import json

from jinja2 import Template
from loguru import logger
from tortoise.exceptions import DoesNotExist

from crawl2 import schema
from crawl2.clients import llm, shopify_knowledge
from crawl2.db import operations, models
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync, CeleryTaskTqdm

PROMPT_TEMPLATE = """You are a professional e-commerce customer service expert. Given product detail page content and common customer concerns, please generate marketing copies that address these concerns effectively.

I will provide you with:
1. Product detail page content
2. Common customer concern points for this category

Please generate:
1. A persuasive marketing copy for each concern point that:
   - Highlights how the product addresses this specific concern
   - Uses compelling and confident language
   - Is concise (30-50 words)
   - Focuses on benefits and unique selling points
   - Builds trust and credibility

Output the results in the following JSON format:
```json
{{
    "concern_points": [
        {{
            "concern": "Concern Point Name",
            "marketing_copies": [
                "Marketing Copy 1 - With specific usage scenario",
                "Marketing Copy 2 - With specific usage scenario",
                "Marketing Copy 3 - With specific usage scenario"
            ]
        }}
    ]
}}

Example output:
```json
{{
    "concern_points": [
        {{
            "name": "design style",
            "marketing_copies": [
                "Experience flexibility and freedom of movement with our FELLEX® eco-friendly insulation. The heated collar and pockets provide just the right amount of warmth while you stay active.",
                "Experience unparalleled warmth and style with our slim-cut heated vest. Designed for a flattering silhouette, the vest offers comfort without adding bulk, making it perfect for layering or wearing alone. Stay warm and chic in any setting.",
                "Experience ultimate comfort with our ultra-soft fleece jacket, designed with a regular fit and 3 heating zones. The heating system ensures you stay warm without compromising on style or comfort."
            ]
        }},
        {{
            "name": "safety",
            "marketing_copies": [
                "Our heated hoodie is built with your safety in mind. Featuring UL-certification, water-resistant design, and multiple safety features, you can enjoy warmth without worry, whether you're in the rain or just lounging.",
                "Experience unparalleled safety with our UL-certified heating technology. Our heated jacket features multiple safety certifications, automatic shut-off, and water-resistant protection, ensuring you stay warm and safe in any weather."
            ]
        }}
    ]
}}
```
```

Product Detail Page Data:
{content}

Customer Concern Points:
{concern_points}

Please ensure:
1. Generate at most 5 marketing copies for each concern point
2. Each marketing copy is persuasive and addresses customer pain points
3. Use clear, simple language that builds customer confidence
4. Each marketing copy should be in English
"""


prompt_template = Template("""
following is a english description of a product concern point, with a format of json object,
please translate it to Chinese, and return the result in the same format json object, leave the keys unchanged,
but translate the values to Chinese.:

the input is:

```json
{{ concern_point }}
```
""")


llm_conf = llm.LLMConfig('doubao', llm.DOUBAO_DEEPSEEK, 0.6, 10000)
translate_llm_config = llm.LLMConfig("usq", "qwen2.5-32b-chat-fp8-offline", 0, 2000, enable_thinking=False)


async def extract_single_product_concern_points(url: str) -> list[schema.ProductConcernPoint] | None:
    product = await operations.get_product_metadata(url)
    if not product:
        logger.warning(f"No product found for URL: {url}")
        return None
    if not product.product_id:
        logger.error(f"No product ID found for URL: {product.url}")
        return None
    knowledge = await operations.get_product_knowledge(url)
    if not knowledge or not knowledge.documents:
        logger.warning(f"No knowledge found for product URL: {product.url}")
        return None
    product_type = product.product_type
    site = await product.site
    concerns = await operations.list_expanded_product_type_concerns(site.id, product_type)
    if concerns:
        product_type_concerns = [r.name for r in concerns.concern_list]
    else:
        product_type_concerns = []
    serialized_product_type_concerns = json.dumps(product_type_concerns, ensure_ascii=False, indent=2)
    documents = [p for p in knowledge.documents]
    product_content = json.dumps(documents, ensure_ascii=False, indent=2)
    prompt = PROMPT_TEMPLATE.format(
        content=product_content,
        concern_points=serialized_product_type_concerns
    )
    result = await llm.call_llm(llm_conf, prompt)
    if result is None:
        return None
    points = schema.ProductConcernPoint.from_llm_result(result)
    return points


@celery_task
async def extract_and_save_single_product_concern_points(product_url: str, clear_existing: bool = False):
    """
    挖掘单个商品的顾虑点及其营销话术
    """
    existing_concern_points = await operations.get_product_concern_points(product_url)
    if existing_concern_points and not clear_existing:
        logger.info(f"Product concern points already exist for {product_url}, skipping extraction.")
        return
    elif existing_concern_points and clear_existing:
        logger.info(f"Clearing existing selling points for {product_url}.")
        await operations.delete_product_concern_points(product_url)
    concern_points = await extract_single_product_concern_points(product_url)
    if not concern_points:
        return
    await operations.save_product_concern_points(product_url, concern_points)


@celery_task
async def extract_all_product_concern_points(domain: str) -> None:
    """
    挖掘指定站点所有商品的顾虑点及其营销话术（遵循挖掘策略）
    """
    # 根据策略获取需要处理的产品URL列表
    product_urls = await operations.get_products_by_strategy(domain, 'product_concern_point')

    if not product_urls:
        logger.info(f"No products to process for concern points in domain {domain}")
        return

    tasks = [extract_and_save_single_product_concern_points(url) for url in product_urls]
    await CeleryTaskTqdmAsync.gather(*tasks,
                                     desc=f"Extracting concern points for {len(product_urls)} products in {domain}")


@celery_task
async def translate_single_product_concern_points(product_url: str) -> None:
    """翻译单个产品的所有顾虑点"""
    try:
        # 获取产品及其顾虑点
        product = await operations.get_product_metadata(product_url)
        if not product:
            logger.warning(f"Product with URL {product_url} not found")
            return
        concern_points = await models.ProductConcernPoint.filter(product=product)

        for cp in concern_points:
            if cp.name_chn and cp.marketing_copies_chn:
                logger.info(f"Skipping already translated concern point {cp.name} of product {product_url}")
                continue
            # 翻译顾虑点名称
            sp_dict = {
                'name': cp.name,
                'marketing_copies': cp.marketing_copies or []
            }
            prompt = prompt_template.render(concern_point=json.dumps(sp_dict, ensure_ascii=False, indent=2))
            result = await llm.call_llm(translate_llm_config, prompt)
            logger.info(f"Translating concern point {cp.name} of product {product_url} with result: {result}")
            if not result:
                logger.warning(f"No translation result for selling point {cp.name} of product {product_url}")
                continue
            if not isinstance(result, dict):
                logger.warning(f"Unexpected translation result type for selling point {cp.name} of {product_url}")
                continue
            if 'name' in result and isinstance(result['name'], str):
                cp.name_chn = result['name']
            if 'marketing_copies' in result and isinstance(result['marketing_copies'], list):
                cp.marketing_copies_chn = [copy for copy in result['marketing_copies'] if isinstance(copy, str)]
            await cp.save()
    except DoesNotExist:
        logger.warning(f"Product with URL {product_url} not found")
    except Exception as e:
        logger.error(f"Error translating selling points for {product_url}: {str(e)}")


@celery_task
async def translate_all_product_concern_points(domain: str) -> None:
    """翻译指定站点的所有产品顾虑点"""
    try:
        # 获取站点
        site = await models.ShopifySite.get(domain=domain)

        # 获取所有产品
        products = await models.ProductMetadata.filter(site=site)
        translation_tasks = [
            translate_single_product_concern_points(product.url) for product in products
        ]
        await CeleryTaskTqdmAsync.gather(*translation_tasks, desc=f"Translating selling points for {domain}")
    except DoesNotExist:
        logger.warning(f"Site with domain {domain} not found")
    except Exception as e:
        logger.error(f"Error translating concern points for domain {domain}: {str(e)}")


@celery_task
async def sync_product_concerns_to_shopify_knowledge(domain: str):
    """将标注为 ready 状态的产品顾虑点同步到线上知识库"""
    site = await operations.get_site(domain)
    if not site:
        logger.error(f"Site with domain {domain} not found.")
        return
    concerns = await operations.list_concerns_by_status(domain, schema.SellingPointStatus.READY,
                                                        # 只要是标注为 ready 的都同步到 shopify-knowledge，即使爬取策略中没包含对应商品
                                                        should_match_crawl_strategy=False
                                                        )
    if not concerns:
        logger.warning(f"No concerns found for store {domain} with status READY.")
        return

    product_concern_groups = {}
    for concern in concerns:
        product_concern_groups.setdefault(concern.product_id, []).append(concern)
    knowledge_client = shopify_knowledge.ShopifyKnowledgeClient.get_client()
    for product_id, concern_items in CeleryTaskTqdm(
          product_concern_groups.items(), total=len(product_concern_groups), desc=f"Syncing concerns for {domain}"):
        try:
            shopify_concern = shopify_knowledge.ProductConcerns(spuId=product_id, concernList=[])
            for concern in concern_items:
                for copy in (concern.marketing_copies or []):
                    shopify_concern.concernList.append(
                        shopify_knowledge.ProductConcernItem(
                            # 注意: 这里存在一个隐患，aspect 可能在 concernList 中重复出现
                            aspect=concern.concern,
                            marketingCopy=copy
                        ))
            await knowledge_client.sync_shopify_knowledge(site.shopify_domain, shopify_concern)
            logger.info(f"Successfully synced concerns for product {product_id}")
        except Exception as e:
            logger.opt(exception=e).error(f"Failed to sync concern  for product {product_id}: {str(e)}")
