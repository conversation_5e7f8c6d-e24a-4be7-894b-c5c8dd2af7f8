"""
Promotion Knowledge Point Qdrant 索引器
用于处理促销知识点的向量化存储和检索
"""

import asyncio
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from typing_extensions import override
from qdrant_client.models import Distance, VectorParams, SparseVectorParams, MultiVectorConfig, MultiVectorComparator

from .base_qdrant_indexer import BaseQdrantIndexer, IndexConfig


class PromotionKnowledgePoint(BaseModel):
    """促销知识点数据模型"""
    point_id: str
    question: str
    answer: str
    extra_questions: List[str] = []
    labels: List[str] = []
    activity_type: str = "reward-crowdfunding"


class PromotionKnowledgeIndexer(BaseQdrantIndexer[PromotionKnowledgePoint]):
    """促销知识点索引器"""
    
    def __init__(self, config: IndexConfig):
        super().__init__(config)
    
    @property
    @override
    def vectors_config(self) -> Dict[str, Any]:
        """向量配置"""
        return {
            "questions_dense": VectorParams(
                size=self.config.vector_size, 
                distance=Distance.COSINE,
                multivector_config=MultiVectorConfig(comparator=MultiVectorComparator.MAX_SIM),
                on_disk=False
            ),
            "labels_dense": VectorParams(
                size=self.config.vector_size, 
                distance=Distance.COSINE,
                multivector_config=MultiVectorConfig(comparator=MultiVectorComparator.MAX_SIM),
                on_disk=False
            )
        }
    
    @property
    @override
    def sparse_vectors_config(self) -> Dict[str, Any]:
        """稀疏向量配置"""
        return {
            "question-sparse": SparseVectorParams()
        }
    
    @property
    @override
    def payload_indexes(self) -> Dict[str, str]:
        """Payload 索引配置"""
        return {
            "activity_type": "keyword"
        }
    
    @override
    def schema_to_payload(self, schema: PromotionKnowledgePoint) -> Dict[str, Any]:
        """Schema 转 payload"""
        return schema.model_dump()
    
    @override
    def get_id(self, schema: PromotionKnowledgePoint) -> str:
        """获取 ID"""
        return schema.point_id
    
    @override
    async def generate_vectors(self, docs: List[PromotionKnowledgePoint]) -> List[Dict[str, Any]]:
        """生成向量"""
        # 为每个文档生成所有问题的向量（主问题+额外问题）
        all_questions_vectors = []
        all_labels_vectors = []
        
        for doc in docs:
            # 合并主问题和额外问题
            doc_questions = [doc.question] + doc.extra_questions
            
            # 生成问题向量和标签向量
            questions_vectors, labels_vectors = await asyncio.gather(
                self.to_dense_embedding(doc_questions),
                self.to_dense_embedding(doc.labels)
            )
            
            all_questions_vectors.append(questions_vectors)
            all_labels_vectors.append(labels_vectors)
        
        res = []
        for questions_vectors, labels_vectors in zip(all_questions_vectors, all_labels_vectors):
            res.append({
                "questions_dense": questions_vectors,
                "labels_dense": labels_vectors,
            })
        
        return res
    
    async def initialize_collection(self):
        """初始化集合"""
        await self.create_collection()
    
    async def upsert_point(self, point: PromotionKnowledgePoint):
        """插入或更新知识点"""
        # 使用基类的 upsert_point 方法
        await super().upsert_point(point)
    
    async def batch_upsert_points(self, points: List[PromotionKnowledgePoint], batch_size: int = 128, mini_batch_size: int = 16) -> Dict[str, Any]:
        """批量插入知识点"""
        results: Dict[str, Any] = {
            "success_count": 0,
            "failed_count": 0,
            "successes": [],
            "failures": []
        }
        
        # 分批处理
        for i in range(0, len(points), batch_size):
            batch = points[i:i + batch_size]
            
            # 进一步分成小批次进行并发处理
            mini_batches = []
            for j in range(0, len(batch), mini_batch_size):
                mini_batch = batch[j:j + mini_batch_size]
                mini_batches.append(mini_batch)
            
            # 并发处理小批次
            batch_results = await asyncio.gather(
                *[self._process_mini_batch(mini_batch) for mini_batch in mini_batches],
                return_exceptions=True
            )
            
            # 统计结果
            for result in batch_results:
                if isinstance(result, Exception):
                    results["failed_count"] += mini_batch_size
                    results["failures"].append({"error": str(result)})
                elif isinstance(result, dict) and result:
                    results["success_count"] += result.get("success_count", 0)
                    results["failed_count"] += result.get("failed_count", 0)
                    results["successes"].extend(result.get("successes", []))
                    results["failures"].extend(result.get("failures", []))
        
        return results
    
    async def _process_mini_batch(self, points: List[PromotionKnowledgePoint]) -> Dict[str, Any]:
        """处理小批次数据"""
        if not points:
            return {"success_count": 0, "failed_count": 0, "successes": [], "failures": []}
            
        batch_result: Dict[str, Any] = {
            "success_count": 0,
            "failed_count": 0,
            "successes": [],
            "failures": []
        }
        
        try:
            # 生成向量
            vectors = await self.generate_vectors(points)
            
            # 创建 Qdrant points，跳过向量生成失败的点
            qdrant_points = []
            for point, vector in zip(points, vectors):
                if vector and vector.get("questions_dense") and vector.get("labels_dense"):
                    qdrant_points.append({
                        "id": point.point_id,
                        "vector": vector,
                        "payload": self.schema_to_payload(point)
                    })
                    if isinstance(batch_result["successes"], list):
                        batch_result["successes"].append({"point_id": point.point_id, "status": "success"})
                else:
                    if isinstance(batch_result["failures"], list):
                        batch_result["failures"].append({"point_id": point.point_id, "error": "向量生成失败"})
                    if isinstance(batch_result["failed_count"], int):
                        batch_result["failed_count"] += 1
                    continue
            
            if qdrant_points:
                # 批量上传
                await self.qdrant.upsert(
                    collection_name=self.config.collection_name,
                    points=qdrant_points
                )
                batch_result["success_count"] = len(qdrant_points)
            
        except Exception as e:
            # 如果整个批次失败，标记所有点为失败
            batch_result["failed_count"] = len(points)
            batch_result["failures"] = [{"point_id": p.point_id, "error": str(e)} for p in points]
        
        return batch_result
    
    async def get_point(self, point_id: str) -> Optional[Dict[str, Any]]:
        """获取知识点"""
        try:
            result = await self.qdrant.retrieve(
                collection_name=self.config.collection_name,
                ids=[point_id],
                with_payload=True,
                with_vectors=False
            )
            if result:
                return result[0].payload
            return None
        except Exception:
            return None
    
    async def delete_point(self, point_id: str):
        """删除知识点"""
        await self.qdrant.delete(
            collection_name=self.config.collection_name,
            points_selector=[point_id]
        )
    
    async def update_point(self, point_id: str, point: PromotionKnowledgePoint):
        """更新知识点"""
        await self.upsert_point(point)
    
    async def list_points(
        self, 
        store_domain: Optional[str] = None,
        activity_type: Optional[str] = None,
        limit: int = 10, 
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """列出知识点"""
        from qdrant_client.models import Filter, FieldCondition, MatchValue
        
        must_conditions = []
        
        if store_domain:
            must_conditions.append(
                FieldCondition(
                    key="store_domain",
                    match=MatchValue(value=store_domain),
                )
            )
        
        if activity_type:
            must_conditions.append(
                FieldCondition(
                    key="activity_type",
                    match=MatchValue(value=activity_type),
                )
            )
        
        filter_condition = Filter(must=must_conditions) if must_conditions else None  # type: ignore
        
        points = await self.qdrant.scroll(
            collection_name=self.config.collection_name,
            scroll_filter=filter_condition,
            limit=limit,
            offset=offset,
            with_payload=True,
            with_vectors=False,
        )
        
        return [point.payload for point in points[0] if point.payload is not None]
    
    async def search_points(
        self, 
        query: str, 
        store_domain: Optional[str] = None,
        activity_type: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """搜索知识点"""
        from qdrant_client.models import Filter, FieldCondition, MatchValue
        
        # 生成查询向量
        dense_vector = await self.to_dense_embedding([query])
        sparse_vector = await self.to_sparse_embedding([query])
        
        # 构建过滤条件
        must_conditions = []
        
        if store_domain:
            must_conditions.append(
                FieldCondition(
                    key="store_domain",
                    match=MatchValue(value=store_domain),
                )
            )
        
        if activity_type:
            must_conditions.append(
                FieldCondition(
                    key="activity_type",
                    match=MatchValue(value=activity_type),
                )
            )
        
        filter_condition = Filter(must=must_conditions) if must_conditions else None  # type: ignore
        
        # 搜索
        search_result = await self.qdrant.search(
            collection_name=self.config.collection_name,
            query_vector=(
                "questions_dense", 
                dense_vector[0]
            ),
            query_filter=filter_condition,
            limit=limit,
            with_payload=True,
            with_vectors=False,
        )
        
        return [point.payload for point in search_result if point.payload is not None]