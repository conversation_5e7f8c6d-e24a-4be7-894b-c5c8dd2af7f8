from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "product_concern_point" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "name" VARCHAR(255) NOT NULL,
    "name_chn" VARCHAR(255),
    "marketing_copies" JSONB NOT NULL,
    "marketing_copies_chn" JSONB,
    "status" VARCHAR(8) NOT NULL DEFAULT 'fresh',
    "last_edited_at" TIMESTAMPTZ,
    "llm_record_id" INT NOT NULL REFERENCES "llm_record" ("id") ON DELETE CASCADE,
    "product_id" INT NOT NULL REFERENCES "product_metadata" ("id") ON DELETE CASCADE,
    "version_id" INT REFERENCES "data_version" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_con_product_323f54" ON "product_concern_point" ("product_id", "llm_record_id", "status");
COMMENT ON COLUMN "product_concern_point"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_concern_point"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_concern_point"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_concern_point"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_concern_point"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_concern_point"."name" IS '顾虑点名称';
COMMENT ON COLUMN "product_concern_point"."name_chn" IS '顾虑点名称中文翻译';
COMMENT ON COLUMN "product_concern_point"."marketing_copies" IS '营销文案';
COMMENT ON COLUMN "product_concern_point"."marketing_copies_chn" IS '营销文案中文翻译';
COMMENT ON COLUMN "product_concern_point"."status" IS '标注状态';
COMMENT ON COLUMN "product_concern_point"."last_edited_at" IS '上次标注时间';
COMMENT ON COLUMN "product_concern_point"."version_id" IS '当前版本';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "product_concern_point";"""
