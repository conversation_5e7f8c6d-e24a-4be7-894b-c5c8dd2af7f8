import asyncio
from typing import List, Dict, <PERSON>, <PERSON><PERSON>
from typing_extensions import override
from loguru import logger
from tqdm import tqdm
from qdrant_client.models import (
    Distance,
    VectorParams,
    SparseVectorParams
)
from datetime import datetime, timezone
from crawl2.db import operations
from crawl2.db import models
from pydantic import BaseModel
from crawl2.qdrant_index.base_qdrant_indexer import BaseQdrantIndexer, IndexConfig


class CollectionKnowledgePoint(BaseModel):
    point_id: str
    topic: str
    title: str
    content: str
    collection_id: str
    source: str
    source_detail: str
    label: str
    store_domain: str


async def convert_collection_knowledge_to_collection_knowledge_point(
    collection_knowledge_point: models.CollectionKnowledgePoint,
) -> Tuple[str, CollectionKnowledgePoint]:
    site = await collection_knowledge_point.site
    store_domain = site.shopify_domain
    point = CollectionKnowledgePoint(
        point_id=collection_knowledge_point.point_id,
        topic=collection_knowledge_point.topic,
        title=collection_knowledge_point.title,
        content=collection_knowledge_point.content,
        collection_id=collection_knowledge_point.collection_id,
        source=collection_knowledge_point.source,
        source_detail=collection_knowledge_point.source_detail,
        label=collection_knowledge_point.label,
        store_domain=store_domain,
    )
    return store_domain, point


async def load_all_collection_knowledge_points(
    domain: str,
) -> Tuple[str, List[CollectionKnowledgePoint]]:
    collection_knowledge_points = await operations.list_collection_knowledge(domain)
    if not collection_knowledge_points or len(collection_knowledge_points) == 0:
        return None, []
    result = []
    for collection_knowledge_point in collection_knowledge_points:
        store_domain, points = (
            await convert_collection_knowledge_to_collection_knowledge_point(
                collection_knowledge_point
            )
        )
        result.append(points)
    return store_domain, result


class CollectionKnowledgePointIndex(
    BaseQdrantIndexer[CollectionKnowledgePoint]
):
    def __init__(self, config: IndexConfig):
        super().__init__(config)

    @override
    async def generate_vectors(self, docs: List[CollectionKnowledgePoint]) -> List[Dict[str, Any]]:
        title_vectors, content_vectors, title_sparse_vectors, content_sparse_vectors = await asyncio.gather(
            self.to_dense_embedding([doc.title for doc in docs]),
            self.to_dense_embedding([doc.content for doc in docs]),
            self.to_sparse_embedding([doc.title for doc in docs]),
            self.to_sparse_embedding([doc.content for doc in docs]),
        )
        return [
            {
                "title-dense": title_vector,
                "content-dense": content_vector,
                "title-sparse": title_sparse_vector,
                "content-sparse": content_sparse_vector,
            } for title_vector, content_vector, title_sparse_vector, content_sparse_vector in zip(
                title_vectors,
                content_vectors,
                title_sparse_vectors,
                content_sparse_vectors
            )
        ]

    @override
    def schema_to_payload(self, schema: CollectionKnowledgePoint) -> Dict[str, Any]:
        now_iso = datetime.now(timezone.utc).isoformat()
        meta_dict = schema.model_dump()
        meta_dict["create_time"] = now_iso
        meta_dict["update_time"] = now_iso
        return meta_dict

    @override
    def get_id(self, schema: CollectionKnowledgePoint) -> str:
        return schema.point_id

    @property
    @override
    def vectors_config(self) -> Dict[str, Any]:
        return {
            "title-dense": VectorParams(
                size=self.config.vector_size, distance=Distance.COSINE, on_disk=True
            ),
            "content-dense": VectorParams(
                size=self.config.vector_size, distance=Distance.COSINE, on_disk=True
            ),
        }

    @property
    @override
    def sparse_vectors_config(self) -> Dict[str, Any]:
        return {
            "title-sparse": SparseVectorParams(),
            "content-sparse": SparseVectorParams(),
        }

    @property
    @override
    def payload_indexes(self) -> Dict[str, str]:
        return {
            "collection_id": "keyword",
            "store_domain": "keyword",
            "topic": "keyword",
            "label": "keyword",
            "source": "keyword",
            "source_detail": "keyword",
        }

    async def import_all_knowledge_points(
        self,
        domain: str,
        batch_size: int = 10,
        force_recreate_collection: bool = False,
        delete_previous_shop_domain_data: bool = False
    ):
        logger.info(
            f"import domain {domain} collection knowledge points to index for {self.config.collection_name}"
        )
        store_domain, documents = await load_all_collection_knowledge_points(domain)
        if not store_domain or not documents or len(documents) == 0:
            logger.warning(f"No documents found for domain {domain}, skip")
            return
        await self.create_collection(force_recreate=force_recreate_collection)
        source_list = list(set([document.source for document in documents]))
        if delete_previous_shop_domain_data:
            await self.delete_store_domain_knowledge(store_domain, source_list=source_list)

        batches = [
            documents[i: i + batch_size] for i in range(0, len(documents), batch_size)
        ]
        for batch in tqdm(batches, desc="Importing store knowledge points"):
            await self.index_batch(batch)
        await self.check_document_count(store_domain, len(documents))
