import asyncio
from concurrent.futures import Thread<PERSON><PERSON>Executor, wait

from lxml import etree
from tqdm.asyncio import tqdm as tqdm_async
from tqdm import tqdm
from loguru import logger

from crawl2 import utils
from crawl2.clients import cos, llm
from crawl2.db import operations
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync

ALIGN_PROMPT = """
你是一位经验丰富的电商运营专家，给定的商品场景化卖点和商品详情页的具体内容，请你判断该场景化的商品卖点与商品详情页特定区域内容是否是相关的。

只输出 True 或者 False, True 表示相关，False 表示不相关。
不要输出额外的解释。

## 商品详情页特定区域内容
{element_content}

## 场景化卖点
{scenario_selling_point}

## 你判断与场景化卖点相关的商品详情页特定区域内容是否相关
"""


llm_conf = llm.LLMConfig("usq", "qwen2.5-32b-chat-fp8-offline", 0.6, 10)


async def get_html(url: str):
    page_url = "".join(utils.split_domain_and_path(url))
    if not await operations.exists_page(page_url):
        logger.warning(f"Page {page_url} not found in db, skip.")
    cos_client = cos.CosClient.get_client()
    logger.info(f"get page {page_url} html from cos")
    return cos_client.read_page_html(page_url)


def get_page_element_with_content(page_html: str, selectors: list[str]):
    parser = etree.HTMLParser()
    tree = etree.fromstring(page_html, parser)
    content = ""
    for selector in selectors:
        elements = tree.xpath(selector)
        for element in elements:
            content += ' '.join(
                text for text in element.itertext()
                if not any(parent.tag in ['script', 'style'] for parent in element.iterancestors())
            ).strip()
        if content:
            break
    content = content.replace("\n", " ").strip()
    return content


async def call_with_payload(prompt, payload):
    llm_response = await llm.call_llm(llm_conf, prompt, parse_json=False)
    return llm_response, payload


async def align_one_scenario_selling_points(point, product_url, product_elements, cached_element_content):
    tasks = []
    for element in product_elements:
        if element.target not in cached_element_content:
            continue
        element_content = cached_element_content[element.target]
        if not element_content:
            logger.warning(f"element {element.selectors} content is empty, skip.")
            continue
        prompt = ALIGN_PROMPT.format(
            element_content=element_content,
            scenario_selling_point=point.scenario + " " + point.content
        )
        tasks.append(
            call_with_payload(
                prompt,
                {
                    "element": element,
                    "prompt": prompt,
                    "element_content": element_content
                }
            )
        )
    related_elements = []
    for task in tqdm_async(asyncio.as_completed(tasks), total=len(tasks), desc="Generate Scenario Selling Point Align"):
        content, payload = await task
        logger.info("get content: {} product_url: {} element: {} prompt: {}",
                    content,
                    product_url,
                    f"{payload['element'].target}:{payload['element'].selectors}",
                    payload["prompt"])
        if content and "True" in content:
            related_elements.append(payload["element"])
    if related_elements:
        logger.info("related elements: {}", related_elements)
        await point.elements.clear()
        await point.elements.add(*related_elements)
        await point.save()

def process_element(element, page_html, cached_element_content):
    """Function to process a single element and store its content."""
    element_content = get_page_element_with_content(page_html, element.selectors)
    cached_element_content[element.target] = element_content


async def get_cached_element_content(product):
    product_url = "".join(utils.split_domain_and_path(product.url))
    product_elements = await operations.list_raw_page_elements(product_url)
    logger.info("product_url: {} product_elements: {}", product_url, product_elements)
    page_html = await get_html(product.url)
    cached_element_content = {}
    if not page_html:
        logger.warning(f"Page html {product.url} not found in OSS, skip.")
        return cached_element_content, product_elements
    logger.info("get page {} html: {}", product.url, page_html[:100] + "...")
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [
            executor.submit(process_element, element, page_html, cached_element_content)
            for element in product_elements
        ]
        wait(futures)

    return cached_element_content, product_elements


@celery_task
async def align_scenario_selling_points(domain: str):
    """将站点所有挖掘到的场景化卖点和页面的element进行对齐."""
    all_products = await operations.list_product_metadata(domain)
    tasks = []
    for product in tqdm(all_products):
        cached_element_content, product_elements = await get_cached_element_content(product)
        logger.info("cached_element_content: {}", cached_element_content)
        points = await product.scenario_selling_points.all()
        for point in points:
            tasks.append(align_one_scenario_selling_points(
                point, product.url, product_elements, cached_element_content)
            )
    
    await CeleryTaskTqdmAsync.gather(*tasks, desc="Align Scenario Selling Points")


@celery_task
async def align_single_product_scenario_selling_point(product_url: str):
    """对单个产品的场景化卖点和页面的element进行对齐."""
    product = await operations.get_product_metadata(product_url)
    if not product:
        logger.error(f"Product {product_url} not found in db, skip.")
        return
    cached_element_content, product_elements = await get_cached_element_content(product)
    points = await product.scenario_selling_points.all()
    tasks = []
    for point in points:
        tasks.append(align_one_scenario_selling_points(
            point, product.url, product_elements, cached_element_content)
        )

    await CeleryTaskTqdmAsync.gather(*tasks, desc="Align Single Product Scenario Selling Points")
