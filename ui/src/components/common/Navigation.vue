<script setup>
import LoginButton from './LoginButton.vue'

const props = defineProps({
  breadcrumbs: {
    type: Array,
    required: true,
  },
  showBackButton: {
    type: Boolean,
    default: true
  },
  backButtonText: {
    type: String,
    default: 'Back to Home'
  }
})
</script>

<template>
  <div class="top-0 left-0 right-0 z-50 flex justify-between items-center py-3 px-8 bg-white shadow">
    <div class="flex items-center gap-2 text-gray-600">
      <template v-for="(crumb, index) in breadcrumbs" :key="index">
        <router-link 
          v-if="crumb.path && index !== breadcrumbs.length - 1" 
          :to="crumb.path"
          class="text-gray-800 no-underline transition-colors duration-200 hover:text-green-500"
        >
          {{ crumb.text }}
        </router-link>
        <span v-else class="text-green-500 font-medium">{{ crumb.text }}</span>
        <span v-if="index < breadcrumbs.length - 1" class="text-gray-200">/</span>
      </template>
    </div>
    <div class="flex items-center gap-4">
      <!-- 任务和工作流导航 -->
      <div class="flex items-center gap-2">
        <router-link 
          to="/task-definitions" 
          class="text-gray-600 hover:text-green-500 text-sm font-medium transition-colors duration-200"
          title="任务定义"
        >
          任务定义
        </router-link>
        <span class="text-gray-300">|</span>
        <router-link 
          to="/task-runs" 
          class="text-gray-600 hover:text-green-500 text-sm font-medium transition-colors duration-200"
          title="任务历史"
        >
          任务历史
        </router-link>
        <span class="text-gray-300">|</span>
        <router-link 
          to="/workflow-definitions" 
          class="text-gray-600 hover:text-green-500 text-sm font-medium transition-colors duration-200"
          title="工作流定义"
        >
          工作流定义
        </router-link>
        <span class="text-gray-300">|</span>
        <router-link 
          to="/workflow-history" 
          class="text-gray-600 hover:text-green-500 text-sm font-medium transition-colors duration-200"
          title="工作流历史"
        >
          工作流历史
        </router-link>
      </div>
      <slot name="right"></slot>
      <LoginButton />
    </div>
  </div>
</template>
