def create_crawl_shopify_product_page_flow():
    from pocketflow import AsyncFlow
    from crawl2.tasks.page_element import nodes

    normalize_page_url = nodes.NormalizePageUrl()
    prepare_inject_js = nodes.LoadInjectJsCode()
    crawl_product_metadata = nodes.CrawlShopifyProductMetadata()
    crawl_product_web_page = nodes.CrawlShopifyProductWebPage()
    end = nodes.End()

    normalize_page_url >> prepare_inject_js
    prepare_inject_js >> crawl_product_metadata
    crawl_product_metadata >> crawl_product_web_page
    crawl_product_web_page >> end

    class CrawlShopifyProductPage(AsyncFlow): pass

    flow = CrawlShopifyProductPage(start=normalize_page_url)
    return flow


if __name__ == "__main__":
    from pathlib import Path
    from crawl2.tasks.page_element.visualize import build_mermaid

    mermaid_file = Path(__file__).parent / "mermaid.md"
    with open(mermaid_file, "w") as f:
        f.write(build_mermaid(create_crawl_shopify_product_page_flow()))
