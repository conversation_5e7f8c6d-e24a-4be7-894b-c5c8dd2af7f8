from crawl2.qdrant_index.store_knowledge_point_index import (
    StoreKnowledgePointIndex,
)
from crawl2.qdrant_index.base_qdrant_indexer import IndexConfig


_store_knowledge_point_index_instance = None


def get_store_knowledge_point_index():
    global _store_knowledge_point_index_instance
    if _store_knowledge_point_index_instance is None:
        # TODO: 从配置文件中读取
        config = IndexConfig(
            collection_name="store_knowledge_point_test",  # 可根据实际需要调整
            embedding_client_url="http://*************:8002/embed",
            sparse_embedding_client_url="http://*************:8002/sparse_embed",
            qdrant_host="localhost",
            qdrant_port=6333,
            vector_size=1024,
        )
        _store_knowledge_point_index_instance = StoreKnowledgePointIndex(config)
    return _store_knowledge_point_index_instance
