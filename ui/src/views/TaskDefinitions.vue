<script setup>
import { ref, onMounted, computed } from 'vue'
import Navigation from '../components/common/Navigation.vue'
import TaskDefinitionCard from '../components/task/TaskDefinitionCard.vue'
import { useTasks } from '@/composables/useTasks.js'
import { Search, ArrowUpDown, TrendingUp, AlertTriangle, CheckCircle, Clock, Play } from 'lucide-vue-next'

const searchKeyword = ref('')
const sortBy = ref('total_runs') // 默认按总执行次数排序
const sortOrder = ref('desc') // 默认降序

// 使用tasks composable
const { 
  fullTaskData, 
  loading, 
  error, 
  fetchFullTaskData,
  statsLoading
} = useTasks()

// 排序选项
const sortOptions = [
  { key: 'total_runs', label: '执行总次数', icon: TrendingUp },
  { key: 'failed_count', label: '失败总次数', icon: AlertTriangle },
  { key: 'success_count', label: '成功总次数', icon: CheckCircle },
  { key: 'running_count', label: '运行中次数', icon: Play },
  { key: 'last_run_time', label: '最近执行时间', icon: Clock }
]

const filteredAndSortedTaskDefinitions = computed(() => {
  // 确保 fullTaskData 是数组且有数据
  if (!Array.isArray(fullTaskData.value) || fullTaskData.value.length === 0) {
    return []
  }
  
  let filtered = fullTaskData.value
  
  // 搜索过滤
  if (searchKeyword.value.trim()) {
    const keywords = searchKeyword.value.toLowerCase().trim().split(/\s+/).filter(k => k.length > 0)
    filtered = filtered.filter(task => {
      // 确保 task 对象存在且有必要的属性
      if (!task || typeof task !== 'object') {
        return false
      }
      
      const taskName = task.name?.toLowerCase() || ''
      const taskDescription = task.description?.toLowerCase() || ''
      const firstAuthor = task.authors?.[0]?.toLowerCase() || ''
      
      return keywords.every(keyword => 
        taskName.includes(keyword) || 
        taskDescription.includes(keyword) ||
        firstAuthor.includes(keyword)
      )
    })
  }
  
  // 排序
  filtered.sort((a, b) => {
    let aValue, bValue
    
    if (sortBy.value === 'last_run_time') {
      aValue = a.stats?.last_run_time ? new Date(a.stats.last_run_time).getTime() : 0
      bValue = b.stats?.last_run_time ? new Date(b.stats.last_run_time).getTime() : 0
    } else {
      aValue = a.stats?.[sortBy.value] || 0
      bValue = b.stats?.[sortBy.value] || 0
    }
    
    if (sortOrder.value === 'asc') {
      return aValue - bValue
    } else {
      return bValue - aValue
    }
  })
  
  return filtered
})

const handleSort = (key) => {
  if (sortBy.value === key) {
    // 如果点击的是当前排序字段，切换排序顺序
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    // 如果是新的排序字段，设置为降序
    sortBy.value = key
    sortOrder.value = 'desc'
  }
}

const getSortIcon = (key) => {
  if (sortBy.value !== key) {
    return ArrowUpDown
  }
  return sortOrder.value === 'asc' ? '↑' : '↓'
}

onMounted(() => {
  fetchFullTaskData()
})
</script>

<template>
  <div class="flex flex-col h-screen w-full pt-0">
    <Navigation
        :breadcrumbs="[
        { text: 'Sites', path: '/' },
        { text: 'Task Definitions' }
      ]"
    />

    <div class="flex justify-between items-center p-3 px-6 w-full bg-white shadow sticky top-16 z-10">
      <div class="relative w-[350px]">
        <input
          v-model="searchKeyword"
          type="text"
          placeholder="Search by task name, description, or first author..."
          class="w-full p-2 px-8 border border-gray-300 rounded-lg text-sm bg-gray-50 transition-all duration-200 focus:outline-none focus:border-blue-500 focus:bg-white focus:shadow-md"
        />
        <div class="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none">
          <Search size="14" />
        </div>
      </div>
      <router-link 
        to="/task-runs" 
        class="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors"
      >
        查看任务运行历史
      </router-link>
    </div>

    <div class="flex-1 p-3 px-6 flex flex-col w-full bg-gray-50">
      <div v-if="loading" class="flex justify-center items-center h-full text-gray-600 bg-white rounded-lg shadow">
        <div class="text-center">
          <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500 mx-auto mb-3"></div>
          <p class="text-sm">Loading task definitions...</p>
        </div>
      </div>
      
      <div v-else-if="error" class="flex justify-center items-center h-full text-red-500 bg-white rounded-lg shadow">
        <div class="text-center">
          <div class="text-red-500 text-5xl mb-3">⚠️</div>
          <p class="text-base font-medium">Error: {{ error }}</p>
        </div>
      </div>
      
      <div v-else-if="filteredAndSortedTaskDefinitions.length" class="space-y-4">
        <!-- 搜索结果统计和排序 -->
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-600">
            找到 {{ filteredAndSortedTaskDefinitions.length }} 个任务定义
            <span v-if="searchKeyword.trim()" class="text-gray-500">
              (搜索: "{{ searchKeyword }}" - 匹配任务名称、描述或第一个作者)
            </span>
            <span v-if="statsLoading" class="ml-2 inline-flex items-center text-blue-600">
              <div class="animate-spin rounded-full h-3 w-3 border-b border-blue-600 mr-1"></div>
              统计数据加载中...
            </span>
          </div>
          <div class="text-xs text-gray-400">
            点击卡片上的按钮来触发任务
          </div>
        </div>
        
        <!-- 排序选项 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
          <div class="flex items-center space-x-3">
            <span class="text-sm font-medium text-gray-700">排序方式:</span>
            <div class="flex space-x-1.5">
              <button
                v-for="option in sortOptions"
                :key="option.key"
                @click="handleSort(option.key)"
                :class="[
                  'flex items-center space-x-1.5 px-2.5 py-1.5 rounded-md text-xs font-medium transition-colors',
                  sortBy.value === option.key
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                <component :is="option.icon" size="14" />
                <span>{{ option.label }}</span>
                <span v-if="sortBy.value === option.key" class="text-blue-600 font-bold">
                  {{ sortOrder.value === 'asc' ? '↑' : '↓' }}
                </span>
              </button>
            </div>
          </div>
        </div>
        
        <!-- 任务定义卡片网格 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4">
          <TaskDefinitionCard 
            v-for="task in filteredAndSortedTaskDefinitions" 
            :key="task.name" 
            :task="task" 
          />
        </div>
      </div>
      
      <div v-else class="flex justify-center items-center h-full text-gray-600 bg-white rounded-lg shadow">
        <div class="text-center">
          <div class="text-gray-400 text-5xl mb-3">🔍</div>
          <p class="text-base font-medium">
            {{ searchKeyword.trim() ? '没有找到匹配的任务' : 'No task definitions found.' }}
          </p>
          <p v-if="searchKeyword.trim()" class="text-sm text-gray-500 mt-1.5">
            尝试使用不同的关键词搜索
          </p>
        </div>
      </div>
    </div>
  </div>
</template> 