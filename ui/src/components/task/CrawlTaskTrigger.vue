<template>
  <div style="display: inline-block">
    <button class="bg-green-500 text-white border-none rounded px-4 py-2 cursor-pointer text-sm mx-2 hover:bg-green-600" @click="openDialog">{{ displayName }}</button>
    <teleport to="body">
      <div v-if="dialogVisible" class="fixed inset-0 bg-black bg-opacity-25 z-[2000] flex items-center justify-center">
        <div class="bg-white rounded-lg min-w-[340px] max-w-[90vw] shadow-lg p-0 overflow-hidden">
          <div class="flex justify-between items-center bg-gray-50 p-4 px-6 font-semibold text-lg">
            <span>触发任务：{{ taskName}}</span>
            <button class="bg-transparent border-none text-2xl cursor-pointer text-gray-500" @click="closeDialog">×</button>
          </div>
          <div class="p-2 px-6 text-gray-600 text-sm border-b border-gray-100 bg-gray-50" v-if="description">
            {{ description }}
          </div>
          <div class="p-6">
            <form v-if="paramFields.length" @submit.prevent="submitTask">
              <div v-for="field in paramFields" :key="field.name" class="mb-5 flex items-center gap-3">
                <!-- boolean: label 包住 checkbox 和文字 -->
                <label v-if="field.type === 'boolean'" :for="field.name" class="flex items-center gap-2">
                  {{ field.name }}
                  <input
                    type="checkbox"
                    v-model="formData[field.name]"
                    :id="field.name"
                    class="mr-2"
                  />
                </label>
                <!-- 其他类型 label -->
                <label v-else :for="field.name" class="text-sm mb-0 min-w-[120px]">{{ field.name }}</label>
                <!-- select (enum) -->
                <select
                  v-if="field.enum"
                  v-model="formData[field.name]"
                  :id="field.name"
                  :required="field.required"
                  class="p-2 border border-gray-300 rounded text-base"
                >
                  <option v-for="option in field.enum" :key="option" :value="option">{{ option }}</option>
                </select>
                <!-- textarea -->
                <textarea
                  v-else-if="field.format === 'textarea'"
                  v-model="formData[field.name]"
                  :id="field.name"
                  :placeholder="field.name"
                  :required="field.required"
                  class="p-2 border border-gray-300 rounded text-base"
                />
                <!-- number/integer -->
                <input
                  v-else-if="field.type === 'number' || field.type === 'integer'"
                  v-model.number="formData[field.name]"
                  type="number"
                  :id="field.name"
                  :placeholder="field.name"
                  :required="field.required"
                  class="p-2 border border-gray-300 rounded text-base"
                />
                <!-- default: string -->
                <input
                  v-else-if="field.type !== 'boolean'"
                  v-model="formData[field.name]"
                  type="text"
                  :id="field.name"
                  :placeholder="field.name"
                  :required="field.required"
                  class="p-2 border border-gray-300 rounded text-base"
                />
              </div>
              <div class="flex gap-4 mt-6">
                <button type="submit" class="px-5 py-2 border-none rounded bg-green-500 text-white text-base cursor-pointer disabled:bg-green-300 disabled:cursor-not-allowed" :disabled="loading">提交</button>
                <button type="button" class="px-5 py-2 border-none rounded bg-gray-200 text-gray-800 text-base cursor-pointer" @click="closeDialog">取消</button>
              </div>
            </form>
            <div v-else-if="loading" class="text-gray-600">加载参数...</div>
            <div v-else class="text-red-500">未找到参数定义</div>
            <div v-if="error" class="text-red-500 mt-4">{{ error }}</div>
            <div v-if="success" class="text-green-600 mt-4">
              任务已触发！
              <a 
                v-if="taskResponse && taskResponse.task_id && taskResponse.log_url" 
                :href="taskResponse.log_url"
                target="_blank"
                class="text-blue-600 no-underline ml-2 font-medium hover:underline"
              >
                点击查看日志
              </a>
              <a 
                href="/#/task-runs"
                class="text-blue-600 no-underline ml-2 font-medium hover:underline"
              >
                查看进度
              </a>
            </div>
          </div>
        </div>
      </div>
    </teleport>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { api } from '@/api/api.js'
import { useTasks } from '@/composables/useTasks.js'

const props = defineProps({
  taskName: { type: String, required: true },
  displayName: { type: String, required: true },
  defaultParams: { type: Object, default: () => ({}) }
})

const dialogVisible = ref(false)
const paramFields = ref([])
const formData = ref({})
const loading = ref(false)
const error = ref('')
const success = ref(false)
const description = ref('')
const taskResponse = ref(null) // 统一保存任务响应对象

// 使用tasks composable
const { getTaskDefinition, fetchTaskDefinitions } = useTasks()

const openDialog = async () => {
  dialogVisible.value = true
  error.value = ''
  success.value = false
  loading.value = true
  formData.value = {}
  paramFields.value = []
  description.value = ''
  taskResponse.value = null // 重置任务响应对象
  
  try {
    // 确保有task definitions数据
    await fetchTaskDefinitions()
    
    const def = getTaskDefinition(props.taskName)
    if (def) {
      description.value = def.description || ''
    }
    if (def && def.params && def.params.properties) {
      paramFields.value = Object.entries(def.params.properties).map(([name, schema]) => ({
        name,
        type: schema.type || 'string',
        required: (def.params.required || []).includes(name),
        enum: schema.enum,
        format: schema.format,
        default: schema.default
      }))
      // 初始化表单数据，使用服务端默认值或预填参数
      paramFields.value.forEach(f => { 
        // 优先使用预填参数，其次使用服务端默认值（如果存在）
        if (props.defaultParams[f.name] !== undefined) {
          formData.value[f.name] = props.defaultParams[f.name]
        } else if (f.default !== undefined) {
          formData.value[f.name] = f.default
        } else {
          // 如果都没有，根据类型设置合适的初始值
          if (f.type === 'boolean') {
            formData.value[f.name] = false
          } else if (f.type === 'array') {
            formData.value[f.name] = []
          } else {
            formData.value[f.name] = ''
          }
        }
      })
    } else {
      paramFields.value = []
    }
  } catch (e) {
    error.value = '参数定义加载失败'
  } finally {
    loading.value = false
  }
}

const closeDialog = () => {
  dialogVisible.value = false
  error.value = ''
  success.value = false
  taskResponse.value = null // 关闭对话框时重置任务响应对象
}

const submitTask = async () => {
  loading.value = true
  error.value = ''
  success.value = false
  try {
    // 清理提交数据：只移除 undefined，保留所有用户输入的值（包括空字符串）
    const cleanedData = {}
    Object.entries(formData.value).forEach(([key, value]) => {
      if (value !== undefined) {
        cleanedData[key] = value
      }
    })
    
    const response = await api.post(`/api/v1/tasks/trigger/${props.taskName}`, cleanedData)
    success.value = true
    // 保存任务ID用于生成日志链接
    taskResponse.value = response.data
    
    // 任务已成功触发
  } catch (e) {
    error.value = e?.response?.data?.detail || '任务触发失败'
  } finally {
    loading.value = false
  }
}
</script>