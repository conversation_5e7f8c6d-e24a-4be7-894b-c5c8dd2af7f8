import asyncio

from bs4 import BeautifulSoup
from crawl4ai import <PERSON>rawlerRunConfig, CacheMode
from loguru import logger

from crawl2.clients import browser, http, llm
from crawl2.db import operations
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync

llm_config = llm.LLMConfig(service="usq", model="minicpm-o-2.6", temperature=0.0, max_tokens=512)


async def _crawl_single_product_page_markdown(url: str, timeout: int = 60) -> str | None:
    """
    Crawl a single product page and extract the markdown content.

    :param url: a product URL to crawl.
    :return: the crawled product page markdown data or None if an error occurs.
    """
    crawl_config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        remove_overlay_elements=True,
        exclude_external_links=True,
        exclude_social_media_links=True,
        excluded_tags=["nav", "header", "footer"],
        remove_forms=True,
    )
    try:
        result = await browser.crawl_with_timeout(url, config=crawl_config, timeout=timeout)
        if result and hasattr(result, 'success') and result.success:
            return result.markdown if hasattr(result, 'markdown') else None
        else:
            error_msg = result.error_message if result and hasattr(result, 'error_message') else "Unknown error"
            logger.error(f"Crawl page markdown failed for {result.url}: {error_msg}")
            return None
    except asyncio.TimeoutError:
        logger.error(f"Crawl page markdown timeout for {url} after {timeout} seconds")
        raise
    except Exception as e:
        logger.opt(exception=e).error(f"Crawl page markdown failed for {url}: {str(e)}")
        return None


def extract_image_url(html: str) -> str:
    """
    从 HTML 中提取图片 URL
    """
    soup = BeautifulSoup(html, "html.parser")
    img_tags = soup.find_all("img")
    urls = [img["src"] for img in img_tags if "src" in img]
    urls = ["https:" + url for url in urls if url.startswith("//")]
    return urls


async def _crawl_single_product_page_image_ocr(url: str) -> str | None:
    """
    Crawl a single product page and extract the image OCR result.
    """
    response = await http.http_get(url)
    response.encoding = "utf-8"
    html = response.text
    image_urls = extract_image_url(html)
    ocr_results = []
    for image_url in image_urls:
        try:
            ocr_result = await llm.ocr_image_with_llm(llm_config, image_url)
            ocr_results.append(ocr_result)
        except Exception as e:
            logger.opt(exception=e).error(f"ocr_image_for_url failed for {image_url}: {str(e)}")
    return "\n".join(ocr_results)


@celery_task
async def crawl_single_product_page_markdown(
        product_url: str, clear_existing: bool = False, enable_image_ocr: bool = False, timeout: int = 60):
    """
    爬取单个商品页面的原始数据，提取 markdown 内容和图片 OCR 结果
    """
    product_metadata = await operations.get_product_metadata(product_url)
    if not product_metadata:
        logger.warning(f"Product metadata not found for {product_url}, skipping crawl raw page.")
        return
    existed = await operations.get_product_raw_page(product_url)
    if existed and not clear_existing:
        logger.info(f"product raw page already exist for {product_url}, skipping crawl.")
        return
    elif existed and clear_existing:
        logger.info(f"Clearing existing product raw page for {product_url}.")
    markdown = await _crawl_single_product_page_markdown(product_url, timeout=timeout)
    if markdown:
        image_ocr_result = (
            await _crawl_single_product_page_image_ocr(product_url)
            if enable_image_ocr
            else None
        )
        await operations.save_product_raw_page(product_url, markdown, image_ocr_result)


ENABLE_IMAGE_OCR_DOMAINS = {
#    "cyxus.com"
}


@celery_task
async def crawl_all_product_page_markdown(
        domain: str,
        enable_image_ocr: bool = False,
        timeout_per_page: int = 60
):
    """
    爬取指定站点所有商品页面的原始数据，提取 markdown 内容和图片 OCR 结果
    """
    metadata_list = await operations.list_product_metadata(domain)
    enable_image_ocr = enable_image_ocr or domain in ENABLE_IMAGE_OCR_DOMAINS
    tasks = [crawl_single_product_page_markdown(metadata.url, enable_image_ocr=enable_image_ocr,
                                                timeout=timeout_per_page)
             for metadata in metadata_list]
    await CeleryTaskTqdmAsync.gather(*tasks, desc='crawl product raw pages')
