<template>
  <div class="fixed inset-0 bg-transparent flex justify-center items-center z-[1000]" @click.self="$emit('close')">
    <div class="bg-white p-8 rounded-lg shadow-lg w-[95%] max-w-[1200px] max-h-[95vh] flex flex-col">
      <h3 class="mt-0 mb-5 text-lg font-bold flex-shrink-0">Edit Product Concerns</h3>
      <form class="flex flex-col max-h-[85vh]">
        <!-- 上面部分：元信息 + 营销话术 -->
        <div class="flex gap-6 flex-1 min-h-0 mb-6">
          <!-- 左侧：元信息 -->
          <div class="w-[30%] flex flex-col gap-3">
            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="font-medium text-sm mb-1">Product</div>
              <a :href="editableData.product_url" target="_blank" class="text-[0.9rem] text-blue-600 hover:underline leading-relaxed">
                {{ editableData.product_title }}
              </a>
              <div class="text-xs text-gray-500 mt-1">{{ editableData.product_title_chn }}</div>
            </div>
            
            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="font-medium text-sm mb-1">Concern</div>
              <div class="text-[0.9rem] text-[#222] leading-relaxed">{{ editableData.concern }}</div>
              <div class="text-xs text-gray-500 mt-1">{{ editableData.concern_chn }}</div>
            </div>
            
            <!-- 时间信息 -->
            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="font-medium text-sm mb-1">时间信息</div>
              <div class="text-xs text-gray-600 space-y-1">
                <div v-if="editableData.created_at">
                  <span class="font-medium">生成时间:</span> 
                  {{ formatDateTime(editableData.created_at) }}
                </div>
                <div v-if="editableData.last_edited_at">
                  <span class="font-medium">最近标注:</span> 
                  {{ formatDateTime(editableData.last_edited_at) }}
                </div>
                <div v-else class="text-gray-500">
                  <span class="font-medium">最近标注:</span> 尚未标注
                </div>
              </div>
            </div>
          </div>
          
          <!-- 右侧：营销话术区域 -->
          <div class="flex-1 flex flex-col min-h-0">
            <div class="flex items-center justify-between mb-2">
              <label class="font-bold text-base">Marketing Copies</label>
              <div class="text-sm text-gray-600">
                已选择: <span class="font-bold text-green-600">{{ selectedCount }}</span> / 
                总计: <span class="font-bold">{{ totalCount }}</span>
              </div>
            </div>
            
            <div class="flex-1 border border-gray-200 rounded-lg overflow-hidden">
              <div class="h-full overflow-y-auto p-3 bg-gray-50">
                <div v-for="(copy, index) in editableData.marketing_copies" :key="index" 
                     class="mb-3 p-3 bg-white rounded-lg border border-gray-200 hover:border-blue-300 transition-colors">
                  <div class="flex items-start gap-3">
                    <input 
                      type="checkbox" 
                      v-model="marketingCopyEnabled[index]" 
                      class="mt-1 flex-shrink-0"
                      :id="`copy-${index}`"
                    />
                    <div class="flex-1 min-w-0">
                      <label :for="`copy-${index}`" class="cursor-pointer block">
                        <div class="text-[0.97rem] text-[#222] mb-1 leading-relaxed">{{ editableData.marketing_copies_chn[index] }}</div>
                        <div class="text-xs text-gray-500 leading-relaxed">{{ copy }}</div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 下面部分：状态选择 + 操作按钮 -->
        <div class="flex gap-4 pt-3 border-t border-gray-200 flex-shrink-0">
          <!-- 左侧：状态选择 -->
          <div class="flex-1 p-3 bg-gray-50 rounded-lg flex justify-end">
            <div class="flex gap-4">
              <label v-for="option in statusOptions" :key="option.value" class="flex items-center gap-2 text-[0.97rem] cursor-pointer">
                <input type="radio" v-model="editableData.status" :value="option.value" />
                <div>
                  <div class="font-medium" :class="option.color">{{ option.label }}</div>
                  <div class="text-xs text-gray-500">{{ option.label_chn }}</div>
                </div>
              </label>
            </div>
          </div>
          
          <!-- 右侧：操作按钮 -->
          <div class="flex items-center gap-4">
            <button type="button" @click="$emit('close')" class="px-6 py-2 rounded font-bold bg-gray-200 text-gray-800 hover:bg-gray-300">Cancel</button>
            <button type="button" @click="saveAndNext" class="px-6 py-2 rounded font-bold bg-green-500 text-white hover:bg-green-600">Save&Next</button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, computed } from 'vue'

const props = defineProps({
  rowData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close', 'save', 'saveAndNext'])

const statusOptions = [
  { value: 'ready', label: 'Ready', label_chn: '已就绪', color: 'text-green-600' },
  { value: 'editing', label: 'Editing', label_chn: '编辑中', color: 'text-blue-600' },
  { value: 'disabled', label: 'Disabled', label_chn: '已禁用', color: 'text-red-600' },
  { value: 'archived', label: 'Archived', label_chn: '已归档', color: 'text-gray-600' },
]

const editableData = ref({})
const marketingCopyEnabled = ref([])

// 计算属性：统计选中的营销话术数量
const selectedCount = computed(() => {
  return marketingCopyEnabled.value.filter(enabled => enabled).length
})

// 计算属性：总营销话术数量
const totalCount = computed(() => {
  return editableData.value.marketing_copies?.length || 0
})

watch(() => props.rowData, (newData) => {
  const data = { ...newData };
  data.marketing_copies = Array.isArray(data.marketing_copies) 
    ? data.marketing_copies 
    : [];
  data.marketing_copies_chn = Array.isArray(data.marketing_copies_chn)
    ? data.marketing_copies_chn
    : [];
  data.status = data.status && data.status !== 'fresh' ? data.status : 'ready';
  editableData.value = data;
  marketingCopyEnabled.value = data.marketing_copies.map(() => true)
}, { immediate: true, deep: true })

const saveAndNext = () => {
  const enabledIndexes = marketingCopyEnabled.value
    .map((v, i) => v ? i : -1)
    .filter(i => i !== -1)
  const newData = {
    ...editableData.value,
    marketing_copies: enabledIndexes.map(i => editableData.value.marketing_copies[i]),
    marketing_copies_chn: enabledIndexes.map(i => editableData.value.marketing_copies_chn[i])
  }
  emit('saveAndNext', newData)
}

// 时间格式化函数
const formatDateTime = (dateTime) => {
  if (!dateTime) return 'N/A'
  
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    return 'Invalid Date'
  }
}
</script>
