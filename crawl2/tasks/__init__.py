import ast
import importlib
import os
import re
import subprocess
from dataclasses import dataclass
from typing import Tuple
from collections import Counter

from loguru import logger

_cur_dir = os.path.dirname(__file__)


@dataclass
class CeleryTaskMetadata:
    name: str
    module: str
    authors: list[str]


def git_blame_authors(filepath: str) -> list[Tuple[int, str]]:
    try:
        output = subprocess.check_output(['git', 'blame', '-w', '-e', '--line-porcelain', filepath], cwd=_cur_dir)
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to run git blame on {filepath}: {e}")
        return []
    line_authors = []
    line_no = None
    blame_entry_start_pattern = re.compile(r'^[0-9a-f]{40} (\d+) (\d+)( \d+)?$')
    author_mail_pattern = re.compile(r'^author-mail <([^>@]+)@[^>@]+>$')
    for line in output.decode('utf-8').splitlines():
        if line_no is None:
            match = blame_entry_start_pattern.match(line)
            if match:
                line_no = int(match.group(2))
        else:
            match = author_mail_pattern.match(line)
            if match:
                author = match.group(1)
                line_authors.append((line_no, author))
                line_no = None
    return line_authors


def collect_celery_tasks_in_file(filepath) -> list[CeleryTaskMetadata]:
    with open(os.path.join(_cur_dir, filepath), "r", encoding="utf-8") as f:
        source = f.read()

    tree = ast.parse(source, filename=filepath)
    res = []
    line_authors = git_blame_authors(filepath)
    author_counts = Counter(author for _, author in line_authors)
    # 获取作者列表并按出现次数排序
    authors = sorted(list(author_counts.keys()), key=lambda x: author_counts[x], reverse=True)

    rel_path = os.path.relpath(filepath, _cur_dir)
    modul_path = rel_path.replace(os.path.sep, '.').replace('.py', '')
    module = f'crawl2.tasks.{modul_path}'

    for node in ast.walk(tree):
        if isinstance(node, ast.AsyncFunctionDef):
            is_celery_task = False
            for decorator in node.decorator_list:
                # 检查 @celery_task 或 @celery_task(...)
                if isinstance(decorator, ast.Name) and decorator.id == "celery_task":
                    is_celery_task = True
                    break
                elif (isinstance(decorator, ast.Call) and isinstance(decorator.func, ast.Name)
                      and decorator.func.id == "celery_task"):
                    is_celery_task = True
                    break
            if is_celery_task:
                res.append(CeleryTaskMetadata(name=node.name, module=module, authors=authors))
    return res


def collect_celery_tasks():
    tasks = []
    for dirpath, _, files in os.walk(_cur_dir):
        for fname in files:
            if not fname.endswith('.py'):
                continue
            if fname.startswith('_'):
                continue
            tasks.extend(collect_celery_tasks_in_file(os.path.join(dirpath, fname)))
    return tasks


for _task in collect_celery_tasks():
    importlib.import_module(_task.module)


from crawl2.tasks import celery_wrap  # NOQA

for _task in collect_celery_tasks():
    task_obj = celery_wrap.celery_tasks.get(_task.name)
    if task_obj:
        task_obj.authors = _task.authors

from crawl2.tasks import shopify_knowledge_callbacks   # NOQA