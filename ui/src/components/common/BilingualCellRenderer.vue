<template>
  <div class="bilingual-cell-renderer">
    <!-- 多条模式：显示所有中英文内容 -->
    <template v-if="isMultiMode">
      <div v-for="(item, index) in multiContent" :key="index" class="multi-item">
        <!-- 中文内容 -->
        <div v-if="item.chinese" class="chinese-text text-ellipsis overflow-hidden whitespace-nowrap" :title="item.chinese">
          {{ item.chinese }}
        </div>
        <!-- 英文内容 -->
        <div v-if="item.english" class="english-text text-ellipsis overflow-hidden whitespace-nowrap" :title="item.english">
          {{ item.english }}
        </div>
      </div>
    </template>
    
    <!-- 单条模式：显示单条中英文内容 -->
    <template v-else>
      <!-- 中文内容 -->
      <div v-if="chineseContent" class="chinese-text text-ellipsis overflow-hidden whitespace-nowrap" :title="chineseContent">
        {{ chineseContent }}
      </div>
      
      <!-- 英文内容 -->
      <div v-if="englishContent" class="english-text text-ellipsis overflow-hidden whitespace-nowrap" :title="englishContent">
        {{ englishContent }}
      </div>
    </template>
    
    <!-- 无内容提示 -->
    <div v-if="!hasContent" class="no-content">
      无内容
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // AG Grid 传递的参数对象
  params: {
    type: Object,
    required: true
  }
})

// 从 cellRendererParams 中获取配置
const config = computed(() => {
  return props.params.colDef?.cellRendererParams || {}
})

// 判断是否为多条模式
const isMultiMode = computed(() => {
  return config.value.mode === 'multiple'
})

// 多条模式的内容处理
const multiContent = computed(() => {
  if (!isMultiMode.value) return []
  
  const { data } = props.params
  const { chineseField, englishField } = config.value
  
  const chineseArray = data[chineseField] || []
  const englishArray = data[englishField] || []
  
  // 取两个数组的最大长度
  const maxLength = Math.max(chineseArray.length, englishArray.length)
  
  const result = []
  for (let i = 0; i < maxLength; i++) {
    result.push({
      chinese: chineseArray[i] || '',
      english: englishArray[i] || ''
    })
  }
  
  return result
})

// 单条模式的内容
const chineseContent = computed(() => {
  if (isMultiMode.value) return ''
  
  const { data } = props.params
  const { chineseField } = config.value
  
  return data[chineseField] || ''
})

const englishContent = computed(() => {
  if (isMultiMode.value) return ''
  
  const { data } = props.params
  const { englishField } = config.value
  
  return data[englishField] || ''
})

// 判断是否有内容
const hasContent = computed(() => {
  if (isMultiMode.value) {
    return multiContent.value.length > 0 && multiContent.value.some(item => item.chinese || item.english)
  }
  return chineseContent.value || englishContent.value
})
</script>

<style scoped>
.bilingual-cell-renderer {
  padding: 1px 0;
  line-height: 1.1;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.chinese-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0px; /* 完全移除中英文之间的间距 */
  line-height: 1.1;
}

.english-text {
  font-size: 0.6875rem; /* 比中文更小的字体 */
  color: #6b7280;
  line-height: 1.0;
}

.no-content {
  font-size: 0.6875rem;
  color: #9ca3af;
  font-style: italic;
}

/* 多条模式的样式 */
.multi-item {
  margin-bottom: 4px;
  padding: 3px 5px;
  background-color: #f9fafb;
  border-radius: 3px;
  border-left: 2px solid #e5e7eb;
  flex-shrink: 0;
}

.multi-item:last-child {
  margin-bottom: 0;
}

.multi-item .chinese-text {
  margin-bottom: 0px; /* 移除中英文之间的间距 */
}

.multi-item .english-text {
  margin-bottom: 0;
}

/* 确保在 AG Grid 中正确显示 */
:deep(.ag-cell-value) {
  line-height: inherit;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
