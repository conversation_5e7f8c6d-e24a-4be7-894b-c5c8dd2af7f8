#!/usr/bin/env python3
"""
Promotion Knowledge CRUD 脚本
用于管理 Promotion 知识点的增删改查操作
直接操作 Qdrant 向量数据库
"""

import json
import argparse
import sys
import os
import asyncio
from typing import List, Dict, Any, Optional
from uuid import uuid4
from datetime import datetime, timezone
from loguru import logger

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from crawl2.qdrant_index.base_qdrant_indexer import IndexConfig
from crawl2.qdrant_index.promotion_knowledge_point_index import PromotionKnowledgeIndexer, PromotionKnowledgePoint
from crawl2.config import settings


class PromotionKnowledgeCRUD:
    """促销知识点 CRUD 操作类"""
    
    def __init__(self, collection_name: str = "promotion_knowledge_points"):
        """初始化 CRUD 操作类"""
        self.config = IndexConfig(
            collection_name=collection_name,
            embedding_client_url=settings.EMBEDDING_API_URL,
            sparse_embedding_client_url=settings.SPARSE_EMBEDDING_API_URL,
            qdrant_url=settings.QDRANT_URL,
            vector_size=settings.QDRANT_VECTOR_SIZE,
        )
        self.indexer = PromotionKnowledgeIndexer(self.config)
    
    async def initialize(self):
        """初始化索引器"""
        try:
            await self.indexer.initialize_collection()
        except Exception as e:
            logger.info(f"Collection 可能已经存在: {e}")
    
    async def create_point(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建知识点"""
        try:
            # 生成 point_id 如果没有提供（兼容原始文件的 UUID 生成方式）
            if 'point_id' not in data or not data['point_id']:
                data['point_id'] = str(uuid4())
            
            # 确保必要字段存在
            if 'question' not in data or 'answer' not in data:
                return {
                    "success": False,
                    "error": "question 和 answer 字段是必需的"
                }
            
            # 设置默认值（保持与原始文件的兼容性）
            data.setdefault('extra_questions', [])
            data.setdefault('labels', [])
            data.setdefault('activity_type', 'reward-crowdfunding')
            
            point = PromotionKnowledgePoint(**data)
            await self.indexer.upsert_point(point)
            
            return {
                "success": True,
                "point_id": point.point_id,
                "message": "知识点创建成功"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "知识点创建失败"
            }
    
    async def get_point(self, point_id: str) -> Dict[str, Any]:
        """获取知识点"""
        try:
            point = await self.indexer.get_point(point_id)
            if point:
                return {
                    "success": True,
                    "data": point
                }
            else:
                return {
                    "success": False,
                    "error": "知识点不存在"
                }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "获取知识点失败"
            }
    
    async def update_point(self, point_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """更新知识点"""
        try:
            # 确保点存在
            existing_point = await self.indexer.get_point(point_id)
            if not existing_point:
                return {
                    "success": False,
                    "error": "知识点不存在"
                }
            
            # 更新数据
            data['point_id'] = point_id
            data['update_time'] = datetime.now(timezone.utc).isoformat()
            
            point = PromotionKnowledgePoint(**data)
            await self.indexer.update_point(point_id, point)
            
            return {
                "success": True,
                "point_id": point_id,
                "message": "知识点更新成功"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "知识点更新失败"
            }
    
    async def delete_point(self, point_id: str) -> Dict[str, Any]:
        """删除知识点"""
        try:
            await self.indexer.delete_point(point_id)
            return {
                "success": True,
                "message": "知识点删除成功"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "知识点删除失败"
            }
    
    async def list_points(
        self, 
        store_domain: Optional[str] = None,
        activity_type: Optional[str] = None,
        limit: int = 10, 
        offset: int = 0
    ) -> Dict[str, Any]:
        """列出知识点"""
        try:
            points = await self.indexer.list_points(
                store_domain=store_domain,
                activity_type=activity_type,
                limit=limit,
                offset=offset
            )
            
            return {
                "success": True,
                "data": points,
                "total": len(points)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "列出知识点失败"
            }
    
    async def search_points(
        self, 
        query: str, 
        store_domain: Optional[str] = None,
        activity_type: Optional[str] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """搜索知识点"""
        try:
            points = await self.indexer.search_points(
                query=query,
                store_domain=store_domain,
                activity_type=activity_type,
                limit=limit
            )
            
            return {
                "success": True,
                "data": points,
                "total": len(points)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "搜索知识点失败"
            }
    
    async def batch_create_points(self, data_list: List[Dict[str, Any]], batch_size: int = 128) -> Dict[str, Any]:
        """批量创建知识点"""
        try:
            # 转换数据格式
            points = []
            for data in data_list:
                # 生成 point_id 如果没有提供（兼容原始文件的 UUID 生成方式）
                if 'point_id' not in data or not data['point_id']:
                    data['point_id'] = str(uuid4())
                
                # 确保必要字段存在
                if 'question' not in data or 'answer' not in data:
                    continue
                
                # 设置默认值（保持与原始文件的兼容性）
                data.setdefault('extra_questions', [])
                data.setdefault('labels', [])
                data.setdefault('activity_type', 'reward-crowdfunding')
                
                points.append(PromotionKnowledgePoint(**data))
            
            # 使用批量上传方法
            result = await self.indexer.batch_upsert_points(points, batch_size)
            
            return {
                "success_count": result["success_count"],
                "failed_count": result["failed_count"],
                "message": f"批量创建完成，成功: {result['success_count']}, 失败: {result['failed_count']}"
            }
            
        except Exception as e:
            return {
                "success_count": 0,
                "failed_count": len(data_list),
                "error": str(e),
                "message": "批量创建失败"
            }
    
    async def batch_create_from_file(self, file_path: str, batch_size: int = 128) -> Dict[str, Any]:
        """从文件批量创建知识点"""
        try:
            data = load_json_data(file_path)
            if data is None:
                return {
                    "success_count": 0,
                    "failed_count": 0,
                    "error": "无法加载文件",
                    "message": "文件加载失败"
                }
            
            if isinstance(data, list):
                return await self.batch_create_points(data, batch_size)
            else:
                # 单个数据点
                result = await self.create_point(data)
                return {
                    "success_count": 1 if result["success"] else 0,
                    "failed_count": 0 if result["success"] else 1,
                    "message": result["message"]
                }
                
        except Exception as e:
            return {
                "success_count": 0,
                "failed_count": 0,
                "error": str(e),
                "message": "从文件创建失败"
            }


def load_json_data(file_path: str) -> Any:
    """从文件加载 JSON 数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading JSON file {file_path}: {e}")
        return None


def format_output(data: Any) -> str:
    """格式化输出数据"""
    if isinstance(data, (dict, list)):
        return json.dumps(data, ensure_ascii=False, indent=2)
    return str(data)


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Promotion Knowledge CRUD 工具")
    parser.add_argument(
        "--collection", default="zgrill-promotions-v0627",
        help="集合名称 (默认: zgrill-promotions-v0627)"
    )

    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 创建命令
    create_parser = subparsers.add_parser("create", help="创建知识点")
    create_group = create_parser.add_mutually_exclusive_group(required=True)
    create_group.add_argument("--file", help="从 JSON 文件创建")
    create_group.add_argument("--data", help="从 JSON 字符串创建")

    # 获取命令
    get_parser = subparsers.add_parser("get", help="获取知识点")
    get_parser.add_argument("point_id", help="知识点 ID")

    # 更新命令
    update_parser = subparsers.add_parser("update", help="更新知识点")
    update_parser.add_argument("point_id", help="知识点 ID")
    update_parser.add_argument("--data", required=True, help="更新数据 (JSON 字符串)")

    # 删除命令
    delete_parser = subparsers.add_parser("delete", help="删除知识点")
    delete_parser.add_argument("point_id", help="知识点 ID")

    # 列表命令
    list_parser = subparsers.add_parser("list", help="列出知识点")
    list_parser.add_argument("--activity-type", help="按活动类型筛选")
    list_parser.add_argument("--limit", type=int, default=10, help="限制数量")
    list_parser.add_argument("--offset", type=int, default=0, help="偏移量")

    # 搜索命令
    search_parser = subparsers.add_parser("search", help="搜索知识点")
    search_parser.add_argument("query", help="搜索查询")
    search_parser.add_argument("--activity-type", help="按活动类型筛选")
    search_parser.add_argument("--limit", type=int, default=10, help="限制数量")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # 创建 CRUD 实例
    crud = PromotionKnowledgeCRUD(collection_name=args.collection)
    
    try:
        # 初始化
        await crud.initialize()
        
        if args.command == "create":
            if args.file:
                data = load_json_data(args.file)
                if data is None:
                    sys.exit(1)
            else:
                data = json.loads(args.data)

            if isinstance(data, list):
                result = await crud.batch_create_points(data)
            else:
                result = await crud.create_point(data)

            print(format_output(result))

        elif args.command == "get":
            result = await crud.get_point(args.point_id)
            print(format_output(result))

        elif args.command == "update":
            data = json.loads(args.data)
            result = await crud.update_point(args.point_id, data)
            print(format_output(result))

        elif args.command == "delete":
            result = await crud.delete_point(args.point_id)
            print(format_output(result))

        elif args.command == "list":
            result = await crud.list_points(
                activity_type=args.activity_type,
                limit=args.limit,
                offset=args.offset
            )
            print(format_output(result))

        elif args.command == "search":
            result = await crud.search_points(
                query=args.query,
                activity_type=args.activity_type,
                limit=args.limit
            )
            print(format_output(result))

    except json.JSONDecodeError as e:
        print(f"JSON 解析错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())