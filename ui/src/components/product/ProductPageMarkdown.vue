<script setup>
import CrawlTaskTrigger from '../task/CrawlTaskTrigger.vue'

const props = defineProps({
  markdown: {
    type: String,
    required: true
  },
  productUrl: {
    type: String,
    required: true
  }
})
</script>

<template>
  <div class="bg-white rounded-lg p-6 shadow">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-gray-800 text-xl m-0">Page Markdown</h3>
      <CrawlTaskTrigger
        taskName="crawl_single_product_page_markdown"
        displayName="Re-crawl Markdown"
        :defaultParams="{ product_url: productUrl, clear_existing: true, enable_image_ocr: true }"
      />
    </div>
    <pre class="bg-gray-50 p-4 rounded-md overflow-x-auto whitespace-pre-wrap break-words">{{ markdown }}</pre>
  </div>
</template> 