from loguru import logger

from crawl2 import schema
from crawl2.clients import llm
from crawl2.db import operations
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync

CONCERN_PROMPT_TEMPLATE = """
You are a senior product user experience expert. First, analyze the following product titles to determine the most appropriate product type. Then, based on your expertise, summarize the key concerns that might prevent users from purchasing products in this category.

The following are some product titles to help you determine the product type:
{item_title_list}

Please follow these steps:
1. Determine the most appropriate product type based on the given titles
2. Think about what aspects of this product type might make users hesitant to purchase
3. Generate at least 15 key concerns that commonly affect purchase decisions
4. For each concern, provide a brief explanation of why it matters

Output your result in the following format:
{{
  "product_type": "determined product type",
  "concern_list": [
    {{
      "name": "concern_name",
      "description": "brief explanation of why this concern matters"
    }}
  ]
}}
"""


EXPAND_CONCERN_PROMPT_TEMPLATE = """
You are a senior product user experience expert. Your task is to analyze the product type and expand the existing concern list with additional relevant concerns that might prevent users from purchasing this type of product.

Product Type: {product_type}
Product Title: {product_title}
Product Price: {product_price}

Existing Concerns:
{existing_concerns}

Product page content:
{product_page_content}

Please follow these steps:
1. Focus on the product type rather than specific product issues
2. Consider concerns that are common for this type of product in general
3. Think about what factors might deter users from purchasing this category of products
4. For each new concern, provide a clear and concise description
5. Ensure the new concerns are distinct and meaningful, not just variations of existing ones
6. Avoid listing specific product problems or defects

Output your result in the following format:
{{
  "product_type": "determined product type",
  "concern_list": [
    {{
      "name": "concern_name",
      "description": "short description here"
    }}
  ]
}}
"""


llm_conf = llm.LLMConfig('doubao', llm.DOUBAO_DEEPSEEK, 0.6, 10000)


async def extract_product_type_concerns(domain: str, product_type: str) -> schema.ProductsConcerns | None:
    products = await operations.filter_product_metadata(domain, product_type=product_type)
    all_product_titles = "\n".join([product.title for product in products[:3]])
    prompt = CONCERN_PROMPT_TEMPLATE.format(item_title_list=all_product_titles)
    result = await llm.call_llm(llm_conf, prompt)
    if not result:
        return None
    if not isinstance(result, dict) or "concern_list" not in result:
        result = schema.ProductsConcerns(product_type=product_type, concern_list=[])
    else:
        result = schema.ProductsConcerns.model_validate(result, strict=False)
    return result


@celery_task
async def extract_all_product_type_concerns(domain: str):
    """
    挖掘指定站点所有商品类型的顾虑点
    """
    all_products = await operations.list_product_metadata(domain)
    product_types = set(p.product_type for p in all_products)
    tasks = [extract_and_save_single_product_type_concerns(domain, product_type) for product_type in product_types]
    await CeleryTaskTqdmAsync.gather(*tasks, desc="Extracting product concerns")


@celery_task
async def extract_and_save_single_product_type_concerns(
        domain: str, product_type: str, clear_existing: bool = False, do_expand: bool = False):
    """
    挖掘指定站点单个商品类型的顾虑点
    """
    site = await operations.get_site(domain)
    existed = await operations.list_product_type_concerns(site.id, product_type)
    if existed is not None and not clear_existing:
        logger.info(f"product type concerns already exist for {product_type}, skipping extraction.")
        return
    elif existed is not None and clear_existing:
        logger.info(f"clearing existing product type concerns for {product_type} in {domain}")
        await operations.delete_product_type_concerns(site.id, product_type)
    result = await extract_product_type_concerns(domain, product_type)
    if result:
        logger.info(f"extracted product concerns for {product_type=}: {result}")
        await operations.save_product_type_concerns(site.id, product_type, result)
        if do_expand:
            await expand_and_save_single_product_type_concerns(domain, product_type, clear_existing)


async def expand_product_concerns(domain: str, product_type: str) -> schema.ProductsConcerns | None:
    site = await operations.get_site(domain)
    existed = await operations.list_product_type_concerns(site.id, product_type)
    if existed is None:
        logger.error(f"no concerns found for product_type {product_type} in {domain}")
        return None
    existing_concerns = "\n".join([f"- {item.name}: {item.description}" for item in existed.concern_list])
    longest_markdown_page = await operations.find_product_raw_pages_with_longest_markdown(domain, product_type)
    if longest_markdown_page is None:
        logger.error(f"no product page found for product_type {product_type}")
        return None
    product = await longest_markdown_page.metadata
    prompt = EXPAND_CONCERN_PROMPT_TEMPLATE.format(
        product_type=product_type,
        product_title=product.title,
        product_price=product.price,
        existing_concerns=existing_concerns,
        product_page_content=longest_markdown_page.markdown_content
    )
    result = await llm.call_llm(llm_conf, prompt)
    if not result:
        return None
    if not isinstance(result, dict) or "concern_list" not in result:
        concern_list = []
    else:
        result = schema.ProductsConcerns.model_validate(result, strict=False)
        logger.info(f"expand product concerns for {product_type=}: {result}")
        concern_list = result.concern_list
    merged_concern_list = schema.NameAndDescription.merge_name_description_lists(existed.concern_list, concern_list)
    return schema.ProductsConcerns(
        product_type=existed.product_type,
        concern_list=merged_concern_list
    )


@celery_task
async def expand_all_product_type_concerns(domain: str):
    """
    扩展指定站点所有商品类型的顾虑点
    """
    all_products = await operations.list_product_metadata(domain)
    product_types = set(p.product_type for p in all_products)
    tasks = [expand_and_save_single_product_type_concerns(domain, product_type) for product_type in product_types]
    await CeleryTaskTqdmAsync.gather(*tasks, desc="Extracting product concerns")


@celery_task
async def expand_and_save_single_product_type_concerns(
        domain: str, product_type: str, clear_existing: bool = False):
    """
    扩展指定站点单个商品类型的顾虑点
    """
    site = await operations.get_site(domain)
    existed = await operations.list_expanded_product_type_concerns(site.id, product_type)
    if existed is not None and not clear_existing:
        logger.info(f"product type concerns already exist for {product_type}, skipping extraction.")
        return
    elif existed is not None and clear_existing:
        logger.info(f"clearing existing product type concerns for {product_type} in {domain}")
        await operations.delete_expanded_product_type_concerns(site.id, product_type)
    result = await expand_product_concerns(domain, product_type)
    if result:
        await operations.save_expanded_product_type_concerns(site.id, product_type, result)
