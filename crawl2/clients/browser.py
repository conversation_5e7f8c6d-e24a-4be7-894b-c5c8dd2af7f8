import asyncio
import contextlib
from typing import Literal

import crawl4ai
import aiolimiter
from crawl4ai import Async<PERSON><PERSON><PERSON>raw<PERSON>, BrowserConfig

from crawl2.config import settings
from crawl2.utils import get_global_cookies

_web_crawler_lock = asyncio.Lock()
_WebCrawlerViewport = Literal["default", "mobile"]
_web_crawlers: dict[_WebCrawlerViewport, AsyncWebCrawler] = dict()
_rate_limit = aiolimiter.AsyncLimiter(2, 1)  # 15 requests per second
_concurrency_limit = asyncio.Semaphore(1)  # Limit concurrency to 5 requests


@contextlib.asynccontextmanager
async def get_web_crawler(viewport: _WebCrawlerViewport = "default") -> AsyncWebCrawler:
    browser_config = {
        "default": BrowserConfig(
            headless=True,
            proxy=settings.CRAWL_PROXY,
            cookies=get_global_cookies()
        ),
        "mobile": BrowserConfig(
            headless=True,
            proxy=settings.CRAWL_PROXY,
            cookies=get_global_cookies(),
            viewport_width=430,
            viewport_height=932
        )
    }

    global _web_crawler_lock
    async with _web_crawler_lock:
        if viewport not in _web_crawlers:
            web_crawler = AsyncWebCrawler(config=browser_config[viewport])
            await web_crawler.start()
            _web_crawlers[viewport] = web_crawler
    async with _rate_limit, _concurrency_limit:
        yield _web_crawlers[viewport]


async def crawl_with_timeout(url: str, config: crawl4ai.CrawlerRunConfig, timeout: int = 60) -> crawl4ai.CrawlResult:
    """
    Crawl a URL with a timeout.

    :param url: The URL to crawl.
    :param config: Crawler configuration.
    :param timeout: Timeout in seconds.
    :return: Crawler result.
    """
    async with get_web_crawler() as ai_crawler:
        res_ = await asyncio.wait_for(ai_crawler.arun(url, config=config), timeout=timeout)
        return res_
