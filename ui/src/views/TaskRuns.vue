<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { api } from '@/api/api.js'
import TaskItem from '../components/task/TaskItem.vue'
import Navigation from '../components/common/Navigation.vue'
import CancelConfirmDialog from '../components/task/CancelConfirmDialog.vue'
import { AlertCircle, ClipboardList } from 'lucide-vue-next'

const route = useRoute()
const router = useRouter()

const props = defineProps({
  pollInterval: {
    type: Number,
    default: 5000 // 5秒轮询一次
  }
})

const tasks = ref([])
const loading = ref(false)
const error = ref(null)

// 分页相关
const page = ref(1)
const pageSize = ref(20)
const total = ref(0)
const totalPages = ref(1)

// 过滤参数
const filterDomain = ref('')
const filterProductUrl = ref('')
const filterTaskName = ref('')
const filterStatus = ref('')

// 批量取消相关
const selectedTaskIds = ref(new Set())
const showBatchCancelDialog = ref(false)

// 从路由参数获取初始过滤条件
const domain = computed(() => route.query.domain || '')
const productUrl = computed(() => route.query.product_url || '')
const taskName = computed(() => route.query.task_name || '')
const status = computed(() => route.query.status || '')

// 监听路由变化，更新过滤条件
watch(
  () => route.query,
  (newQuery) => {
    filterDomain.value = newQuery.domain || ''
    filterProductUrl.value = newQuery.product_url || ''
    filterTaskName.value = newQuery.task_name || ''
    filterStatus.value = newQuery.status || ''
    // 重置到第一页
    page.value = 1
  },
  { immediate: true }
)

// 计算未完成的任务数量
const pendingTasksCount = computed(() => {
  return tasks.value.filter(task => 
    task.status !== 'SUCCESS' && 
    task.status !== 'FAILURE' && 
    task.status !== 'FAILED'
  ).length
})

// 计算当前页面可取消的任务数量
const cancelableTasksCount = computed(() => {
  return tasks.value.filter(task => 
    ['PENDING', 'STARTED'].includes(task.status)
  ).length
})

// 计算当前页面已选择的任务数量
const selectedCount = computed(() => selectedTaskIds.value.size)

// 计算是否全选
const isAllSelected = computed(() => {
  const cancelableCount = cancelableTasksCount.value
  return cancelableCount > 0 && selectedCount.value === cancelableCount
})

// 计算是否部分选择
const isIndeterminate = computed(() => {
  const cancelableCount = cancelableTasksCount.value
  return cancelableCount > 0 && selectedCount.value > 0 && selectedCount.value < cancelableCount
})

// 过滤任务列表（分页后只对当前页数据过滤）
const filteredTasks = computed(() => {
  return tasks.value.filter(task => {
    // 按任务名称过滤（已在后端过滤，这里可省略）
    return true
  })
})

// 获取任务列表
const fetchTasks = async () => {
  try {
    loading.value = true
    const params = {
      page: page.value,
      size: pageSize.value,
    }
    if (filterDomain.value) params.domain = filterDomain.value
    if (filterProductUrl.value) params.product_url = filterProductUrl.value
    if (filterTaskName.value) params.task_name = filterTaskName.value
    if (filterStatus.value) params.status = filterStatus.value
    const response = await api.get('/api/v1/tasks/runs', { params })
    const data = response.data
    tasks.value = data.items
    total.value = data.total
    totalPages.value = data.total_pages
    // 按创建时间排序（后端已排序，这里可省略）
    // tasks.value.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
    loading.value = false
  } catch (err) {
    error.value = err.message
    loading.value = false
    console.error('Error fetching tasks:', err)
  }
}

// 分页切换
const handlePageChange = (newPage) => {
  if (newPage !== page.value) {
    page.value = newPage
    // 清空选择状态
    selectedTaskIds.value.clear()
    fetchTasks()
    // 自动滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
}
const handlePageSizeChange = (newSize) => {
  if (newSize !== pageSize.value) {
    pageSize.value = newSize
    page.value = 1
    // 清空选择状态
    selectedTaskIds.value.clear()
    fetchTasks()
    // 自动滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
}

// 快速跳转到指定页
const jumpToPage = () => {
  const input = document.getElementById('pageInput')
  const targetPage = parseInt(input.value)
  if (targetPage && targetPage >= 1 && targetPage <= totalPages.value) {
    handlePageChange(targetPage)
  }
}

// 轮询定时器
let pollTimer = null

// 停止轮询
const stopPolling = () => {
  if (pollTimer) {
    clearInterval(pollTimer)
    pollTimer = null
  }
}

// 处理任务取消事件
const handleTaskCanceled = (taskId) => {
  // 找到被取消的任务并更新其状态
  const task = tasks.value.find(t => t.task_id === taskId)
  if (task) {
    task.status = 'CANCELED'
    task.error = 'Task was canceled by user'
    task.finished_at = new Date().toISOString()
  }
  // 从选择列表中移除
  selectedTaskIds.value.delete(taskId)
}

// 处理任务重试事件：刷新列表
const handleTaskRetried = async () => {
  await fetchTasks()
}

// 应用过滤条件并更新URL
const applyFilters = async () => {
  const query = { ...route.query }
  
  // 更新查询参数
  if (filterDomain.value) query.domain = filterDomain.value
  else delete query.domain
  
  if (filterProductUrl.value) query.product_url = filterProductUrl.value
  else delete query.product_url
  
  if (filterTaskName.value) query.task_name = filterTaskName.value
  else delete query.task_name
  
  if (filterStatus.value) query.status = filterStatus.value
  else delete query.status
  
  // 重置页码
  query.page = 1
  
  // 更新URL
  await router.push({ query })
  
  // 重新获取数据
  page.value = 1
  // 清空选择状态
  selectedTaskIds.value.clear()
  await fetchTasks()
}

// 处理搜索任务名称事件
const handleSearchTaskName = (taskName) => {
  filterTaskName.value = taskName
  // 自动应用过滤
  applyFilters()
}

// 选择相关方法
const toggleTaskSelection = (taskId) => {
  if (selectedTaskIds.value.has(taskId)) {
    selectedTaskIds.value.delete(taskId)
  } else {
    selectedTaskIds.value.add(taskId)
  }
}

const toggleSelectAll = () => {
  const cancelableTasks = tasks.value.filter(task => 
    ['PENDING', 'STARTED'].includes(task.status)
  )
  
  if (isAllSelected.value) {
    // 取消全选
    selectedTaskIds.value.clear()
  } else {
    // 全选
    cancelableTasks.forEach(task => {
      selectedTaskIds.value.add(task.task_id)
    })
  }
}

// 格式化任务信息用于显示
const formatTaskInfo = (task) => {
  const createdAt = new Date(task.created_at).toLocaleString()
  const taskName = task.name || '未命名任务'
  
  // 格式化参数
  let paramsStr = ''
  if (task.params && Object.keys(task.params).length > 0) {
    paramsStr = Object.entries(task.params)
      .map(([key, value]) => {
        const formattedValue = typeof value === 'string' ? value : JSON.stringify(value)
        return `${key}: ${formattedValue}`
      })
      .join(', ')
  }
  
  if (paramsStr) {
    return `${createdAt} ${taskName} (${paramsStr})`
  } else {
    return `${createdAt} ${taskName}`
  }
}

// 根据任务ID获取任务显示信息
const getTaskDisplayInfo = (taskId) => {
  const task = tasks.value.find(t => t.task_id === taskId)
  return task ? formatTaskInfo(task) : `任务 ${taskId}`
}

// 批量取消相关方法
const openBatchCancelDialog = () => {
  if (selectedTaskIds.value.size === 0) return
  showBatchCancelDialog.value = true
}

const closeBatchCancelDialog = () => {
  showBatchCancelDialog.value = false
}

// 处理批量取消完成
const handleBatchCancelCompleted = (result) => {
  // 更新本地任务状态
  result.results.forEach(r => {
    if (r.success) {
      handleTaskCanceled(r.taskId)
    }
  })
  
  // 清空选择状态
  selectedTaskIds.value.clear()
  
  // 关闭对话框
  showBatchCancelDialog.value = false
}

onMounted(() => {
  fetchTasks()
  pollTimer = setInterval(fetchTasks, props.pollInterval)
})

onUnmounted(() => {
  stopPolling()
})
</script>

<template>
  <div class="flex flex-col h-screen w-full pt-0">
    <Navigation
        :breadcrumbs="[
        { text: 'Sites', path: '/' },
        { text: 'Task Runs' }
      ]"
    />

    <div class="flex-1 p-6 bg-gray-50 pb-24">
      <div class="max-w-7xl mx-auto">
        <!-- 页面标题和统计信息 -->
        <div class="mb-6">
          <div class="flex items-center justify-between mb-4">
            <div>
              <h1 class="text-2xl font-bold text-gray-900">任务运行历史</h1>
            </div>
            <div class="flex items-center gap-4">
              <div class="flex items-center gap-2">
                <span class="text-sm text-gray-600">待处理任务:</span>
                <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-sm font-medium">
                  {{ pendingTasksCount }}
                </span>
              </div>
              <button 
                @click="fetchTasks"
                :disabled="loading"
                class="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                {{ loading ? '刷新中...' : '刷新' }}
              </button>
            </div>
          </div>
          
          <!-- 过滤条件显示 -->
          <div v-if="domain || productUrl || taskName || status" class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <div class="text-sm text-blue-800">
              <span class="font-medium">当前过滤条件:</span>
              <span v-if="domain" class="ml-2">域名: {{ domain }}</span>
              <span v-if="productUrl" class="ml-2">产品URL: {{ productUrl }}</span>
              <span v-if="taskName" class="ml-2">任务名称: {{ taskName }}</span>
              <span v-if="status" class="ml-2">状态: {{ status }}</span>
            </div>
          </div>
        </div>

        <!-- 过滤条件 -->
        <div class="mb-4 bg-white rounded-lg shadow p-4">
          <h3 class="text-lg font-medium text-gray-900 mb-3">过滤条件</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">域名</label>
              <input
                v-model="filterDomain"
                type="text"
                placeholder="输入域名进行过滤..."
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">产品URL</label>
              <input
                v-model="filterProductUrl"
                type="text"
                placeholder="输入产品URL进行过滤..."
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                任务名称
              </label>
              <input
                v-model="filterTaskName"
                type="text"
                placeholder="输入任务名称进行过滤..."
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
              <select v-model="filterStatus" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                <option value="">全部</option>
                <option value="PENDING">待处理</option>
                <option value="STARTED">运行中</option>
                <option value="SUCCESS">成功</option>
                <option value="FAILED">已失败</option>
                <option value="CANCELED">已取消</option>
                <option value="RETRIED">已重试</option>
              </select>
            </div>
          </div>
          <!-- 应用过滤按钮 -->
          <div class="mt-4 flex justify-end">
            <button
              @click="applyFilters"
              class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              应用过滤
            </button>
          </div>
        </div>

        <!-- 错误信息 -->
        <div v-if="error" class="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex items-center">
            <AlertCircle class="h-5 w-5 text-red-400 mr-2" />
            <span class="text-red-800">{{ error }}</span>
          </div>
        </div>

        <!-- 任务列表 -->
        <div class="bg-white rounded-lg shadow">
          <!-- 批量操作工具栏 -->
          <div v-if="cancelableTasksCount > 0" class="p-4 border-b border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-4">
                <div class="flex items-center gap-2">
                  <input
                    type="checkbox"
                    :checked="isAllSelected"
                    :indeterminate="isIndeterminate"
                    @change="toggleSelectAll"
                    class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                  />
                  <label class="text-sm font-medium text-gray-700">
                    全选 ({{ selectedCount }}/{{ cancelableTasksCount }})
                  </label>
                </div>
                <button
                  v-if="selectedCount > 0"
                  @click="openBatchCancelDialog"
                  class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                >
                  批量取消 ({{ selectedCount }})
                </button>
              </div>
            </div>
          </div>

          <div v-if="loading && tasks.length === 0" class="p-8 text-center">
            <div class="inline-flex items-center justify-center w-8 h-8 border-4 border-green-500 border-t-transparent rounded-full animate-spin mb-4"></div>
            <p class="text-gray-600">加载任务中...</p>
          </div>
          
          <div v-else-if="filteredTasks.length === 0" class="p-8 text-center">
            <ClipboardList class="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p class="text-gray-600">
              {{ (filterDomain || filterProductUrl || filterTaskName || filterStatus) ? '没有找到匹配的任务' : '暂无任务' }}
            </p>
          </div>
          
          <div v-else class="divide-y divide-gray-200">
            <TaskItem 
              v-for="task in filteredTasks" 
              :key="task.task_id" 
              :task="task"
              :is-selected="selectedTaskIds.has(task.task_id)"
              :can-select="['PENDING', 'STARTED'].includes(task.status)"
              @task-canceled="handleTaskCanceled"
              @toggle-selection="toggleTaskSelection"
              @search-task-name="handleSearchTaskName"
              @task-retried="handleTaskRetried"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 固定分页控件 -->
    <div v-if="total > 0" class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
      <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="flex justify-center items-center gap-4 text-sm text-gray-600">
          <span>共 {{ total }} 个任务，页码：</span>
          <button :disabled="page === 1" @click="handlePageChange(page - 1)" class="px-3 py-2 rounded border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50" >上一页</button>
          <span class="font-medium">{{ page }} / {{ totalPages }}</span>
          <button :disabled="page === totalPages" @click="handlePageChange(page + 1)" class="px-3 py-2 rounded border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">下一页</button>
          <span>跳转到</span>
          <input 
            id="pageInput"
            type="number" 
            min="1" 
            :max="totalPages" 
            class="w-16 px-2 py-2 border rounded text-center"
            @keyup.enter="jumpToPage"
          />
          <button @click="jumpToPage" class="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">跳转</button>
          <span>每页</span>
          <select v-model.number="pageSize" @change="handlePageSizeChange($event.target.value)" class="border rounded px-2 py-2">
            <option :value="10">10</option>
            <option :value="20">20</option>
            <option :value="50">50</option>
            <option :value="100">100</option>
          </select>
          <span>条</span>
        </div>
      </div>
    </div>

    <!-- 批量取消确认对话框 -->
    <CancelConfirmDialog
      :show="showBatchCancelDialog"
      :title="selectedCount === 1 ? '取消任务' : '批量取消任务'"
      :description="`确定要取消${selectedCount === 1 ? '这个任务' : `选中的 ${selectedCount} 个任务`}吗？此操作不可撤销。`"
      :tasks="Array.from(selectedTaskIds).map(taskId => ({ 
        id: taskId, 
        displayInfo: getTaskDisplayInfo(taskId) 
      }))"
      @close="closeBatchCancelDialog"
      @cancel-completed="handleBatchCancelCompleted"
    />
  </div>
</template> 