from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "blog_metadata" ALTER COLUMN "url" TYPE VARCHAR(1024) USING "url"::VARCHAR(1024);
        ALTER TABLE "collection_metadata" ALTER COLUMN "url" TYPE VARCHAR(1024) USING "url"::VARCHAR(1024);
        ALTER TABLE "page_metadata" ALTER COLUMN "url" TYPE VARCHAR(1024) USING "url"::VARCHAR(1024);
        ALTER TABLE "product_metadata" ALTER COLUMN "url" TYPE VARCHAR(1024) USING "url"::VARCHAR(1024);
        ALTER TABLE "store_document_faqs" ALTER COLUMN "file_url" TYPE VARCHAR(1024) USING "file_url"::VARCHAR(1024);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "blog_metadata" ALTER COLUMN "url" TYPE VARCHAR(255) USING "url"::VARCHAR(255);
        ALTER TABLE "page_metadata" ALTER COLUMN "url" TYPE VARCHAR(255) USING "url"::VARCHAR(255);
        ALTER TABLE "product_metadata" ALTER COLUMN "url" TYPE VARCHAR(255) USING "url"::VARCHAR(255);
        ALTER TABLE "store_document_faqs" ALTER COLUMN "file_url" TYPE VARCHAR(255) USING "file_url"::VARCHAR(255);
        ALTER TABLE "collection_metadata" ALTER COLUMN "url" TYPE VARCHAR(255) USING "url"::VARCHAR(255);"""
