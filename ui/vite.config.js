import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import tailwindcss from '@tailwindcss/vite'
import http from 'http'

// 检测端点可用性的函数
function checkEndpoint(url) {
  return new Promise((resolve) => {
    const req = http.get(url, (res) => {
      resolve(res.statusCode === 200);
    });
    
    req.on('error', () => {
      resolve(false);
    });
    
    req.setTimeout(3000, () => {
      req.destroy();
      resolve(false);
    });
  });
}

// 动态获取可用的 target
async function getAvailableTarget() {
  // 按优先级检测端点
  const endpoints = [
    'http://127.0.0.1:8181/api/v1/health',
    'http://192.168.1.99:8000/api/v1/health',
    'https://shop-pupil-stq.leyanbot.com/api/v1/health'
  ];
  
  for (const endpoint of endpoints) {
    try {
      const isAvailable = await checkEndpoint(endpoint);
      if (isAvailable) {
        // 移除 /health 路径，返回基础 URL
        const baseUrl = endpoint.replace('/api/v1/health', '');
        console.log(`✅ 使用后端 api 地址: ${baseUrl}`);
        return baseUrl;
      }
    } catch (error) {
      console.log(`❌ 检测后端 api 失败: ${endpoint}`, error.message);
    }
  }
  
  // 如果所有端点都不可用，使用默认的线上地址
  console.log('⚠️ 所有本地后端API都不可用，使用线上stq环境地址');
  return 'https://shop-pupil-stq.leyanbot.com';
}

// https://vitejs.dev/config/
export default defineConfig(async ({ command, mode }) => {
  let target;

  // 只在开发模式下检测后端服务
  if (command === 'serve' && mode === 'development') {
    const detectTargetStartTime = Date.now();
    target = await getAvailableTarget();
    const detectTargetEndTime = Date.now();
    console.log(`检测后端API地址耗时: ${detectTargetEndTime - detectTargetStartTime}ms`);
  } else {
    target = 'http://127.0.0.1:8181';
    console.log(`使用固定后端API地址: ${target}`);
  }
  
  return {
    plugins: [vue(), tailwindcss()],
    base: '/', // 设置基础路径为根路径
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    server: {
      host: '127.0.0.1',
      port: 8000,
      proxy: {
        '/api': {
          target: target,
          changeOrigin: false,
          secure: false,
        },
        '/docs': {
          target: target,
          changeOrigin: true,
          secure: false,
        },
        '/openapi.json': {
          target: target,
          changeOrigin: true,
          secure: false,
        }
      }
    },
    build: {
      rollupOptions: {
        output: {
          // 确保静态资源使用绝对路径
          assetFileNames: (assetInfo) => {
            if (assetInfo.name === 'style.css') return 'assets/[name][extname]'
            return 'assets/[name]-[hash][extname]'
          },
          // 确保 JS 文件使用绝对路径
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js'
        }
      }
    }
  }
})
