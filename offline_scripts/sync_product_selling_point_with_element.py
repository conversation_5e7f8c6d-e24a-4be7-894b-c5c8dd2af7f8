import asyncio
import json
import os
from pathlib import Path
from typing import List, Dict, Any

from loguru import logger
from tqdm.asyncio import tqdm

from crawl2.clients.shopify_knowledge import (
    ShopifyKnowledgeClient,
    ProductSellingPoints,
    ProductSellingPointItem,
)


def load_alignment_results_from_file(filepath: str) -> Dict[str, Any]:
    """
    从文件加载对齐结果数据

    Args:
        filepath: JSON 文件路径

    Returns:
        解析后的 JSON 数据
    """
    with open(filepath, "r", encoding="utf-8") as f:
        data = json.load(f)
    logger.info(f"Loaded alignment results from {filepath}")
    logger.info(f"Domain: {data['shopify_domain']}, Total products: {len(data['products'])}")
    return data


def convert_alignment_to_selling_points(
    product_data: Dict[str, Any]
) -> ProductSellingPoints:
    """
    将对齐结果数据转换为 ProductSellingPoints 格式

    Args:
        product_data: 单个产品的对齐数据

    Returns:
        ProductSellingPoints 对象
    """
    product_id = product_data["product_id"]
    alignments = product_data.get("alignments", [])

    selling_points_list = []

    for alignment in alignments:
        selling_point_data = alignment.get("selling_point", {})
        related_elements = alignment.get("related_elements", [])

        # 提取卖点信息
        aspect = selling_point_data.get("aspect", "")
        description = selling_point_data.get(
            "content", selling_point_data.get("description", "")
        )
        marketing_copies = selling_point_data.get("marketingCopies", [])

        # 如果 marketingCopies 是包含字符串的字典列表，提取字符串值
        if marketing_copies and isinstance(marketing_copies[0], dict):
            marketing_copies = [
                copy.get("content", "")
                for copy in marketing_copies
                if copy.get("content")
            ]

        # 提取元素目标（页面元素选择器）
        element_targets = []
        for element in related_elements:
            target = element.get("target", "")
            if target:
                element_targets.append(target)

        # 创建卖点项
        selling_point_item = ProductSellingPointItem(
            aspect=aspect,
            description=description,
            marketingCopies=marketing_copies,
            element_targets=element_targets,
        )

        selling_points_list.append(selling_point_item)

    return ProductSellingPoints(spuId=product_id, sellingPointsList=selling_points_list)


async def sync_product_selling_points_to_shopify_knowledge(
    shopify_knowledge_client: ShopifyKnowledgeClient,
    domain: str,
    products_data: List[Dict[str, Any]],
) -> None:
    """
    将产品卖点数据同步到 shopify_knowledge

    Args:
        domain: 店铺域名
        products_data: 产品数据列表
    """

    logger.info(f"Starting sync for domain: {domain}")
    logger.info(f"Total products to sync: {len(products_data)}")

    success_count = 0
    failed_count = 0

    for product_data in tqdm(products_data, desc="Syncing products"):
        try:
            product_id = product_data["product_id"]
            alignments = product_data.get("alignments", [])

            # 跳过没有对齐结果的产品
            if not alignments:
                logger.debug(f"Skipping product {product_id} - no alignments found")
                continue

            # 转换为 ProductSellingPoints 格式
            selling_points = convert_alignment_to_selling_points(product_data)

            # 跳过没有卖点的产品
            if not selling_points.sellingPointsList:
                logger.debug(f"Skipping product {product_id} - no selling points found")
                continue

            # 同步到 shopify_knowledge
            response = await shopify_knowledge_client.sync_shopify_knowledge(
                domain, selling_points
            )
            logger.info(f"Sync response: {response}")

            logger.info(
                f"Successfully synced product {product_id} with {len(selling_points.sellingPointsList)} selling points"
            )
            success_count += 1

        except Exception as e:
            logger.error(
                f"Failed to sync product {product_data.get('product_id', 'unknown')}: {str(e)}"
            )
            failed_count += 1
            continue

    logger.info(f"Sync completed. Success: {success_count}, Failed: {failed_count}")


async def sync_alignment_results_from_directory(
    shopify_knowledge_client: ShopifyKnowledgeClient,
    directory_path: str = "alignment_results",
) -> None:
    """
    从指定目录加载所有对齐结果文件并同步到 shopify_knowledge

    Args:
        directory_path: 包含对齐结果 JSON 文件的目录路径
    """
    alignment_dir = Path(directory_path)

    if not alignment_dir.exists():
        logger.error(f"Directory {directory_path} does not exist")
        return

    # 查找所有 JSON 文件
    json_files = list(alignment_dir.glob("*.json"))

    if not json_files:
        logger.warning(f"No JSON files found in {directory_path}")
        return

    logger.info(f"Found {len(json_files)} JSON files to process")

    for json_file in json_files:
        try:
            logger.info(f"Processing file: {json_file}")

            # 加载对齐结果数据
            alignment_data = load_alignment_results_from_file(str(json_file))

            domain = alignment_data.get("shopify_domain")
            products_data = alignment_data.get("products", [])

            if not domain:
                logger.error(f"No domain found in {json_file}, skipping")
                continue

            if not products_data:
                logger.warning(f"No products data found in {json_file}, skipping")
                continue

            # 同步产品卖点数据
            await sync_product_selling_points_to_shopify_knowledge(
                shopify_knowledge_client, domain, products_data
            )

            logger.info(f"Completed processing file: {json_file}")

        except Exception as e:
            logger.error(f"Error processing file {json_file}: {str(e)}")
            continue


async def sync_single_alignment_file(
    shopify_knowledge_client: ShopifyKnowledgeClient, filepath: str
) -> None:
    """
    同步单个对齐结果文件

    Args:
        shopify_knowledge_client: ShopifyKnowledgeClient 实例
        filepath: JSON 文件路径
    """
    try:
        logger.info(f"Processing single file: {filepath}")

        # 加载对齐结果数据
        alignment_data = load_alignment_results_from_file(filepath)

        domain = alignment_data.get("shopify_domain")
        products_data = alignment_data.get("products", [])

        if not domain:
            logger.error(f"No domain found in {filepath}")
            return

        if not products_data:
            logger.warning(f"No products data found in {filepath}")
            return

        # 同步产品卖点数据
        await sync_product_selling_points_to_shopify_knowledge(
            shopify_knowledge_client, domain, products_data
        )

        logger.info(f"Completed processing file: {filepath}")

    except Exception as e:
        logger.error(f"Error processing file {filepath}: {str(e)}")


async def main(
    shopify_knowledge_client: ShopifyKnowledgeClient,
    filepath: str = None,
    directory_path: str = None,
):
    """
    主函数 - 可以选择同步整个目录或单个文件
    """
    if filepath:
        if os.path.isfile(filepath):
            await sync_single_alignment_file(shopify_knowledge_client, filepath)
        else:
            logger.error(f"File {filepath} does not exist")
    elif directory_path:
        await sync_alignment_results_from_directory(shopify_knowledge_client, "alignment_results")
    else:
        logger.error("No filepath or directory_path provided")

if __name__ == "__main__":
    base_url = "https://usq-apiv2.jolect.com"
    auth_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsZXlhbl91c2VyIjp7Im5hbWUiOiJhZG1pbiJ9LCJpc3MiOiJzaG9waWZ5LXN0b3JlLWNlbnRlciIsInN0b3JlSW5mbyI6eyJzdG9yZUlkIjo0LjAsImRvbWFpbiI6Im9yb3JvLWNhLm15c2hvcGlmeS5jb20iLCJleHRlcm5hbFN0b3JlSWQiOiI0NjIwMzg5NTk2MSJ9LCJleHAiOjE3NTQ2NDgwMzgsImlhdCI6MTc1NDA0MzIzOCwidXNlcklkIjozLCJqdGkiOiI4MDk3NmY1ZC0zODJmLTQyNTItYjcyMy1jMmJlZjI2YTUyMGMiLCJvcmdJZCI6NX0.IX2Pmx6BySwQx1QLZnYrflXiCJDNRXHehy2R6EyjUMA"
    shopify_knowledge_client = ShopifyKnowledgeClient(
        base_url=base_url,
        api_key=auth_token,
    )
    file_path = "./alignment_results/alignment_site_ororowear.ca.json"
    asyncio.run(main(shopify_knowledge_client, file_path))
