from abc import ABC, abstractmethod
from typing import Any, Dict, List, Generic, TypeVar
from pydantic import BaseModel
from crawl2.clients.embedding import HttpEmbeddingClient
from qdrant_client import AsyncQdrantClient
from qdrant_client.models import (
    PointStruct,
    PointIdsList,
    WriteOrdering,
    Filter,
    FieldCondition,
    MatchValue,
    SparseVector,
    MatchAny,
)
from loguru import logger

SchemaT = TypeVar("SchemaT")


class IndexConfig(BaseModel):
    collection_name: str
    embedding_client_url: str
    sparse_embedding_client_url: str
    qdrant_url: str
    vector_size: int


class BaseQdrantIndexer(ABC, Generic[SchemaT]):
    def __init__(self, config: IndexConfig):
        self.config = config
        self.embedding_client = HttpEmbeddingClient(config.embedding_client_url)
        self.sparse_embedding_client = HttpEmbeddingClient(
            config.sparse_embedding_client_url
        )
        self.qdrant = AsyncQdrantClient(url=config.qdrant_url)

    @property
    @abstractmethod
    def vectors_config(self) -> Dict[str, Any]:
        """子类指定 dense embedding 字段及参数"""
        pass

    @property
    @abstractmethod
    def sparse_vectors_config(self) -> Dict[str, Any]:
        """子类指定 sparse embedding 字段及参数"""
        pass

    @property
    @abstractmethod
    def payload_indexes(self) -> Dict[str, str]:
        """
        子类指定需要建立payload索引的字段及类型
        例如: {"store_domain": "keyword", "product_id": "keyword"}
        """
        pass

    @abstractmethod
    def schema_to_payload(self, schema: SchemaT) -> Dict:
        """schema对象转payload dict"""
        pass

    @abstractmethod
    def get_id(self, schema: SchemaT) -> str:
        """子类实现获取id逻辑"""
        pass

    @abstractmethod
    async def generate_vectors(self, schema: List[SchemaT]) -> List[Dict[str, Any]]:
        """子类实现生成向量逻辑"""
        pass

    async def to_sparse_embedding(self, text_list: List[str]) -> List[SparseVector]:
        embeddings = (await self.sparse_embedding_client.embed_async(text_list))[
            "embeddings"
        ]
        sparse_vectors = [
            SparseVector(indices=embedding.keys(), values=embedding.values())
            for embedding in embeddings
        ]
        return sparse_vectors

    async def to_dense_embedding(self, text_list: List[str]) -> List[List[float]]:
        if len(text_list) == 0:
            return []
        return await self.embedding_client.embed_async(text_list)

    async def create_collection(self, force_recreate=False):
        exists = await self.collection_exists()
        if exists and not force_recreate:
            logger.info(
                f"Collection {self.config.collection_name} already exists, skipping creation"
            )
            return
        if exists and force_recreate:
            try:
                await self.qdrant.delete_collection(self.config.collection_name)
                logger.info(
                    f"Deleted existing collection: {self.config.collection_name}"
                )
            except Exception as e:
                logger.error(f"Error deleting collection: {e}")
        await self.qdrant.create_collection(
            collection_name=self.config.collection_name,
            vectors_config=self.vectors_config,
            sparse_vectors_config=self.sparse_vectors_config,
        )
        for field, schema in self.payload_indexes.items():
            await self.qdrant.create_payload_index(
                collection_name=self.config.collection_name,
                field_name=field,
                field_schema=schema,
            )

    async def collection_exists(self) -> bool:
        try:
            collections = await self.qdrant.get_collections()
            return any(
                collection.name == self.config.collection_name
                for collection in collections.collections
            )
        except Exception as e:
            logger.error(f"Error checking collection existence: {e}")
            return False

    async def upsert_point(self, doc: SchemaT):
        point_id = self.get_id(doc)
        vectors = await self.generate_vectors([doc])
        vector = vectors[0]
        await self.qdrant.upsert(
            collection_name=self.config.collection_name,
            points=[
                PointStruct(
                    id=point_id, vector=vector, payload=self.schema_to_payload(doc)
                )
            ],
            wait=True,
            ordering=WriteOrdering.WEAK,
        )

    async def index_batch(self, batch: List[SchemaT]):
        points = []
        vectors = await self.generate_vectors(batch)
        for i, (doc, vector) in enumerate(zip(batch, vectors)):
            points.append(
                PointStruct(
                    id=self.get_id(doc),
                    vector=vector,
                    payload=self.schema_to_payload(doc),
                )
            )
        await self.qdrant.upsert(
            collection_name=self.config.collection_name, points=points
        )

    async def get_point(self, point_id: str) -> Dict | None:
        points = await self.qdrant.retrieve(
            collection_name=self.config.collection_name,
            ids=[point_id],
            with_payload=True,
            with_vectors=False,
        )
        return points[0].payload if points else None

    async def delete_point(self, point_id: str):
        await self.qdrant.delete(
            collection_name=self.config.collection_name,
            points_selector=PointIdsList(points=[point_id]),
            wait=True,
            ordering=WriteOrdering.WEAK,
        )

    async def update_point(self, point_id: str, doc: SchemaT):
        vectors = await self.generate_vectors([doc])
        vector = vectors[0]
        await self.qdrant.upsert(
            collection_name=self.config.collection_name,
            points=[
                PointStruct(
                    id=point_id, vector=vector, payload=self.schema_to_payload(doc)
                )
            ],
            wait=True,
            ordering=WriteOrdering.WEAK,
        )

    async def delete_store_domain_knowledge(
        self, store_domain: str, source_list: list = None
    ):
        """删除指定store_domain和source的所有数据"""
        try:
            must_condition = []
            must_condition.append(
                FieldCondition(
                    key="store_domain",
                    match=MatchValue(value=store_domain.lower()),
                )
            )
            if source_list:
                must_condition.append(
                    FieldCondition(key="source", match=MatchAny(any=source_list))
                )
            await self.qdrant.delete(
                collection_name=self.config.collection_name,
                points_selector=Filter(must=must_condition),
            )
            logger.info(f"Deleted all data for store_domain: {store_domain}")
        except Exception as e:
            logger.error(f"Error deleting data for store_domain {store_domain}: {e}")
            raise

    async def delete_single_product_knowledge(
        self, store_domain: str, product_id: str, source_list: list = None
    ):
        """删除指定store_domain的指定product_id的所有数据"""
        try:
            must_condition = []
            must_condition.append(
                FieldCondition(
                    key="store_domain",
                    match=MatchValue(value=store_domain.lower()),
                )
            )
            must_condition.append(
                FieldCondition(key="product_id", match=MatchValue(value=product_id))
            )
            if source_list:
                must_condition.append(
                    FieldCondition(key="source", match=MatchAny(any=source_list))
                )
            await self.qdrant.delete(
                collection_name=self.config.collection_name,
                points_selector=Filter(must=must_condition),
            )
            logger.info(
                f"Deleted all data for store_domain: {store_domain} and product_id: {product_id}"
            )
        except Exception as e:
            logger.error(
                f"Error deleting data for store_domain {store_domain} and product_id {product_id}: {e}"
            )
            raise

    async def check_document_count(self, store_domain: str, total_documents: int):
        try:
            count_result = await self.qdrant.count(
                collection_name=self.config.collection_name,
                count_filter=Filter(
                    must=[
                        FieldCondition(
                            key="store_domain",
                            match=MatchValue(value=store_domain.lower()),
                        )
                    ]
                ),
                exact=True,
            )
            qdrant_doc_count = count_result.count
            logger.info(f"--- Verification for store_domain: {store_domain} ---")
            logger.info(f"Documents processed: {total_documents}")
            logger.info(f"Documents in Qdrant: {qdrant_doc_count}")

            if total_documents == qdrant_doc_count:
                logger.info(
                    "Verification successful: Document count in Qdrant matches the number of documents processed."
                )  # NOQA
            else:
                logger.warning(
                    f"Verification failed: Mismatch in document count! Expected {total_documents}, but found {qdrant_doc_count}."  # NOQA
                )  # NOQA
        except Exception as e:
            logger.error(
                f"Failed to verify document count in Qdrant for store_domain {store_domain}: {e}"
            )
