<template>
  <div
    class="cursor-default text-gray-600 font-medium h-full flex items-center whitespace-nowrap overflow-hidden text-ellipsis leading-[1.2] py-1"
    @mouseenter="showPopup"
    @mouseleave="hidePopup"
    ref="cellRef"
  >
    <div v-if="hasFormatter" v-html="formattedValue" class="w-full whitespace-nowrap overflow-hidden text-ellipsis"></div>
    <template v-else>{{ value }}</template>

    <teleport to="body">
      <div v-if="isPopupVisible" class="value-popup" :style="popupStyle" ref="popupRef">
        <pre>{{ value }}</pre>
      </div>
    </teleport>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted, nextTick } from 'vue'

const props = defineProps({
  params: {
    type: Object,
    required: true
  }
})

const cellRef = ref(null)
const popupRef = ref(null)
const isPopupVisible = ref(false)
const popupStyle = ref({})
let showTimeout = null

const value = computed(() => props.params.value)
const hasFormatter = computed(() => props.params.colDef.cellRendererParams?.formatter)

const formattedValue = computed(() => {
  if (hasFormatter.value && value.value) {
    return props.params.colDef.cellRendererParams.formatter(value.value)
  }
  return value.value
})

const showPopup = async () => {
  if (!value.value) return

  clearTimeout(showTimeout)
  showTimeout = setTimeout(async () => {
    isPopupVisible.value = true
    await nextTick()
    calculatePopupPosition()
  }, 300) // 300ms delay
}

const hidePopup = () => {
  clearTimeout(showTimeout)
  isPopupVisible.value = false
}

const calculatePopupPosition = () => {
  const triggerEl = cellRef.value
  const popupEl = popupRef.value
  if (!triggerEl || !popupEl) return

  // Use the parent ag-cell for more reliable positioning within the grid
  const cellEl = triggerEl.closest('.ag-cell') || triggerEl

  const cellRect = cellEl.getBoundingClientRect()
  const popupRect = popupEl.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  let top = cellRect.bottom + 5
  let left = cellRect.left

  if (left + popupRect.width > viewportWidth) {
    left = viewportWidth - popupRect.width - 5
  }
  if (left < 0) {
    left = 5
  }

  if (top + popupRect.height > viewportHeight && cellRect.top > popupRect.height + 5) {
    top = cellRect.top - popupRect.height - 5
  }

  popupStyle.value = {
    top: `${top}px`,
    left: `${left}px`,
    minWidth: `${cellRect.width}px`,
  }
}

onMounted(() => {
  window.addEventListener('scroll', hidePopup, true)
  window.addEventListener('resize', hidePopup)
})

onUnmounted(() => {
  window.removeEventListener('scroll', hidePopup, true)
  window.removeEventListener('resize', hidePopup)
})
</script>

<style>
/* Global styles for popup, not scoped */
.value-popup {
  position: fixed;
  z-index: 1050;
  background: white;
  border: 1px solid #e1e4e8;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  padding: 1rem;
  max-width: 600px;
  max-height: 400px;
  overflow-y: auto;
  transition: opacity 0.1s ease-in-out;
}

.value-popup pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 0.9rem;
  line-height: 1.45;
  color: #24292e;
}
</style> 