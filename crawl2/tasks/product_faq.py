import json

from loguru import logger

from crawl2 import schema
from crawl2.clients import llm, shopify_knowledge
from crawl2.db import operations
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync, CeleryTaskTqdm

PROMPT_TEMPLATE = """You are a professional e-commerce customer service expert. Given product detail page content, please generate realistic FAQ pairs that address common customer questions.

I will provide you with product detail page content. Please generate FAQ pairs that:
- Cover common customer questions based on the product information
- Provide detailed, factual answers using the product details
- Use natural, conversational language
- Include specific product features and benefits
- Keep answers concise yet informative (50-100 words per answer)
- Generate at least 5 different FAQ pairs

Output the results in the following JSON array format:
```json
[
       {{
            "question": "What is the material of this product?",
            "answer": "This product is made from high-quality [material], which provides durability and [specific benefit]. The material has been tested for [feature] and is suitable for [use case]."
        }},
        {{
            "question": "How do I clean/maintain this product?",
            "answer": "For best results, [cleaning instructions]. Avoid [things to avoid]. The product can be [maintenance tips]. Regular maintenance will ensure [benefit]."
        }}
]
```

Product Detail Page Data:
{content}
"""


llm_conf = llm.LLMConfig('usq', 'qwen2.5-32b-chat-fp8-offline', 0.7, 2000)


async def extract_single_product_faqs(url: str) -> list[schema.Faq] | None:
    product = await operations.get_product_metadata(url)
    if not product:
        logger.warning(f"No product found for URL: {url}")
        return None
    if not product.product_id:
        logger.error(f"No product ID found for URL: {product.url}")
        return None
    knowledge = await operations.get_product_knowledge(url)
    if not knowledge or not knowledge.documents:
        logger.warning(f"No knowledge found for product URL: {product.url}")
        return None
    documents = [p for p in knowledge.documents]
    product_content = json.dumps(documents, ensure_ascii=False, indent=2)
    prompt = PROMPT_TEMPLATE.format(content=product_content)
    result = await llm.call_llm(llm_conf, prompt)
    if result is None:
        return None
    faqs = schema.Faq.from_llm_result(result)
    return faqs


@celery_task
async def extract_and_save_single_product_faqs(product_url: str, clear_existing: bool = False):
    """
    为指定商品挖掘 FAQ
    """
    existing_faqs = await operations.get_product_faqs(product_url)
    if existing_faqs and not clear_existing:
        logger.info(f"Product faqs already exist for {product_url}, skipping extraction.")
        return
    elif existing_faqs and clear_existing:
        logger.info(f"Clearing existing selling points for {product_url}.")
        await operations.delete_product_faqs(product_url)
    faqs = await extract_single_product_faqs(product_url)
    if not faqs:
        return
    await operations.save_product_faqs(product_url, faqs)


@celery_task
async def extract_and_save_all_product_faqs(domain: str) -> None:
    """
    为指定站点的所有商品挖掘 FAQ（遵循挖掘策略）
    """
    # 根据策略获取需要处理的产品URL列表
    product_urls = await operations.get_products_by_strategy(domain, 'product_faq')

    if not product_urls:
        logger.info(f"No products to process for FAQs in domain {domain}")
        return

    tasks = [extract_and_save_single_product_faqs(url) for url in product_urls]
    await CeleryTaskTqdmAsync.gather(*tasks, desc=f"Generating FAQs for {len(product_urls)} products in {domain}")


@celery_task
async def sync_product_faqs_to_knowledge_base(domain: str):
    """
    将标注为 ready 状态的产品 FAQ 同步到线上知识库
    """
    faqs = await operations.list_faqs_by_status(domain, schema.SellingPointStatus.READY,
                                                # 只要是标注为 ready 的都同步到 shopify-knowledge，即使爬取策略中没包含对应商品
                                                should_match_crawl_strategy=False)
    if not faqs:
        logger.warning(f"No FAQs found for store {domain} with status READY.")
        return

    knowledge_client = shopify_knowledge.ShopifyKnowledgeClient.get_client()
    # 每次同步一个商品的所有 faq
    for record in CeleryTaskTqdm(faqs, desc="Syncing FAQs to knowledge base"):
        req = shopify_knowledge.UpdateProductFaqsReq(
            storeDomain=record.shopify_domain,
            source="PRODUCT_KNOWLEDGE",
            faqList=[]
        )
        for faq in record.faqs:
            req.faqList.append(shopify_knowledge.ProductFaqItem(
                spuId=record.product_id,
                question=faq.question,
                answer=faq.answer,
                externalId=str(record.id)
            ))
        try:
            await knowledge_client.generate_faqs(req)
        except Exception as e:
            logger.opt(exception=e).error(f"failed to sync FAQs of {record.product_url} to knowledge base")
