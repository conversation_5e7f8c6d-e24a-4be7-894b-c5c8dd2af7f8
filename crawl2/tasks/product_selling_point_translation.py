import json

from jinja2 import Template
from loguru import logger
from tortoise.exceptions import DoesNotExist

from crawl2.clients import llm
from crawl2.db import models
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync

llm_config = llm.LLMConfig("usq", "qwen2.5-32b-chat-fp8-offline", 0, 2000, enable_thinking=False)


prompt_template = Template("""
following is a english description of a product selling point, with a format of json object,
please translate it to Chinese, and return the result in the same format json object, leave the keys unchanged,
but translate the values to Chinese.:

the input is:

```json
{{ selling_point }}
```
""")


@celery_task
async def translate_single_product_selling_point(product_url: str) -> None:
    """翻译单个产品的所有卖点"""
    try:
        # 获取产品及其卖点
        product = await models.ProductMetadata.get_or_none(url=product_url)
        if not product:
            logger.warning(f"Product with URL {product_url} not found")
            return
        selling_points = await models.ProductSellingPoint.filter(product=product)

        for sp in selling_points:
            # 翻译卖点名称
            sp_dict = {
                'name': sp.name,
                'description': sp.description,
                'marketing_copies': sp.marketing_copies or []
            }
            if sp.name_chn and sp.description_chn and sp.marketing_copies_chn:
                logger.info(f"Skipping already translated selling point {sp.name} of product {product_url}")
                continue
            prompt = prompt_template.render(selling_point=json.dumps(sp_dict, ensure_ascii=False, indent=2))
            result = await llm.call_llm(llm_config, prompt)
            logger.info(f"Translating selling point {sp.name} of product {product_url} with result: {result}")
            if not result:
                logger.warning(f"No translation result for selling point {sp.name} of product {product_url}")
                continue
            if not isinstance(result, dict):
                logger.warning(f"Unexpected translation result type for selling point {sp.name} of {product_url}")
                continue
            if 'name' in result and isinstance(result['name'], str):
                sp.name_chn = result['name']
            if 'description' in result and isinstance(result['description'], str):
                sp.description_chn = result['description']
            if 'marketing_copies' in result and isinstance(result['marketing_copies'], list):
                sp.marketing_copies_chn = [copy for copy in result['marketing_copies'] if isinstance(copy, str)]
            await sp.save()
    except DoesNotExist:
        logger.warning(f"Product with URL {product_url} not found")
    except Exception as e:
        logger.error(f"Error translating selling points for {product_url}: {str(e)}")


@celery_task
async def translate_all_product_selling_points(domain: str) -> None:
    """翻译指定站点的所有产品卖点"""
    try:
        # 获取站点
        site = await models.ShopifySite.get(domain=domain)

        # 获取所有产品
        products = await models.ProductMetadata.filter(site=site)
        translation_tasks = [
            translate_single_product_selling_point(product.url) for product in products
        ]
        await CeleryTaskTqdmAsync.gather(*translation_tasks, desc=f"Translating selling points for {domain}")
    except DoesNotExist:
        logger.warning(f"Site with domain {domain} not found")
    except Exception as e:
        logger.error(f"Error translating selling points for domain {domain}: {str(e)}")


@celery_task
async def translate_selling_points_for_multiple_products(product_urls: list[str]) -> None:
    """翻译多个产品的卖点"""
    translation_tasks = [
        translate_single_product_selling_point(url) for url in product_urls
    ]
    await CeleryTaskTqdmAsync.gather(*translation_tasks, desc="Translating selling points for multiple products")
