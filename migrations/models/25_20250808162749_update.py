from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "shopify_site" ADD "crawl_strategy" JSONB;
        ALTER TABLE "shopify_site" ADD "strategy_updated_at" TIMESTAMPTZ;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "shopify_site" DROP COLUMN "crawl_strategy";
        ALTER TABLE "shopify_site" DROP COLUMN "strategy_updated_at";"""
