"""腾讯云对象存储"""
import base64
import json
from io import BytesIO
from functools import lru_cache

from loguru import logger
from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
from crawl2.config import settings


class CosClient:
    def __init__(self, config: CosConfig, bucket: str):
        self.bucket = bucket
        self.client = CosS3Client(config)

    @classmethod
    @lru_cache
    def get_client(cls):
        return cls(
            config=CosConfig(
                Region=settings.COS_REGION,
                SecretId=settings.COS_SECRET_ID,
                SecretKey=settings.COS_SECRET_KEY
            ),
            bucket=settings.COS_BUCKET,
        )

    @staticmethod
    def normalize_url(page_url: str) -> str:
        if page_url.endswith("/"):
            page_url = page_url[:-1]
        return page_url

    def upload_page_html(self, page_url: str, html_content: str):
        page_url = self.normalize_url(page_url)
        key = f"shopify/site/{page_url}/index.html"
        self.client.put_object(
            Bucket=self.bucket,
            Key=key,
            Body=html_content.encode('utf-8'),
            ContentType='text/html; charset=utf-8'
        )

    def read_page_html(self, page_url: str):
        page_url = self.normalize_url(page_url)
        key = f"shopify/site/{page_url}/index.html"
        try:
            response = self.client.get_object(
                Bucket=self.bucket,
                Key=key
            )
            return response['Body'].get_raw_stream().read().decode('utf-8')
        except Exception as e:
            logger.error(f"read page html error: {e}, page_url: {page_url}, key: {key}")
            return None

    def get_page_html_url(self, page_url: str):
        page_url = self.normalize_url(page_url)
        key = f"shopify/site/{page_url}/index.html"
        return self.client.get_object_url(Bucket=self.bucket, Key=key)

    def upload_page_screenshot(self, page_url: str, screenshot: str):
        page_url = self.normalize_url(page_url)
        img_data = BytesIO(base64.b64decode(screenshot))
        key = f"shopify/site/{page_url}/screenshot.png"
        logger.info(f"upload screenshot to {key}")
        self.client.put_object(
            Bucket=self.bucket,
            Key=key,
            Body=img_data,
            ContentType="image/png",
            ContentDisposition="inline; filename=screenshot.png"
        )

    def read_page_screenshot(self, page_url: str) -> bytes:
        page_url = self.normalize_url(page_url)
        key = f"shopify/site/{page_url}/screenshot.png"
        response = self.client.get_object(
            Bucket=self.bucket,
            Key=key
        )
        return response['Body'].get_raw_stream().read()

    def get_page_screenshot_url(self, page_url: str):
        page_url = self.normalize_url(page_url)
        key = f"shopify/site/{page_url}/screenshot.png"
        return self.client.get_object_url(Bucket=self.bucket, Key=key)

    def dump_page_elements(self, domain: str, dump_data: list, version: str):
        def json_default(obj):
            from datetime import datetime, date
            if isinstance(obj, (datetime, date)):
                return obj.isoformat()
            raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")

        key = f"shopify/site/{domain}/dump/{version}.json"
        self.client.put_object(
            Bucket=self.bucket,
            Key=key,
            Body=json.dumps(dump_data, default=json_default).encode('utf-8'),
            ContentType='application/json'
        )

    def load_page_elements(self, domain: str, version: str):
        key = f"shopify/site/{domain}/dump/{version}.json"
        try:
            response = self.client.get_object(Bucket=self.bucket, Key=key)
            return json.loads(response['Body'].get_raw_stream().read().decode('utf-8'))
        except Exception as e:
            logger.error(f"read page element bound box error: {e}, domain: {domain}, version: {version} key: {key}")
            return None
