<template>
  <div class="space-y-4">
    <!-- 统计指标网格 -->
    <div class="grid grid-cols-5 gap-4">
      <div 
        v-for="(stat, key) in statItems" 
        :key="key"
        @click="$emit('view-history', { type: stat.key, value: stat.value, label: stat.label })"
        class="text-center cursor-pointer group transition-all duration-200 hover:scale-105 hover:bg-gray-50 rounded-lg p-2"
        :title="`点击查看 ${stat.label} 相关的执行历史`"
      >
        <div class="text-2xl font-bold transition-colors duration-200" :class="stat.colorClass">
          {{ stat.value }}
        </div>
        <div class="text-xs text-gray-500 mt-1 group-hover:text-gray-700">{{ stat.label }}</div>
      </div>
    </div>
    
    <!-- 最近运行信息 -->
    <div v-if="safeStats.last_run_time" class="pt-3 border-t border-gray-200">
      <div 
        @click="$emit('view-history', { type: 'last_run', value: safeStats.last_run_time, label: '最近执行' })"
        class="flex justify-between items-center cursor-pointer group hover:bg-gray-50 rounded-lg p-2 transition-colors duration-200"
        title="点击查看最近执行相关的历史记录"
      >
        <div class="text-sm text-gray-600 group-hover:text-gray-800">
          最近运行: {{ formatLastRunTime(safeStats.last_run_time) }}
        </div>
        <div class="px-3 py-1 rounded-full text-xs font-medium" :class="getStatusClasses(safeStats.last_run_status)">
          {{ getStatusText(safeStats.last_run_status) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  stats: {
    type: Object,
    required: true,
    default: () => ({
      total_runs: 0,
      success_count: 0,
      failed_count: 0,
      running_count: 0,
      pending_count: 0,
      last_run_time: null,
      last_run_status: null
    })
  }
})

// 定义事件
const emit = defineEmits(['view-history'])

// 确保 stats 对象的所有属性都有安全的值
const safeStats = computed(() => {
  const defaultStats = {
    total_runs: 0,
    success_count: 0,
    failed_count: 0,
    running_count: 0,
    pending_count: 0,
    last_run_time: null,
    last_run_status: null
  }
  
  if (!props.stats || typeof props.stats !== 'object') {
    return defaultStats
  }
  
  return {
    ...defaultStats,
    ...props.stats,
    // 确保数值类型安全
    total_runs: Number(props.stats.total_runs) || 0,
    success_count: Number(props.stats.success_count) || 0,
    failed_count: Number(props.stats.failed_count) || 0,
    running_count: Number(props.stats.running_count) || 0,
    pending_count: Number(props.stats.pending_count) || 0
  }
})

// 统计项配置
const statItems = computed(() => [
  { key: 'total_runs', value: safeStats.value.total_runs, label: '总次数', colorClass: 'text-blue-600' },
  { key: 'success_count', value: safeStats.value.success_count, label: '成功', colorClass: 'text-green-600' },
  { key: 'failed_count', value: safeStats.value.failed_count, label: '失败', colorClass: 'text-red-600' },
  { key: 'running_count', value: safeStats.value.running_count, label: '运行中', colorClass: 'text-orange-600' },
  { key: 'pending_count', value: safeStats.value.pending_count, label: '等待中', colorClass: 'text-gray-600' }
])

const formatLastRunTime = (timeStr) => {
  if (!timeStr) return 'N/A'
  
  try {
    const date = new Date(timeStr)
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return 'N/A'
    }
    
    const now = new Date()
    const diffMs = now - date
    
    if (diffMs < 60000) { // 小于1分钟
      return '刚刚'
    } else if (diffMs < 3600000) { // 小于1小时
      return `${Math.floor(diffMs / 60000)}分钟前`
    } else if (diffMs < 86400000) { // 小于1天
      return `${Math.floor(diffMs / 3600000)}小时前`
    } else {
      return date.toLocaleDateString('zh-CN')
    }
  } catch (error) {
    console.warn('Error formatting time:', error, timeStr)
    return 'N/A'
  }
}

const getStatusText = (status) => {
  if (!status || typeof status !== 'string') {
    return 'N/A'
  }
  
  const statusMap = {
    'SUCCESS': '成功',
    'FAILED': '失败',
    'STARTED': '运行中',
    'PENDING': '等待中',
    'CANCELED': '已取消'
  }
  return statusMap[status] || status || 'N/A'
}

const getStatusClasses = (status) => {
  if (!status || typeof status !== 'string') {
    return 'bg-gray-100 text-gray-600'
  }
  
  const classMap = {
    'SUCCESS': 'bg-green-100 text-green-800',
    'FAILED': 'bg-red-100 text-red-800',
    'STARTED': 'bg-orange-100 text-orange-800',
    'PENDING': 'bg-gray-100 text-gray-700',
    'CANCELED': 'bg-red-100 text-red-800'
  }
  return classMap[status] || 'bg-gray-100 text-gray-600'
}
</script>
