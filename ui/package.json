{"name": "ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@bpsmartdesign/rrweb-player-vue3": "^1.0.9", "@rrweb/packer": "2.0.0-alpha.18", "@vueuse/core": "^13.6.0", "ag-grid-community": "^33.3.2", "ag-grid-vue3": "^33.3.2", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "lucide-vue-next": "^0.534.0", "pinia": "^3.0.3", "reka-ui": "^2.4.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "vue": "^3.5.13", "vue-router": "4"}, "devDependencies": {"@tailwindcss/vite": "^4.1.11", "@vitejs/plugin-vue": "^5.2.3", "vite": "^6.3.5"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}