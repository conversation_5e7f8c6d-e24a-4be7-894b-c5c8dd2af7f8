from loguru import logger

from crawl2.tasks.celery_wrap import celery_task
from crawl2.clients import http


@celery_task
async def simple_demo(domain: str):
    """
    用于展示挖掘任务定义与使用的简单 demo.
    """
    logger.info(f'hello there: {domain}')
    meta_url = f"https://{domain}/meta.json"
    response = await http.http_get(meta_url)
    response.raise_for_status()
    logger.info(f'meta.json response: {response.text}')
