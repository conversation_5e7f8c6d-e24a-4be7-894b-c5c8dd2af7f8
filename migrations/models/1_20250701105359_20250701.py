from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "product_scenario_selling_point" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "scenario" VARCHAR(255) NOT NULL,
    "content" TEXT NOT NULL,
    "product_id" INT NOT NULL REFERENCES "product_metadata" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_sce_product_3a744f" ON "product_scenario_selling_point" ("product_id");
COMMENT ON COLUMN "product_scenario_selling_point"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_scenario_selling_point"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_scenario_selling_point"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_scenario_selling_point"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_scenario_selling_point"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_scenario_selling_point"."scenario" IS '场景';
COMMENT ON COLUMN "product_scenario_selling_point"."content" IS '场景卖点内容';
        CREATE TABLE IF NOT EXISTS "product_type_scenarios" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "product_type" VARCHAR(255) NOT NULL,
    "scenarios" JSONB NOT NULL
);
CREATE INDEX IF NOT EXISTS "idx_product_typ_product_9baa1f" ON "product_type_scenarios" ("product_type");
COMMENT ON COLUMN "product_type_scenarios"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_type_scenarios"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_type_scenarios"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_type_scenarios"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_type_scenarios"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_type_scenarios"."product_type" IS '网站展示的字面产品类型';
COMMENT ON COLUMN "product_type_scenarios"."scenarios" IS '产品类型的使用场景';
COMMENT ON TABLE "product_type_scenarios" IS '产品类型的使用场景';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "product_scenario_selling_point";
        DROP TABLE IF EXISTS "product_type_scenarios";"""
