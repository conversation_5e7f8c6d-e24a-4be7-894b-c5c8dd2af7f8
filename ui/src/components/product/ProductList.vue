<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { AgGridVue } from 'ag-grid-vue3'
import { themeAlpine } from 'ag-grid-community'


const props = defineProps({
  products: {
    type: Array,
    required: true,
    default: () => []
  },
  domain: {
    type: String,
    required: true
  }
})

const router = useRouter()

// ag-grid 配置
const gridOptions = ref({
  columnDefs: [
    {
      headerName: 'Product ID',
      field: 'product_id',
      width: 120,
      sortable: true,
      filter: true
    },
    {
      headerName: 'Title',
      field: 'title',
      flex: 1,
      sortable: true,
      filter: true,
      cellRenderer: (params) => {
        return `<div class="font-medium text-gray-800">${params.value || 'N/A'}</div>`
      }
    },
    {
      headerName: 'URL',
      field: 'url',
      width: 200,
      sortable: true,
      filter: true,
      cellRenderer: (params) => {
        if (params.value) {
          return `<a href="${params.value}" target="_blank" class="text-blue-600 hover:text-blue-800 underline">${params.value}</a>`
        }
        return 'N/A'
      }
    },
    {
      headerName: 'Price',
      field: 'price',
      width: 100,
      sortable: true,
      filter: true,
      cellRenderer: (params) => {
        if (params.value) {
          return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
          }).format(params.value)
        }
        return 'N/A'
      }
    },
    {
      headerName: 'Type',
      field: 'product_type',
      width: 150,
      sortable: true,
      filter: true
    },
    {
      headerName: 'Actions',
      width: 120,
      sortable: false,
      filter: false,
      cellRenderer: (params) => {
        return `<button class="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors" onclick="window.navigateToProduct('${params.data.product_id}')">View Details</button>`
      }
    }
  ],
  defaultColDef: {
    resizable: true,
    minWidth: 100
  },
  rowData: [],
  pagination: true,
  paginationPageSize: 20,
  domLayout: 'autoHeight',
  theme: themeAlpine
})

// 全局函数，用于 ag-grid 中的按钮点击
window.navigateToProduct = (productId) => {
  router.push(`/sites/${props.domain}/products/${productId}`)
}

// 监听 products 变化，更新表格数据
watch(() => props.products, (newProducts) => {
  if (gridOptions.value.api) {
    gridOptions.value.api.setRowData(newProducts || [])
  }
}, { immediate: true })

onMounted(() => {
  // 初始化表格数据
  if (gridOptions.value.api) {
    gridOptions.value.api.setRowData(props.products || [])
  }
})
</script>

<template>
  <div class="w-full max-w-full min-w-[800px]">
    <div class="ag-theme-alpine w-full h-[600px]">
      <AgGridVue 
        :gridOptions="gridOptions"
        :rowData="products"
        class="w-full h-full"
      />
    </div>
  </div>
</template>
