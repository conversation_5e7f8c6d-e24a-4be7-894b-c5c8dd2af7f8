import random

from loguru import logger

from crawl2 import schema
from crawl2.clients import llm
from crawl2.db import operations
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync

PRODUCT_PROPERTY_SCHEMA_PROMPT_TEMPLATE = '''
You are a product information analyzer. I will provide you with markdown content from a product detail page. Your task is to analyze the content and generate a JSON schema that describes the key filterable properties of this type of product.
If a product has partially related attributes, split them into multiple fields (e.g., split volume into three separate fields: length, width, and height; or split range into two separate fields: (property)LowerBound and (property)UpperBound).

Important Context:
The schema you generate will be used for hard filtering in product search, meaning:
1. Each property must support exact value matching or numeric range filtering
2. Properties with arbitrary text values that can't be enumerated should be excluded
3. Focus on properties that customers commonly use to filter products (e.g., color, material, brand)
4. Do NOT include generic properties such as price, dimensions, weight, or any property whose value is a floating-point number (e.g., price, length, width, height, weight, volume, etc.). Only include the main attributes that distinguish this type of product.

1. Only include properties whose values are one of these types:
   - string: A single value that can be from a fixed set (e.g., "Red")
   - number: A numeric value for range filtering (e.g., number of ports, number of functions)
   - bool: A boolean value (e.g. true, false)
   - array of string: Multiple string values (e.g., available sizes ["S", "M", "L"])
   - array of number: Multiple numeric values (e.g., available options [1, 2, 3])

2. When deciding the property type:
   - Use "string" when each product has a specific string value that can be filtered.
   - Use "number" when each product has a numeric value that can be filtered by range, but do NOT use for price, size, dimensions, or weight.
   - Use "bool" when each product has a boolean value
   - Use "array" with "string" type when products can have multiple string values
   - Use "array" with "number" type when products can have multiple numeric values

3. DO NOT include properties that:
   - Have free-form text values
   - Have unique or non-standardized values
   - Cannot be used for exact matching or range filtering
   - Are generic properties such as price, dimensions, weight, or any property whose value is a floating-point number (e.g., price, length, width, height, weight, volume, etc.)

Input will be markdown content from a product page. Here's the content:
{markdown_content}

Strictly generate JSON dict format wrapped in ```json ``` ， following this format:

Example output:
```json
{{
  "properties": {{
    "color": {{
      "type": "array",
      "items": {{
        "type": "string"
      }},
      "description": "Available color options for filtering"
    }},
    "heatingZonesNumber": {{
      "type": "number",
      "description": "Number of heating zones for filtering"
    }},
    "isWaterproof": {{
      "type": "bool",
      "description": "Whether the product is waterproof"
    }}
  }}
}}
```

Please analyze the provided content and generate a similar JSON schema. Remember:
1. Every property must support exact value matching or range filtering
2. String values must always be from a fixed enumeration set
3. Only include properties that can be effectively used for hard filtering
4. Do NOT include price, size, dimensions, weight, or any floating-point value property. Focus on the main attributes of this product type.
'''

EXPAND_PRODUCT_PROPERTY_SCHEMA_PROMPT_TEMPLATE = '''
You are a product information analyzer. I will provide you with:
1. The current property schema (in JSON format) for a certain product type.
2. Markdown content from a product detail page of the same product type.

Your task is to:
- Analyze the provided markdown content and compare it with the existing property schema.
- Identify any additional key filterable properties that are present in the markdown but missing from the schema.
- Expand and improve the property schema by adding these new properties, following the same schema format and rules.
- If you find that an existing property can be improved (e.g., more precise type, better description), update it accordingly.
- Output the complete, updated property schema in JSON format.

Important Context and Rules:
- The schema is used for hard filtering in product search.
- Each property must support exact value matching or numeric range filtering.
- Do NOT include generic properties such as price, dimensions, weight, or any property whose value is a floating-point number (e.g., price, length, width, height, weight, volume, etc.).
- Only include properties whose values are one of these types:
  - string: A single value from a fixed set (e.g., "Red")
  - number: A numeric value for range filtering (not for price, size, dimensions, or weight)
  - bool: Boolean value (true/false)
  - array of string: Multiple string values (e.g., available sizes ["S", "M", "L"])
  - array of number: Multiple numeric values (e.g., available options [1, 2, 3])
- Do NOT include properties with free-form text, unique or non-standardized values, or those that cannot be used for exact matching or range filtering.
- Focus on properties that customers commonly use to filter products (e.g., color, material, brand).

Input:
Current property schema:
```json
{existing_properties}
```

Product detail page content:
{markdown_content}

Output:
Strictly generate the complete, updated property schema in JSON dict format wrapped in ```json ```, following this format:

Example output:
```json
{{
  "properties": {{
    "color": {{
      "type": "array",
      "items": {{
        "type": "string"
      }},
      "description": "Available color options for filtering"
    }},
    "heatingZonesNumber": {{
      "type": "number",
      "description": "Number of heating zones for filtering"
    }},
    "isWaterproof": {{
      "type": "bool",
      "description": "Whether the product is waterproof"
    }},
    "newProperty": {{
      "type": "string",
      "description": "Description of the new property"
    }}
  }}
}}
```

Remember:
- Output the full, updated property schema (not just the new properties).
- Do NOT include price, size, dimensions, weight, or any floating-point value property.
- Only include properties that can be effectively used for hard filtering.
'''

llm_conf = llm.LLMConfig('usq', 'qwen2.5-32b-chat-fp8-offline', 0, 5000)


async def _extract_the_first_product_pv_schema(markdown: str) -> dict:
    prompt = PRODUCT_PROPERTY_SCHEMA_PROMPT_TEMPLATE.format(markdown_content=markdown)
    logger.info(f"Extracting product pv schema with prompt: {prompt}")
    result = await llm.call_llm(llm_conf, prompt)
    logger.info(f"Extracted product pv schema with result: {result}")
    if result is None:
        return {}
    properties = result.get('properties', {})
    return properties


async def _expand_product_pv_schema(markdown: str, existing_properties: dict) -> dict:
    prompt = EXPAND_PRODUCT_PROPERTY_SCHEMA_PROMPT_TEMPLATE.format(markdown_content=markdown, existing_properties=existing_properties)
    logger.info(f"Expanding product pv schema with prompt: {prompt}")
    result = await llm.call_llm(llm_conf, prompt)
    logger.info(f"Expanded product pv schema with result: {result}")
    if result is None:
        return {}
    properties = result.get('properties', {})
    return properties


async def extract_product_pv_schema(domain: str, product_type: str, top_n: int = 3) -> schema.ProductTypePvSchema | None:   # NOQA
    products = await operations.filter_product_metadata(domain, product_type=product_type)
    products = random.sample(products, min(top_n, len(products)))
    markdown_list = []
    for product in products:
        knowledge = await product.product_knowledge.order_by('-id').first()
        if knowledge:
            markdown_list.append(knowledge.markdown_content)
    if not markdown_list:
        logger.warning(f"No markdown content found for product type {product_type} in domain {domain}")
        return None
    properties = await _extract_the_first_product_pv_schema(markdown_list[0])
    for markdown in markdown_list[1:]:
        properties = await _expand_product_pv_schema(markdown, properties)
    return schema.ProductTypePvSchema(domain=domain, product_type=product_type, properties=properties)

@celery_task
async def extract_all_product_type_pv_schema(domain: str, clear_existing: bool = False):
    """
    挖掘指定站点所有商品类型的产品属性 schema
    """
    all_products = await operations.list_product_metadata(domain)
    product_types = set(p.product_type for p in all_products)
    tasks = [extract_and_save_single_product_type_pv_schema(domain, product_type, clear_existing) for product_type in product_types]
    await CeleryTaskTqdmAsync.gather(*tasks, desc="Extracting product pv schema")

@celery_task
async def extract_and_save_single_product_type_pv_schema(domain: str, product_type: str, clear_existing: bool = False):
    """
    挖掘单个商品类型的产品属性 schema
    """
    site = await operations.get_site(domain)
    existed = await operations.get_product_type_pv_schema(domain, product_type)
    if existed and not clear_existing:
        logger.info(f"product type pv schema already exist for {product_type}, skipping extraction.")
        return
    elif existed and clear_existing:
        logger.info(f"clearing existing product type pv schema for {product_type} in {domain}")
        await operations.delete_product_type_pv_schema(site.id, product_type)
    result = await extract_product_pv_schema(domain, product_type, top_n=3)
    if result:
        await operations.save_product_type_pv_schema(domain, product_type, result)
