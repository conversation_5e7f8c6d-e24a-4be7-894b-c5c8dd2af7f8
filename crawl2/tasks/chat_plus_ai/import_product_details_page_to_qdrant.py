import asyncio
from typing import Dict, Any, Optional
from datetime import datetime
from loguru import logger
import httpx
from qdrant_client.http.models import Filter, FieldCondition, MatchValue, OrderBy

from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.product_knowledge import extract_product_page_knowledge
from crawl2.qdrant_index.lechat_product_knowledge_point_index import ProductDetailPoint
from crawl2.tasks.chat_plus_ai.utils import (
    get_item_info,
    get_item_url_only,
    get_item_name_only,
    ProductDetailCrawler,
    merge_and_deduplicate_image_url,
    remove_image_url_query_params,
    llm_config,
    batch_index_points
)
from crawl2.config import settings
from crawl2.qdrant_index.lechat_product_knowledge_point_index import ProductDetailPointIndex, IndexConfig
from crawl2.clients.llm import get_llm
from crawl2.utils import parse_llm_json_response
from crawl2.tasks.chat_plus_ai.prompts import ORC_PROMPT

# 全局常量：数据源标识
SOURCE_PRODUCT_DETAIL_PAGE = 'product_detail_page'
SOURCE_PRODUCT_DETAIL_PAGE_IMAGE_OCR = 'product_detail_page_image_ocr'

# 全局并发限制：限制同时执行的crawl_product_detail_with_update任务数量
CRAWL_PRODUCT_DETAIL_SEMAPHORE = asyncio.Semaphore(settings.TASK_CONCURRENCY)


def get_qdrant_indexer() -> ProductDetailPointIndex:
    """获取 Qdrant 索引器实例"""
    index_config = IndexConfig(
        collection_name=settings.LECHAT_PRODUCT_PAGE_KNOWLEDGE_COLLECTION,
        embedding_client_url=settings.EMBEDDING_API_URL,
        sparse_embedding_client_url=settings.SPARSE_EMBEDDING_API_URL,
        qdrant_url=settings.QDRANT_URL,
        vector_size=settings.QDRANT_VECTOR_SIZE
    )
    return ProductDetailPointIndex(index_config)


async def ocr_image_extract_knowledge_with_llm(image_url: str, org_id: str):
    """
    直接从图片URL调用大模型抽取知识
    Args:
        image_url: 图片URL
        org_id: org_id
    Returns:
        抽取的知识列表，格式为 [{"topic": "xxx", "title": "xxx", "content": "xxx"}]
    """
    try:
        logger.info(f"ocr_image: {image_url}")
        async with get_llm(llm_config) as llm:
            # noinspection PyTypeChecker
            response = await llm.chat.completions.create(
                model=llm_config.model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "image_url", "image_url": {"url": image_url}},
                            {"type": "text", "text": ORC_PROMPT},
                        ],
                    }
                ],
                max_tokens=llm_config.max_tokens,
                temperature=llm_config.temperature,
                extra_body={"stop_token_ids": [151645, 151643]},
                extra_headers={"x-litellm-tags": f"tenant={org_id}"},
            )
        answer = response.choices[0].message.content

        # 使用通用的LLM JSON响应解析函数
        try:
            result = parse_llm_json_response(answer)
            # 确保结果是列表格式
            if isinstance(result, list):
                logger.info(f"成功从图片提取到 {len(result)} 个知识点: {image_url}")
                return result
            else:
                logger.warning(f"解析结果不是列表格式: {result}")
                return []
        except Exception as e:
            logger.error(f"解析JSON失败: {e}, 原始响应: {answer}")
            return []

    except Exception as e:
        logger.error(f"OCR图片知识提取失败: {image_url}, 错误: {e}")
        return []


def _post_process_product_detail_points(detail_points: list) -> list:
    """
    对ProductDetailPoint列表进行后处理，去除重复和无效内容
    Args:
        detail_points: ProductDetailPoint列表
    Returns:
        处理后的ProductDetailPoint列表
    """
    if not detail_points:
        return []

    # 用于去重的集合
    seen_items = set()
    processed_points = []

    for point in detail_points:
        title = getattr(point, 'title', '')
        content = getattr(point, 'content', '')

        # 1. 过滤空内容
        if not content or not content.strip():
            continue

        # 2. 过滤title和content完全相同的知识
        if title and title.strip() == content.strip():
            continue

        # 3. 去重：基于title+content的组合
        item_key = f"{title}|{content}"
        if item_key in seen_items:
            continue

        seen_items.add(item_key)

        # 4. 添加到处理后的列表
        processed_points.append(point)

    return processed_points


def _extract_image_urls(item_info, knowledge: Dict[str, Any]) -> list:
    """
    提取图片URL列表
    Args:
        item_info: 商品信息对象
        knowledge: 商品知识数据
    Returns:
        合并去重后的图片URL列表
    """
    if item_info is None:
        image_url = []
    else:
        image_url = list(item_info.image_url)

    image_url_api = knowledge.get('image_url', [])

    # 先对每个URL进行查询参数清理
    cleaned_image_url = [remove_image_url_query_params(i) for i in image_url]
    cleaned_image_url_api = [remove_image_url_query_params(i) for i in image_url_api]

    # 再进行合并去重操作
    image_url_all = merge_and_deduplicate_image_url(cleaned_image_url, cleaned_image_url_api)

    return image_url_all


async def _check_existing_data(store_domain: str, product_id: str) -> Optional[Dict[str, Any]]:
    """
    检查 Qdrant 中是否存在指定 store_domain 和 product_id 的数据
    Args:
        store_domain: 店铺域名
        product_id: 商品ID
    Returns:
        如果存在返回包含 product_page、existing_image_urls 和 has_existing_ocr_data 的字典，否则返回 None
    """
    try:
        indexer = get_qdrant_indexer()

        # 查询markdown数据获取页面内容（按创建时间倒序，获取最新数据）
        markdown_result = await indexer.qdrant.scroll(
            collection_name=indexer.config.collection_name,
            scroll_filter=Filter(
                must=[
                    FieldCondition(key="store_domain", match=MatchValue(value=store_domain)),
                    FieldCondition(key="product_id", match=MatchValue(value=product_id)),
                    FieldCondition(key="source", match=MatchValue(value=SOURCE_PRODUCT_DETAIL_PAGE))
                ]
            ),
            limit=1,
            with_payload=True,
            with_vectors=False,
            order_by=OrderBy(key="created_at", direction="desc")
        )

        # 检查是否存在OCR数据（按创建时间倒序，获取最新数据）
        ocr_result = await indexer.qdrant.scroll(
            collection_name=indexer.config.collection_name,
            scroll_filter=Filter(
                must=[
                    FieldCondition(key="store_domain", match=MatchValue(value=store_domain)),
                    FieldCondition(key="product_id", match=MatchValue(value=product_id)),
                    FieldCondition(key="source", match=MatchValue(value=SOURCE_PRODUCT_DETAIL_PAGE_IMAGE_OCR))
                ]
            ),
            limit=1,
            with_payload=True,
            with_vectors=False,
            order_by=OrderBy(key="created_at", direction="desc")
        )
        has_existing_ocr_data = len(ocr_result[0]) > 0

        # 如果没有markdown数据，查询OCR数据获取图片URL
        if not markdown_result[0]:
            if has_existing_ocr_data:
                # 只有OCR数据，从中获取图片URL
                ocr_payload = ocr_result[0][0].payload
                return {
                    'product_page': '',
                    'existing_image_urls': ocr_payload.get('image_urls', []) or [],
                    'has_existing_ocr_data': True  # 标记有OCR数据
                }
            else:
                # 没有任何数据，返回 None
                logger.info(f"没有找到任何数据: {store_domain}/{product_id}")
                return None

        # 有markdown数据，从中获取所有信息
        markdown_payload = markdown_result[0][0].payload
        return {
            'product_page': markdown_payload.get('product_page', ''),
            'existing_image_urls': markdown_payload.get('image_urls', []) or [],
            'has_existing_ocr_data': has_existing_ocr_data  # 标记是否有OCR数据
        }

    except Exception as e:
        logger.error(f"检查现有数据失败: {e}")
        return None


def _compare_data(old_data: Dict[str, Any], new_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    比较新旧数据是否有更新
    Args:
        old_data: 旧数据，包含 product_page 和 existing_image_urls
        new_data: 新数据，包含 product_page、image_urls 和 enable_image_ocr
    Returns:
        包含更新状态和详细变化信息的字典
    """
    # 比较 product_page (markdown)
    old_page = old_data.get('product_page', '')
    new_page = new_data.get('product_page', '')
    page_updated = old_page != new_page

    # 比较图片OCR相关状态
    old_image_urls = set(old_data.get('existing_image_urls', []))
    new_enable_image_ocr = new_data.get('enable_image_ocr', False)
    new_image_urls = set(new_data.get('image_urls', []))
    # 检查是否有OCR数据存在（通过检查是否有OCR source的数据来判断）
    has_existing_ocr_data = old_data.get('has_existing_ocr_data', False)

    # 图片URL变化分析
    image_ocr_changes = {
        'should_disable_ocr': not new_enable_image_ocr,  # 不启用OCR就删除所有OCR数据
        'urls_to_delete': [],  # 需要删除的URL
        'urls_to_add': [],  # 需要新增的URL
        'urls_unchanged': []  # 保持不变的URL
    }

    if not new_enable_image_ocr:
        # 不启用OCR，标记删除所有OCR数据（删除操作是幂等的）
        pass
    else:
        # 启用OCR，进行精细的URL比较
        # 如果之前没有OCR数据但现在启用OCR，则所有新图片URL都需要处理
        if not has_existing_ocr_data:
            # 之前没有OCR数据，现在启用OCR，所有图片URL都需要处理
            image_ocr_changes['urls_to_add'] = list(new_image_urls)
            image_ocr_changes['urls_unchanged'] = []
        else:
            # 之前有OCR数据，进行精细的URL比较
            image_ocr_changes['urls_to_delete'] = list(old_image_urls - new_image_urls)  # 老的URL中新URL不存在的
            image_ocr_changes['urls_to_add'] = list(new_image_urls - old_image_urls)  # 新增的URL
            image_ocr_changes['urls_unchanged'] = list(old_image_urls & new_image_urls)  # 保持不变的URL

    # 判断是否有图片OCR更新
    image_ocr_updated = (
            image_ocr_changes['should_disable_ocr'] or
            len(image_ocr_changes['urls_to_delete']) > 0 or
            len(image_ocr_changes['urls_to_add']) > 0
    )

    return {
        'page_updated': page_updated,
        'image_ocr_updated': image_ocr_updated,
        'image_ocr_changes': image_ocr_changes
    }


async def _update_product_image_urls(store_domain: str, product_id: str, new_image_urls: list):
    """
    更新 Qdrant 中指定商品的 image_urls 字段
    Args:
        store_domain: 店铺域名
        product_id: 商品ID
        new_image_urls: 新的图片URL列表
    """
    try:
        indexer = get_qdrant_indexer()

        # 查询所有相关的点，使用分页查询确保获取所有符合条件的点
        all_points = []
        offset = None
        limit = 100  # 每次查询100个点

        while True:
            # 构建查询参数
            scroll_params = {
                "collection_name": indexer.config.collection_name,
                "scroll_filter": Filter(
                    must=[
                        FieldCondition(key="store_domain", match=MatchValue(value=store_domain)),
                        FieldCondition(key="product_id", match=MatchValue(value=product_id)),
                    ]
                ),
                "limit": limit,
                "with_payload": True,
                "with_vectors": False  # 不需要向量数据，提高性能
            }

            # 如果有偏移量，添加到查询参数中
            if offset:
                scroll_params["offset"] = offset

            # 执行查询
            scroll_result = await indexer.qdrant.scroll(**scroll_params)

            if not scroll_result[0]:  # 没有更多数据
                break

            all_points.extend(scroll_result[0])

            # 检查是否还有更多数据
            if len(scroll_result[0]) < limit:
                break

            # 更新偏移量，继续查询下一页
            offset = scroll_result[1]  # scroll_result[1] 是下一个偏移量

        if all_points:
            logger.info(f"找到 {len(all_points)} 个点需要更新 image_urls: {store_domain}/{product_id}")

            # 更新每个点的 image_urls
            for point in all_points:
                point_id = point.id
                payload = point.payload
                payload['image_urls'] = new_image_urls

                # 创建新的 ProductDetailPoint 对象
                payload['updated_at'] = datetime.now()  # 更新修改时间
                updated_point = ProductDetailPoint(**payload)

                # 更新点
                await indexer.update_point(point_id, updated_point)

            logger.info(f"成功更新 {len(all_points)} 个点的 image_urls: {store_domain}/{product_id}")
        else:
            logger.warning(f"未找到要更新的数据: {store_domain}/{product_id}")

    except Exception as e:
        logger.error(f"更新 image_urls 失败: {e}")
        raise


async def _extract_knowledge_from_single_image_url(image_url: str, org_id: str, item_url: str,
                                                   product_id: str, knowledge: Dict[str, Any],
                                                   store_domain: str, all_image_urls: list, item_name: str,
                                                   spu_group_id: str = "") -> list:
    """
    从单个图片URL抽取知识并创建 ProductDetailPoint 列表
    Args:
        image_url: 单个图片URL
        org_id: 组织ID
        item_url: 商品URL
        product_id: 商品ID
        knowledge: 商品知识数据
        store_domain: 店铺域名
        all_image_urls: 所有图片URL列表
    Returns:
        ProductDetailPoint 列表
    """
    try:
        # 直接调用大模型抽取知识（优化后的方法）
        new_knowledge = await ocr_image_extract_knowledge_with_llm(image_url, org_id)

        if not new_knowledge or len(new_knowledge) == 0:
            logger.warning(f"从图片中没有提取到知识点: {store_domain}/{product_id}, URL: {image_url}")
            return []

        all_detail_points = []
        for knowledge_item in new_knowledge:
            topic = knowledge_item.get('topic', 'Others')
            title = knowledge_item.get('title', '')
            content = knowledge_item.get('content', '')

            if not isinstance(content, str):
                logger.warning(f"<UNK>: {store_domain}/{product_id}, URL: {image_url}, "
                               f"knowledge_item: {knowledge_item}")
                continue

            if not content.strip():  # 跳过空内容
                continue

            detail_point = ProductDetailPoint(
                org_id=str(org_id),
                url=item_url,
                product_id=str(product_id),
                product_title=item_name,
                product_type='',
                product_tags='',
                product_page='',
                variant_info=None,
                property_values=None,
                product_summary=None,
                store_domain=store_domain,
                title=title,
                content=content,
                label=topic,
                source=SOURCE_PRODUCT_DETAIL_PAGE_IMAGE_OCR,
                image_urls=all_image_urls,
                source_detail=image_url,
                spu_group_id=spu_group_id,
            )
            all_detail_points.append(detail_point)

        logger.info(
            f"从单个图片URL提取到 {len(all_detail_points)} 个知识点: {store_domain}/{product_id}, URL: {image_url}")
        return all_detail_points

    except Exception as e:
        logger.error(f"从单个图片URL抽取知识失败: {store_domain}/{product_id}, URL: {image_url}, 错误: {e}")
        return []


async def _extract_knowledge_and_create_points(content: str, source: str, org_id: str, item_url: str,
                                               product_id: str, knowledge: Dict[str, Any],
                                               store_domain: str, image_urls: list = None, item_name: str = '',
                                               spu_group_id: str = "") -> list:
    """
    抽取知识并创建 ProductDetailPoint 列表
    Args:
        content: 要抽取知识的内容
        source: 数据源标识（如 SOURCE_PRODUCT_DETAIL_PAGE 或 SOURCE_PRODUCT_DETAIL_PAGE_IMAGE_OCR）
        org_id: 组织ID
        item_url: 商品URL
        product_id: 商品ID
        knowledge: 商品知识数据
        store_domain: 店铺域名
        image_urls: 图片URL列表（所有来源都需要填入）
    Returns:
        ProductDetailPoint 列表
    """
    if not content:
        return []

    try:
        extracted_knowledge = await extract_product_page_knowledge(
            content,
            extra_headers={"x-litellm-tags": f"tenant={org_id}"}
        )

        if not extracted_knowledge or len(extracted_knowledge) == 0:
            logger.warning(f"从 {source} 内容中没有提取到知识点: {store_domain}/{product_id}")
            return []

        all_detail_points = []
        for k in extracted_knowledge:
            label = k.topic
            documents = k.documents
            if documents:
                detail_points = [
                    ProductDetailPoint(
                        org_id=str(org_id),
                        url=item_url,
                        product_id=str(product_id),
                        product_title=item_name,
                        product_type='',
                        product_tags='',
                        product_page=content,
                        variant_info=None,
                        property_values=None,
                        product_summary=None,
                        store_domain=store_domain,
                        title=document.title,
                        content=document.content,
                        label=label,
                        source=source,
                        image_urls=image_urls or [],  # 所有来源都填入image_urls
                        source_detail=None,  # markdown内容没有具体的source_detail
                        spu_group_id=spu_group_id,
                    )
                    for document in documents
                ]
                all_detail_points.extend(detail_points)

        logger.info(f"从 {source} 内容中提取到 {len(all_detail_points)} 个知识点: {store_domain}/{product_id}")
        return all_detail_points

    except Exception as e:
        logger.error(f"从 {source} 内容中抽取知识失败: {store_domain}/{product_id}, 错误: {e}")
        return []


async def _batch_insert_points(points: list, store_domain: str, product_id: str) -> bool:
    """
    批量插入知识点到 Qdrant
    Args:
        points: ProductDetailPoint 列表
        store_domain: 店铺域名
        product_id: 商品ID
    Returns:
        是否成功插入
    """
    if not points:
        logger.info(f"没有知识点需要插入: {store_domain}/{product_id}")
        return False

    try:
        indexer = get_qdrant_indexer()
        # await indexer.create_collection()
        await batch_index_points(indexer, points)
        logger.info(f"成功导入qdrant: {store_domain}/{product_id}, 共 {len(points)} 个知识点")
        return True
    except Exception as e:
        logger.error(f"批量插入知识点失败: {store_domain}/{product_id}, 错误: {e}")
        raise


async def _extract_knowledge_from_all_image_urls(image_urls: list, org_id: str, item_url: str,
                                                 product_id: str, knowledge: Dict[str, Any],
                                                 store_domain: str, item_name: str, spu_group_id: str = "") -> list:
    """
    从所有图片URL抽取知识并创建 ProductDetailPoint 列表
    Args:
        image_urls: 图片URL列表
        org_id: 组织ID
        item_url: 商品URL
        product_id: 商品ID
        knowledge: 商品知识数据
        store_domain: 店铺域名
    Returns:
        ProductDetailPoint 列表
    """
    if not image_urls:
        return []

    logger.info(f"开始处理 {len(image_urls)} 张图片的OCR: {store_domain}/{product_id}")

    all_detail_points = []
    for image_url in image_urls:
        ocr_points = await _extract_knowledge_from_single_image_url(
            image_url, org_id, item_url, product_id, knowledge, store_domain, image_urls, item_name, spu_group_id
        )
        all_detail_points.extend(ocr_points)

    # 在所有图片处理完成后进行后处理
    if all_detail_points:
        original_count = len(all_detail_points)
        all_detail_points = _post_process_product_detail_points(all_detail_points)
        logger.info(
            f"知识后处理完成: 原始 {original_count} 个知识点，处理后 {len(all_detail_points)} 个: {store_domain}/{product_id}")

    logger.info(f"完成图片OCR知识抽取，共生成 {len(all_detail_points)} 个知识点: {store_domain}/{product_id}")
    return all_detail_points


async def _handle_image_url_changes(store_domain: str, product_id: str, image_ocr_changes: Dict[str, Any],
                                    org_id: str, item_url: str, knowledge: Dict[str, Any],
                                    all_image_urls: list, item_name: str, spu_group_id: str = "") -> list:
    """
    处理图片URL的变化：删除旧URL数据，添加新URL数据
    Args:
        store_domain: 店铺域名
        product_id: 商品ID
        image_ocr_changes: 图片OCR变化信息
        org_id: 组织ID
        item_url: 商品URL
        knowledge: 商品知识数据
        all_image_urls: 所有图片URL列表
        item_name: 商品名称
        spu_group_id: SPU组ID，如果提供则基于SPU组级别进行删除操作
    Returns:
        新增的ProductDetailPoint列表
    """
    try:
        indexer = get_qdrant_indexer()

        # 1. 删除不再需要的URL数据
        urls_to_delete = image_ocr_changes.get('urls_to_delete', [])
        for url_to_delete in urls_to_delete:
            await indexer.delete_single_product_knowledge_with_source_detail(
                store_domain, product_id, SOURCE_PRODUCT_DETAIL_PAGE_IMAGE_OCR, url_to_delete, spu_group_id
            )
            if spu_group_id:
                logger.info(
                    f"已删除SPU组图片URL数据: group_id={spu_group_id}, {store_domain}/{product_id}, URL: {url_to_delete}")
            else:
                logger.info(f"已删除图片URL数据: {store_domain}/{product_id}, URL: {url_to_delete}")

        # 2. 为新增的URL进行OCR并抽取知识
        urls_to_add = image_ocr_changes.get('urls_to_add', [])
        if urls_to_add:
            # 复用_extract_knowledge_from_all_image_urls方法，它会自动进行后处理
            all_new_points = await _extract_knowledge_from_all_image_urls(
                urls_to_add, org_id, item_url, product_id, knowledge, store_domain, item_name, spu_group_id
            )
        else:
            all_new_points = []

        # 打印保持不变的URL（仅日志记录）
        urls_unchanged = image_ocr_changes.get('urls_unchanged', [])
        if urls_unchanged:
            logger.info(f"保持不变的图片URL: {store_domain}/{product_id}, URLs: {urls_unchanged}")

        return all_new_points

    except Exception as e:
        logger.error(f"处理图片URL变化失败: {store_domain}/{product_id}, 错误: {e}")
        raise


async def _execute_full_extraction_process(org_id: str, store_domain: str, product_id: str,
                                           item_url: str, knowledge: Dict[str, Any], image_urls: list = None,
                                           should_delete_existing: bool = True, item_name: str = '',
                                           spu_group_id: str = "") -> list:
    """
    执行markdown内容的知识抽取流程
    Args:
        org_id: 组织ID
        store_domain: 店铺域名
        product_id: 商品ID
        item_url: 商品URL
        knowledge: 商品知识数据
        image_urls: 图片URL列表
        should_delete_existing: 是否需要删除现有数据，默认为True
    Returns:
        markdown内容抽取的ProductDetailPoint列表
    """
    try:
        indexer = get_qdrant_indexer()

        # 1. 删除现有的数据（仅在需要时执行）
        if should_delete_existing:
            await indexer.delete_single_product_knowledge_with_source(
                store_domain, product_id, SOURCE_PRODUCT_DETAIL_PAGE, spu_group_id
            )
            if spu_group_id:
                logger.info(f"已删除SPU组现有数据: group_id={spu_group_id}, {store_domain}/{product_id}")
            else:
                logger.info(f"已删除现有数据: {store_domain}/{product_id}")

        # 2. 从 markdown 内容抽取知识
        markdown = knowledge.get('description', '')
        markdown_points = []

        if markdown:
            markdown_points = await _extract_knowledge_and_create_points(
                markdown, SOURCE_PRODUCT_DETAIL_PAGE, org_id, item_url, product_id, knowledge,
                store_domain, image_urls, item_name, spu_group_id
            )

        logger.info(f"完成markdown知识抽取流程，共生成 {len(markdown_points)} 个知识点: {store_domain}/{product_id}")
        return markdown_points

    except Exception as e:
        logger.error(f"执行markdown抽取流程失败: {store_domain}/{product_id}, 错误: {e}")
        raise


# 创建全局实例
crawler = ProductDetailCrawler()


async def _handle_markdown_extraction(org_id: str, store_domain: str, product_id: str,
                                      item_url: str, knowledge: Dict[str, Any], image_urls: list,
                                      item_name: str, spu_group_id: str = "") -> list:
    """
    处理markdown内容的知识抽取
    Args:
        org_id: 组织ID
        store_domain: 店铺域名
        product_id: 商品ID
        item_url: 商品URL
        knowledge: 商品知识数据
        image_urls: 图片URL列表
        item_name: 商品名称
        spu_group_id: SPU组ID
    Returns:
        抽取的ProductDetailPoint列表
    """
    try:
        # 执行markdown内容的知识抽取流程
        markdown_points = await _execute_full_extraction_process(
            org_id, store_domain, product_id, item_url, knowledge, image_urls,
            should_delete_existing=True, item_name=item_name, spu_group_id=spu_group_id
        )

        # 批量插入知识点
        if markdown_points:
            await _batch_insert_points(markdown_points, store_domain, product_id)
            logger.info(f"成功处理markdown知识抽取: {store_domain}/{product_id}, 共 {len(markdown_points)} 个知识点")

        return markdown_points

    except Exception as e:
        logger.error(f"处理markdown知识抽取失败: {store_domain}/{product_id}, 错误: {e}")
        raise


async def _handle_image_extraction_with_changes(org_id: str, store_domain: str, product_id: str,
                                                item_url: str, knowledge: Dict[str, Any], image_urls: list,
                                                item_name: str, image_ocr_changes: Dict[str, Any],
                                                spu_group_id: str = "") -> list:
    """
    处理图片知识抽取（带变化检测）
    Args:
        org_id: 组织ID
        store_domain: 店铺域名
        product_id: 商品ID
        item_url: 商品URL
        knowledge: 商品知识数据
        image_urls: 图片URL列表
        item_name: 商品名称
        image_ocr_changes: 图片OCR变化信息
        spu_group_id: SPU组ID
    Returns:
        抽取的ProductDetailPoint列表
    """
    try:
        # 进行精细化处理
        logger.info(f"图片OCR有更新，进行精细化处理: {store_domain}/{product_id}")
        new_points = await _handle_image_url_changes(
            store_domain, product_id, image_ocr_changes,
            org_id, item_url, knowledge, image_urls, item_name, spu_group_id
        )

        # 插入新的知识点
        if new_points:
            await _batch_insert_points(new_points, store_domain, product_id)
            logger.info(f"成功处理图片知识抽取（带变化）: {store_domain}/{product_id}, 共 {len(new_points)} 个知识点")

        return new_points

    except Exception as e:
        logger.error(f"处理图片知识抽取（带变化）失败: {store_domain}/{product_id}, 错误: {e}")
        raise


async def _handle_initial_extraction(org_id: str, store_domain: str, product_id: str,
                                     item_url: str, knowledge: Dict[str, Any], image_urls: list,
                                     item_name: str, spu_group_id: str = "", enable_image_ocr: bool = False) -> None:
    """
    处理初始数据抽取（数据不存在时）
    Args:
        org_id: 组织ID
        store_domain: 店铺域名
        product_id: 商品ID
        item_url: 商品URL
        knowledge: 商品知识数据
        image_urls: 图片URL列表
        item_name: 商品名称
        spu_group_id: SPU组ID
        enable_image_ocr: 是否启用图片OCR
    """
    try:
        logger.info(f"数据不存在，执行完整抽取流程: {store_domain}/{product_id}")

        # 1. 处理markdown内容抽取
        markdown_points = await _execute_full_extraction_process(
            org_id, store_domain, product_id, item_url, knowledge, image_urls,
            should_delete_existing=False, item_name=item_name, spu_group_id=spu_group_id
        )

        # 2. 处理图片OCR抽取（如果启用）
        image_points = []
        if enable_image_ocr and image_urls:
            image_points = await _extract_knowledge_from_all_image_urls(
                image_urls, org_id, item_url, product_id, knowledge, store_domain, item_name, spu_group_id
            )

        # 3. 统一插入所有知识点
        all_points = markdown_points + image_points
        if all_points:
            await _batch_insert_points(all_points, store_domain, product_id)
            logger.info(f"完成初始数据抽取: {store_domain}/{product_id}, 共 {len(all_points)} 个知识点")

    except Exception as e:
        logger.error(f"处理初始数据抽取失败: {store_domain}/{product_id}, 错误: {e}")
        raise


async def _handle_data_update(org_id: str, store_domain: str, product_id: str,
                              item_url: str, knowledge: Dict[str, Any], image_urls: list,
                              item_name: str, existing_data: Dict[str, Any],
                              spu_group_id: str = "", enable_image_ocr: bool = False) -> None:
    """
    处理数据更新（数据存在时）
    Args:
        org_id: 组织ID
        store_domain: 店铺域名
        product_id: 商品ID
        item_url: 商品URL
        knowledge: 商品知识数据
        image_urls: 图片URL列表
        item_name: 商品名称
        existing_data: 现有数据
        spu_group_id: SPU组ID
        enable_image_ocr: 是否启用图片OCR
    """
    try:
        logger.info(f"数据存在，检查更新: {store_domain}/{product_id}")

        markdown = knowledge.get('description', '')
        new_data = {
            'product_page': markdown,
            'image_urls': image_urls,
            'enable_image_ocr': enable_image_ocr
        }

        update_status = _compare_data(existing_data, new_data)
        logger.info(f"update_status: {update_status}")

        # 判断更新策略
        page_updated = update_status['page_updated']
        image_ocr_updated = update_status['image_ocr_updated']
        image_ocr_changes = update_status['image_ocr_changes']

        # 处理更新策略
        # 1. 处理页面更新
        if page_updated:
            logger.info(f"页面内容有更新，执行完整抽取流程: {store_domain}/{product_id}")
            await _handle_markdown_extraction(
                org_id, store_domain, product_id, item_url, knowledge, image_urls,
                item_name, spu_group_id
            )

        # 2. 单独处理图片更新
        if image_ocr_updated:
            if not enable_image_ocr:
                # 禁用OCR，删除所有OCR数据
                logger.info(f"禁用图片OCR，删除所有OCR数据: {store_domain}/{product_id}")
                indexer = get_qdrant_indexer()
                await indexer.delete_single_product_knowledge_with_source(
                    store_domain, product_id, SOURCE_PRODUCT_DETAIL_PAGE_IMAGE_OCR
                )
            else:
                # 启用OCR且有更新，进行精细化处理
                await _handle_image_extraction_with_changes(
                    org_id, store_domain, product_id, item_url, knowledge, image_urls,
                    item_name, image_ocr_changes, spu_group_id
                )

        # 检查图片URL是否真正发生变化，且没有执行page_updated操作
        old_image_urls = set(existing_data.get('existing_image_urls', []))
        new_image_urls_set = set(image_urls or [])
        image_urls_changed = old_image_urls != new_image_urls_set
        logger.info(f"image_urls_changed: {image_urls_changed}")
        if image_urls_changed and not page_updated and image_urls:
            # 只有在图片URL真正变化且没有执行page_updated操作时才更新image_urls
            await _update_product_image_urls(store_domain, product_id, image_urls)
            logger.info(f"图片URL发生变化，已同步更新 image_urls: {store_domain}/{product_id}")

        # 3. 检查是否没有任何更新
        if not page_updated and not image_ocr_updated:
            logger.info(f"没有数据更新，跳过操作: {store_domain}/{product_id}")

    except Exception as e:
        logger.error(f"处理数据更新失败: {store_domain}/{product_id}, 错误: {e}")
        raise


@celery_task
async def crawl_product_detail_with_update(org_id: str, platform: str, region: str, external_store_id: str,
                                           product_id: str,
                                           spu_group_id: str = "",
                                           enable_image_ocr: bool = False):
    """
    爬取商品详情页并进行更新检查（支持 TikTok、Lazada、Shopee）
    :param org_id: org_id
    :param platform: 平台名称
    :param external_store_id: 外部店铺ID
    :param product_id: 商品ID
    :param region: 区域
    :param enable_image_ocr: 是否开启图片OCR识别，默认开启
    :param spu_group_id: SPU组ID，默认为空字符串
    """
    async with CRAWL_PRODUCT_DETAIL_SEMAPHORE:
        try:
            # enable_image_ocr = False
            # 拼接 store_domain
            spu_group_id = ''
            product_id = str(product_id)
            store_domain = f"{platform}-{region}-{external_store_id}".lower()
            logger.info(f"开始crawl_product_detail_with_update任务: {org_id} {store_domain} {product_id}")

            # 1. 拉取详情页信息
            item_info = get_item_info(platform, region, external_store_id, product_id, need_refresh=True)

            # 获取item_url和item_name
            item_url = get_item_url_only(item_info)
            item_name = get_item_name_only(item_info)

            knowledge = await crawler.crawl_product_knowledge(external_store_id, product_id, platform, region)

            # 2. 提取图片URL
            image_urls = _extract_image_urls(item_info, knowledge)

            # 3. 检查 Qdrant 中是否存在数据
            existing_data = await _check_existing_data(store_domain, product_id)
            logger.info(f"existing_data: {existing_data}")

            if existing_data is None:
                # 数据不存在，执行完整的抽取流程
                await _handle_initial_extraction(org_id, store_domain, product_id, item_url, knowledge,
                                                 image_urls, item_name, spu_group_id, enable_image_ocr)

            else:
                # 数据存在，比较是否有更新
                await _handle_data_update(org_id, store_domain, product_id, item_url, knowledge,
                                          image_urls, item_name, existing_data, spu_group_id, enable_image_ocr)

        except Exception as e:
            logger.error(f"crawl_product_detail_with_update任务执行失败: org_id={org_id}, platform={platform}, "
                         f"region={region}, external_store_id={external_store_id}, "
                         f"product_id={product_id}, enable_image_ocr={enable_image_ocr}, error={e}")
            raise


@celery_task
async def crawl_product_detail_with_delete(platform: str, region: str, external_store_id: str, product_id: str):
    """
    删除指定商品在 Qdrant 中的所有数据
    Args:
        platform: 平台名称
        region: 区域
        external_store_id: 外部店铺ID
        product_id: 商品ID
    """
    try:
        # 构造 store_domain
        product_id = str(product_id)
        store_domain = f"{platform}-{region}-{external_store_id}".lower()
        logger.info(f"开始crawl_product_detail_with_delete任务: {store_domain} {product_id}")

        # 获取 Qdrant 索引器
        indexer = get_qdrant_indexer()

        # 删除指定商品的所有数据（不区分source）
        await indexer.delete_single_product_knowledge(store_domain, product_id)

        logger.info(f"成功删除商品数据: {store_domain}/{product_id}")

    except Exception as e:
        logger.error(f"crawl_product_detail_with_delete任务执行失败: platform={platform}, "
                     f"region={region}, external_store_id={external_store_id}, "
                     f"product_id={product_id}, error={e}")
        raise


@celery_task
async def create_collection_with_knowledge_page(collection_name: str):
    """
    创建指定名称的Qdrant collection
    :param collection_name: 要创建的collection名称
    """
    try:
        logger.info(f"开始创建collection: {collection_name}")

        # 获取默认索引器并修改collection名称
        indexer = get_qdrant_indexer()
        indexer.config.collection_name = collection_name

        # 创建collection
        await indexer.create_collection()

        logger.info(f"成功创建collection: {collection_name}")

    except Exception as e:
        logger.error(f"创建collection失败: collection_name={collection_name}, error={e}")
        raise


@celery_task
async def crawl_product_detail_with_spu_group_update(org_id: str, platform: str, region: str, external_store_id: str,
                                                     product_id: str, enable_image_ocr: bool = False):
    """
    爬取SPU组商品详情页并进行更新检查
    :param org_id: org_id
    :param platform: 平台名称
    :param external_store_id: 外部店铺ID
    :param product_id: 商品ID
    :param region: 区域
    :param enable_image_ocr: 是否开启图片OCR识别，默认关闭
    """
    async with CRAWL_PRODUCT_DETAIL_SEMAPHORE:
        try:
            product_id = str(product_id)
            store_domain = f"{platform}-{region}-{external_store_id}".lower()
            logger.info(f"开始crawl_product_detail_with_spu_group_update任务:  {org_id} {store_domain} {product_id}")

            # 1. 获取 SPU 组数据
            spu_group_data = await _get_spu_group_data(product_id, platform, region, external_store_id)
            if not spu_group_data:
                logger.error(f"获取SPU组数据失败: {store_domain} {product_id}")
                return

            group_id = spu_group_data.get('group_id')
            spu_info_list = spu_group_data.get('spu_info_list', [])

            if not group_id or not spu_info_list:
                logger.error(f"SPU组数据无效: group_id={group_id}, spu_info_list={spu_info_list}")
                return

            logger.info(f"获取到SPU组数据: group_id={group_id}, spu_count={len(spu_info_list)}")

            # 2. 检查历史数据
            main_product_id = await _check_spu_group_existing_data(group_id)

            if main_product_id is None:
                # 3. 首次处理（无历史数据）
                logger.info(f"无历史数据，执行首次处理: group_id={group_id}")
                await _handle_initial_spu_group_processing(
                    org_id, store_domain, group_id, spu_info_list, enable_image_ocr
                )
            else:
                # 4. 历史数据存在时的主商品检查
                logger.info(f"存在历史数据，主商品ID: {main_product_id}")

                # 检查主商品是否在当前SPU组中
                if any(spu.get('spu_id') == main_product_id for spu in spu_info_list):
                    # 5. 主商品存在时的处理
                    await _handle_existing_spu_group_processing(
                        org_id, store_domain, group_id, spu_info_list, main_product_id, enable_image_ocr
                    )
                else:
                    # 主商品不在组内，删除历史数据并重新处理
                    logger.info(f"主商品不在当前SPU组中，删除历史数据: group_id={group_id}")
                    await _delete_spu_group_data(group_id)
                    await _handle_initial_spu_group_processing(
                        org_id, store_domain, group_id, spu_info_list, enable_image_ocr
                    )

        except Exception as e:
            logger.error(
                f"crawl_product_detail_with_spu_group_update任务执行失败: org_id={org_id}, platform={platform}, "
                f"region={region}, external_store_id={external_store_id}, "
                f"product_id={product_id}, enable_image_ocr={enable_image_ocr}, error={e}")
            raise


async def _get_spu_group_data(product_id: str, platform: str,
                              region: str, external_store_id: str) -> Optional[Dict[str, Any]]:
    """
    获取SPU组数据
    Args:
        product_id: 商品ID
        platform: 平台名称
        region: 区域
        external_store_id: 外部店铺ID
    Returns:
        SPU组数据字典，包含group_id和spu_info_list
    """
    try:
        api_url = (f"https://prq-api.leyanbot.com/item-cluster/api/spu-group-cluster/externalSpuId/"
                   f"{product_id}/externalStoreId/{external_store_id}/platform/{platform}/region/{region}")
        logger.info(f"请求SPU组数据API: {api_url}")

        async with httpx.AsyncClient(timeout=30) as client:
            response = await client.get(api_url)
            response.raise_for_status()

            result_data = response.json()
            logger.info(f"SPU组数据API响应: {result_data}")

            spu_info_list = result_data['spuList']
            adapted_spu_info_list = []
            for spu_info in spu_info_list:
                spu_key = spu_info['spuKey']
                store_id = spu_info['storeId']
                spu_parts = spu_key.split('|')
                assert len(spu_parts) == 3
                adapted_spu_info = {
                    'spu_id': spu_parts[0],
                    'platform': spu_parts[1],
                    'region': spu_parts[2],
                    'external_store_id': store_id
                }
                adapted_spu_info_list.append(adapted_spu_info)
            return {
                'group_id': result_data['mainSpuKey'],
                'spu_info_list': adapted_spu_info_list
            }
    except Exception as e:
        logger.error(f"获取SPU组数据失败: {e}")
        return None


async def _check_spu_group_existing_data(group_id: str) -> Optional[str]:
    """
    检查SPU组的历史数据
    Args:
        group_id: SPU组ID
    Returns:
        如果存在返回主商品ID，否则返回None
    """
    try:
        indexer = get_qdrant_indexer()

        # 查询SPU组数据（按创建时间倒序，获取最新数据）
        result = await indexer.qdrant.scroll(
            collection_name=indexer.config.collection_name,
            scroll_filter=Filter(
                must=[
                    FieldCondition(key="spu_group_id", match=MatchValue(value=group_id)),
                ]
            ),
            limit=1,
            with_payload=True,
            with_vectors=False,
            order_by=OrderBy(key="created_at", direction="desc")
        )

        if result[0]:
            # 从最新数据中提取主商品ID
            payload = result[0][0].payload
            return payload.get('product_id')
        else:
            return None

    except Exception as e:
        logger.error(f"检查SPU组历史数据失败: {e}")
        return None


async def _delete_spu_group_data(group_id: str):
    """
    删除SPU组的所有数据
    Args:
        group_id: SPU组ID
    """
    try:
        indexer = get_qdrant_indexer()

        # 查询所有相关的点
        all_points = []
        offset = None
        limit = 100

        while True:
            scroll_params = {
                "collection_name": indexer.config.collection_name,
                "scroll_filter": Filter(
                    must=[
                        FieldCondition(key="spu_group_id", match=MatchValue(value=group_id)),
                    ]
                ),
                "limit": limit,
                "with_payload": False,
                "with_vectors": False
            }

            if offset:
                scroll_params["offset"] = offset

            scroll_result = await indexer.qdrant.scroll(**scroll_params)

            if not scroll_result[0]:
                break

            all_points.extend(scroll_result[0])

            if len(scroll_result[0]) < limit:
                break

            offset = scroll_result[1]

        # 删除所有点
        if all_points:
            point_ids = [point.id for point in all_points]
            await indexer.qdrant.delete(
                collection_name=indexer.config.collection_name,
                points_selector=point_ids
            )
            logger.info(
                f"成功删除SPU组数据: group_id={group_id}, 删除点数={len(point_ids)}")
        else:
            logger.info(f"SPU组无数据可删除: group_id={group_id}")

    except Exception as e:
        logger.error(f"删除SPU组数据失败: {e}")
        raise


async def _handle_initial_spu_group_processing(org_id: str, store_domain: str, group_id: str,
                                               spu_info_list: list, enable_image_ocr: bool):
    """
    处理首次SPU组数据（无历史数据）
    Args:
        org_id: 组织ID
        store_domain: 店铺域名
        group_id: SPU组ID
        spu_info_list: SPU信息列表
        enable_image_ocr: 是否启用图片OCR
    """
    logger.info(f"开始首次SPU组处理: group_id={group_id}, spu_count={len(spu_info_list)}")
    # 遍历SPU列表，找到第一个成功爬取的商品
    for spu_info in spu_info_list:
        spu_id = spu_info.get('spu_id')
        spu_platform = spu_info.get('platform')
        spu_region = spu_info.get('region')
        spu_external_store_id = spu_info.get('external_store_id')

        if not all([spu_id, spu_platform, spu_region, spu_external_store_id]):
            logger.warning(f"SPU信息不完整，跳过: {spu_info}")
            continue

        logger.info(f"尝试处理SPU: {spu_platform}/{spu_region}/{spu_external_store_id}/{spu_id}")

        try:
            # 爬取商品知识
            knowledge = await crawler.crawl_product_knowledge(
                spu_external_store_id, spu_id, spu_platform, spu_region
            )

            # 检查爬取是否成功
            if knowledge.get('crawl_success', False):
                logger.info(f"SPU爬取成功，开始知识抽取: {spu_id}")

                # 获取商品信息
                item_info = get_item_info(spu_platform, spu_region, spu_external_store_id, spu_id, need_refresh=True)
                item_url = get_item_url_only(item_info)
                item_name = get_item_name_only(item_info)

                # 提取图片URL
                image_urls = _extract_image_urls(item_info, knowledge)

                # 复用_handle_initial_extraction函数执行完整抽取
                await _handle_initial_extraction(
                    org_id, store_domain, spu_id, item_url, knowledge, image_urls,
                    item_name, group_id, enable_image_ocr
                )

                logger.info(f"SPU组首次处理完成: group_id={group_id}, 成功处理SPU={spu_id}")
                return  # 成功处理一个SPU后终止

            else:
                logger.warning(f"SPU爬取失败: {spu_id}")
                continue

        except Exception as e:
            logger.error(f"处理SPU失败: {spu_id}, 错误: {e}")
            continue

    # 所有SPU都失败
    logger.error(f"SPU组所有SPU处理均失败: group_id={group_id}")


async def _handle_existing_spu_group_processing(org_id: str, store_domain: str, group_id: str,
                                                spu_info_list: list, main_product_id: str, enable_image_ocr: bool):
    """
    处理已存在的SPU组数据
    Args:
        org_id: 组织ID
        store_domain: 店铺域名
        group_id: SPU组ID
        spu_info_list: SPU信息列表
        main_product_id: 主商品ID
        enable_image_ocr: 是否启用图片OCR
    """
    logger.info(f"处理已存在的SPU组: group_id={group_id}, main_product_id={main_product_id}")

    # 查找主商品信息
    main_spu_info = next((spu for spu in spu_info_list if spu.get('spu_id') == main_product_id), None)

    if not main_spu_info:
        logger.error(f"主商品信息未找到: main_product_id={main_product_id}")
        return

    # 处理主商品
    spu_platform = main_spu_info.get('platform')
    spu_region = main_spu_info.get('region')
    spu_external_store_id = main_spu_info.get('external_store_id')

    try:
        # 拉取主商品最新页面数据
        knowledge = await crawler.crawl_product_knowledge(
            spu_external_store_id, main_product_id, spu_platform, spu_region
        )

        # 检查爬取结果
        if not knowledge.get('crawl_success', False):
            logger.warning(f"主商品爬取失败，删除历史数据: main_product_id={main_product_id}")
            await _delete_spu_group_data(group_id)
            # 重新遍历整个SPU列表
            await _handle_initial_spu_group_processing(
                org_id, store_domain, group_id, spu_info_list, enable_image_ocr
            )
            return

        # 获取商品信息
        item_info = get_item_info(spu_platform, spu_region, spu_external_store_id, main_product_id, need_refresh=True)
        item_url = get_item_url_only(item_info)
        item_name = get_item_name_only(item_info)

        # 提取图片URL
        image_urls = _extract_image_urls(item_info, knowledge)

        # 检查现有数据
        existing_data = await _check_existing_data(store_domain, main_product_id)

        if existing_data is None:
            # 没有现有数据，复用_handle_initial_extraction函数执行完整抽取
            logger.info(f"主商品无现有数据，执行完整抽取: {main_product_id}")
            await _handle_initial_extraction(
                org_id, store_domain, main_product_id, item_url, knowledge, image_urls,
                item_name, group_id, enable_image_ocr
            )
        else:
            # 有现有数据，复用_handle_data_update函数处理更新
            await _handle_data_update(
                org_id, store_domain, main_product_id, item_url, knowledge, image_urls,
                item_name, existing_data, group_id, enable_image_ocr
            )

        logger.info(f"SPU组主商品处理完成: group_id={group_id}, main_product_id={main_product_id}")

    except Exception as e:
        logger.error(f"处理主商品失败: {main_product_id}, 错误: {e}")
        raise
