import json
import re
import asyncio
from typing import Dict, Any

from leyan_proto.oversea.item.overseaim_item_logic_pb2 import BatchGetItemInfoRequest
from loguru import logger
from leyan_proto.wrappers.stubs import GaiaStub
from leyan_proto.oversea.item.overseaim_item_logic_pb2_grpc import ItemServiceStub
from leyan_proto.oversea.apiauth.apiauth_pb2 import (
    CallTikTokApiRequest,
    CallLazadaApiRequest,
    CallShopeeApiRequest
)
from leyan_proto.oversea.apiauth.apiauth_pb2_grpc import ApiAuthServiceStub

from crawl2.clients import llm

item_stub = GaiaStub(ItemServiceStub, "overseaim-item-service", "service")

# LLM 配置用于 OCR
llm_config = llm.LLMConfig(service="usq", model="minicpm-o-2.6", temperature=0.0, max_tokens=512)

# TikTok API 客户端
tiktok_server = GaiaStub(
    ApiAuthServiceStub, "overseaim-apiauth", "tiktok"
)

# TikTok US API 客户端
tiktok_us_server = GaiaStub(
    ApiAuthServiceStub, "overseaim-apiauth", "tiktok-us"
)

# Lazada API 客户端
lazada_server = GaiaStub(
    ApiAuthServiceStub, "lazada-apiauth", "web"
)

# Shopee API 客户端
shopee_server = GaiaStub(
    ApiAuthServiceStub, "overseaim-apiauth", "web"
)


class ProductDetailCrawler:
    """商品详情页爬取器，支持 TikTok、Lazada、Shopee 三个平台"""

    def __init__(self):
        self.platforms = {
            'TIKTOK': self._crawl_tiktok_knowledge,
            'TIKTOK_US': self._crawl_tiktok_us_knowledge,
            'LAZADA': self._crawl_lazada_knowledge,
            'SHOPEE': self._crawl_shopee_knowledge
        }

    async def crawl_product_knowledge(self, external_store_id: str, product_id: str, platform: str,
                                      region: str) -> Dict[str, Any]:
        """
        爬取商品详情页知识
        Args:
            external_store_id: 外部店铺ID
            product_id: 商品ID
            platform: 平台名称 (tiktok/lazada/shopee)
            region: 区域
        Returns:
            包含商品知识的字典，包含crawl_success字段表示爬取是否成功
        """
        if platform not in self.platforms:
            raise ValueError(f"不支持的平台: {platform}，支持的平台: {list(self.platforms.keys())}")

        try:
            if platform == 'LAZADA':
                knowledge = await self.platforms[platform](external_store_id, product_id, region)
            else:
                knowledge = await self.platforms[platform](external_store_id, product_id)

            # 根据description是否为空判断爬取是否成功
            description = knowledge.get('description', '')
            crawl_success = bool(description and description.strip())
            knowledge['crawl_success'] = crawl_success

            if crawl_success:
                logger.info(f"成功爬取 {platform} 平台商品知识: {external_store_id}/{product_id}")
            else:
                logger.warning(f"爬取 {platform} 平台商品知识失败，description为空: {external_store_id}/{product_id}")

            return knowledge
        except Exception as e:
            logger.error(f"爬取 {platform} 平台商品知识失败: {external_store_id}/{product_id}, 错误: {e}")
            raise

    async def _crawl_tiktok_knowledge(self, external_store_id: str, product_id: str) -> Dict[str, Any]:
        """爬取 TikTok 商品知识"""
        try:
            # 获取商品详情
            req = CallTikTokApiRequest(
                external_store_id=external_store_id,
                api_name="/api/products/details",
                method_type="GET",
                param={
                    'store_id': external_store_id,
                    "product_id": product_id
                }
            )
            resp = tiktok_server.call_tiktok_api(req).response

            # 检查响应是否为空
            if not resp:
                logger.warning(f"TikTok API 返回空响应: {external_store_id}/{product_id}")
                return {
                    'platform': 'TIKTOK',
                    'external_store_id': external_store_id,
                    'product_id': product_id,
                    'product_name': '',
                    'description': '',
                    'product_attributes': {},
                    'raw_data': {},
                    'image_url': [],
                }

            try:
                product_info = json.loads(resp)
            except json.JSONDecodeError as e:
                logger.error(f"TikTok API 响应 JSON 解析失败: {resp}, 错误: {e}")
                return {
                    'platform': 'TIKTOK',
                    'external_store_id': external_store_id,
                    'product_id': product_id,
                    'product_name': '',
                    'description': '',
                    'product_attributes': {},
                    'raw_data': {'raw_response': resp},
                    'image_url': [],
                }

            description = self._get_tiktok_description(product_info)

            # 提取商品知识
            knowledge = {
                'platform': 'TIKTOK',
                'external_store_id': external_store_id,
                'product_id': product_id,
                'product_name': self._get_tiktok_product_name(product_info),
                'description': description,
                'product_attributes': self._get_tiktok_product_attributes(product_info),
                'raw_data': product_info,
                'image_url': self._extract_image_urls(description),
            }

            return knowledge

        except Exception as e:
            logger.error(f"TikTok 商品知识爬取失败: {e}")
            raise

    async def _crawl_tiktok_us_knowledge(self, external_store_id: str, product_id: str) -> Dict[str, Any]:
        """爬取 TikTok US 商品知识"""
        try:
            # 获取商品详情
            req = CallTikTokApiRequest(
                external_store_id=external_store_id,
                api_name="/api/products/details",
                method_type="GET",
                param={
                    'store_id': external_store_id,
                    "product_id": product_id
                }
            )
            resp = tiktok_us_server.call_tiktok_api(req).response

            # 检查响应是否为空
            if not resp:
                logger.warning(f"TikTok US API 返回空响应: {external_store_id}/{product_id}")
                return {
                    'platform': 'TIKTOK_US',
                    'external_store_id': external_store_id,
                    'product_id': product_id,
                    'product_name': '',
                    'description': '',
                    'product_attributes': {},
                    'raw_data': {},
                    'image_url': [],
                }

            try:
                product_info = json.loads(resp)
            except json.JSONDecodeError as e:
                logger.error(f"TikTok US API 响应 JSON 解析失败: {resp}, 错误: {e}")
                return {
                    'platform': 'TIKTOK_US',
                    'external_store_id': external_store_id,
                    'product_id': product_id,
                    'product_name': '',
                    'description': '',
                    'product_attributes': {},
                    'raw_data': {'raw_response': resp},
                    'image_url': [],
                }

            description = self._get_tiktok_description(product_info)

            # 提取商品知识
            knowledge = {
                'platform': 'TIKTOK_US',
                'external_store_id': external_store_id,
                'product_id': product_id,
                'product_name': self._get_tiktok_product_name(product_info),
                'description': description,
                'product_attributes': self._get_tiktok_product_attributes(product_info),
                'raw_data': product_info,
                'image_url': self._extract_image_urls(description),
            }

            return knowledge

        except Exception as e:
            logger.error(f"TikTok US 商品知识爬取失败: {e}")
            raise

    async def _crawl_lazada_knowledge(self, external_store_id: str, product_id: str, region: str) -> Dict[str, Any]:
        """爬取 Lazada 商品知识"""
        try:
            # 获取商品详情
            req = CallLazadaApiRequest(
                external_store_id=str(external_store_id),
                api_name="/product/item/get",
                method_type="GET",
                region=region,  # 使用传入的 region
                param={
                    "item_id": product_id
                }
            )
            resp = lazada_server.call_lazada_api(req).response

            # 检查响应是否为空
            if not resp:
                logger.warning(f"Lazada API 返回空响应: {external_store_id}/{product_id}")
                return {
                    'platform': 'LAZADA',
                    'external_store_id': external_store_id,
                    'product_id': product_id,
                    'region': region,
                    'product_name': '',
                    'description': '',
                    'product_attributes': {},
                    'raw_data': {},
                    'image_url': [],
                }

            try:
                product_info = json.loads(resp)
            except json.JSONDecodeError as e:
                logger.error(f"Lazada API 响应 JSON 解析失败: {resp}, 错误: {e}")
                return {
                    'platform': 'LAZADA',
                    'external_store_id': external_store_id,
                    'product_id': product_id,
                    'region': region,
                    'product_name': '',
                    'description': '',
                    'product_attributes': {},
                    'raw_data': {'raw_response': resp},
                    'image_url': [],
                }

            description = self._get_lazada_description(product_info)

            # 提取商品知识
            knowledge = {
                'platform': 'LAZADA',
                'external_store_id': external_store_id,
                'product_id': product_id,
                'region': region,
                'product_name': self._get_lazada_product_name(product_info),
                'description': description,
                'product_attributes': {},
                'raw_data': product_info,
                'image_url': self._extract_image_urls(description),
            }

            return knowledge

        except Exception as e:
            logger.error(f"Lazada 商品知识爬取失败: {e}")
            raise

    async def _crawl_shopee_knowledge(self, external_store_id: str, product_id: str) -> Dict[str, Any]:
        """爬取 Shopee 商品知识"""
        try:
            # 获取商品基础信息
            base_info = self._get_shopee_item_base_info(external_store_id, product_id)
            # 获取商品额外信息
            extra_info = self._get_shopee_item_extra_info(external_store_id, product_id)

            # 检查响应是否为空
            if not base_info.response or not extra_info.response:
                logger.warning(f"Shopee API 返回空响应: {external_store_id}/{product_id}")
                return {
                    'platform': 'SHOPEE',
                    'external_store_id': external_store_id,
                    'product_id': product_id,
                    'product_name': '',
                    'description': '',
                    'product_attributes': {},
                    'raw_data': {},
                    'image_url': [],
                }

            try:
                product_info = {
                    "basic": json.loads(base_info.response),
                    "extra": json.loads(extra_info.response)
                }
            except json.JSONDecodeError as e:
                logger.error(f"Shopee API 响应 JSON 解析失败: base_info={base_info.response}, "
                             f"extra_info={extra_info.response}, 错误: {e}")
                return {
                    'platform': 'SHOPEE',
                    'external_store_id': external_store_id,
                    'product_id': product_id,
                    'product_name': '',
                    'description': '',
                    'product_attributes': {},
                    'raw_data': {'base_response': base_info.response, 'extra_response': extra_info.response},
                    'image_url': [],
                }

            # 提取商品知识
            knowledge = {
                'platform': 'SHOPEE',
                'external_store_id': external_store_id,
                'product_id': product_id,
                'product_name': self._get_shopee_product_name(product_info),
                'description': self._get_shopee_description(product_info),
                'product_attributes': self._get_shopee_product_attributes(product_info),
                'raw_data': product_info,
                'image_url': self._get_shopee_image_url(product_info),
            }

            return knowledge

        except Exception as e:
            logger.error(f"Shopee 商品知识爬取失败: {e}")
            raise

    def _get_tiktok_product_name(self, info: Dict[str, Any]) -> str:
        """提取 TikTok 商品名称"""
        try:
            data = info.get('data')
            if data is None:
                logger.warning("TikTok 商品数据为空")
                return ''
            return data.get('data', '')
        except Exception as e:
            logger.error(f"tiktok 商品名称解析失败: {e}")
            return ''

    def _get_tiktok_description(self, info: Dict[str, Any]) -> str:
        """提取 TikTok 商品描述"""
        try:
            data = info.get('data')
            if data is None:
                logger.warning("TikTok 商品数据为空")
                return ''
            return data.get('description', '')
        except Exception as e:
            logger.error(f"tiktok 详情页描述解析失败: {e}")
            return ''

    def _get_tiktok_product_attributes(self, info: Dict[str, Any]) -> Dict[str, Any]:
        """提取 TikTok 商品属性"""
        try:
            data = info.get('data')
            if data is None:
                logger.warning("TikTok 商品数据为空")
                return {}
            product_attributes = data.get('product_attributes', [])
            result = {}
            for attr in product_attributes:
                name = attr.get('name', '')
                values = attr.get('values', [])
                values_names = [value.get('name', '') for value in values]
                combined_values = ', '.join(values_names)
                if name and combined_values:
                    result[name] = combined_values
            return result
        except Exception as e:
            logger.error(f"tiktok 详情页属性解析失败: {e}")
            return {}

    def _extract_image_urls(self, html_string):
        # 使用正则表达式匹配所有<img>标签的src属性
        if not html_string:
            return []
        pattern = r'<img[^>]*src="([^"]*)"[^>]*>'
        urls = re.findall(pattern, html_string)
        return urls

    def _get_shopee_image_url(self, info):
        try:
            basic = info.get('basic', {})
            response = basic.get('response', {})
            item_list = response.get('item_list', [])
            if not item_list:
                logger.warning("Shopee 商品列表为空")
                return []

            item = item_list[0]
            description_info = item.get('description_info', {})
            extended_description = description_info.get('extended_description', {})
            field_list = extended_description.get('field_list', [])
            field_list_text = [i.get('image_info', {}).get('image_url', '') for i in field_list if
                               i.get('field_type', '') == 'image']
            return field_list_text
        except Exception as e:
            logger.error(f"shoppe 图片url解析失败: {e}")
            return []

    def _get_lazada_product_name(self, info: Dict[str, Any]) -> str:
        """提取 Lazada 商品名称"""
        try:
            if not isinstance(info, dict):
                logger.warning(f"Lazada 商品信息格式错误，info: {info}")
                return ''

            attributes = info.get('attributes', None)
            if attributes is None:
                logger.warning("Lazada 商品属性为空")
                return ''
            return attributes.get('name', '')
        except Exception as e:
            logger.error(f"lazada 商品名称解析失败: {e}")
            return ''

    def _get_lazada_description(self, info: Dict[str, Any]) -> str:
        """提取 Lazada 商品描述"""
        try:
            if not isinstance(info, dict):
                logger.warning(f"Lazada 商品信息格式错误，info: {info}")
                return ''

            attributes = info.get('attributes', None)
            if attributes is None:
                logger.warning("Lazada 商品属性为空")
                return ''
            short_desc = attributes.get('short_description', '')
            full_desc = attributes.get('description', '')
            return f"{short_desc}\n\n{full_desc}".strip()
        except Exception as e:
            logger.error(f"lazada 商品描述解析失败: {e}")
            return ''

    def _get_shopee_item_base_info(self, shop_id: str, item_id: str):
        """获取 Shopee 商品基础信息"""
        req = CallShopeeApiRequest(
            shop_id=str(shop_id),
            url="/api/v2/product/get_item_base_info",
            method_type="GET",
            param={
                "item_id_list": item_id,
            },
            app_type=2
        )
        return shopee_server.call_shopee_api(req)

    def _get_shopee_item_extra_info(self, shop_id: str, item_id: str):
        """获取 Shopee 商品额外信息"""
        req = CallShopeeApiRequest(
            shop_id=str(shop_id),
            url="/api/v2/product/get_item_extra_info",
            method_type="GET",
            param={
                "item_id_list": item_id,
            },
            app_type=2
        )
        return shopee_server.call_shopee_api(req)

    def _get_shopee_product_name(self, info: Dict[str, Any]) -> str:
        """提取 Shopee 商品名称"""
        try:
            basic = info.get('basic', {})
            response = basic.get('response', {})
            item_list = response.get('item_list', [])
            if not item_list:
                logger.warning("Shopee 商品列表为空")
                return ''
            return item_list[0].get('item_name', '')
        except Exception as e:
            logger.error(f"shopee 商品名称解析失败: {e}")
            return ''

    def _get_shopee_description(self, info: Dict[str, Any]) -> str:
        """提取 Shopee 商品描述"""
        try:
            basic = info.get('basic', {})
            response = basic.get('response', {})
            item_list = response.get('item_list', [])
            if not item_list:
                logger.warning("Shopee 商品列表为空")
                return ''

            item = item_list[0]
            description = item.get('description', '')
            description_info = item.get('description_info', {})
            extended_description = description_info.get('extended_description', {})
            field_list = extended_description.get('field_list', [])
            field_list_text = '\n'.join([i.get('text', '') for i in field_list if i.get('field_type', '') == 'text'])
            return f"{description}\n\n{field_list_text}".strip()
        except Exception as e:
            logger.error(f"shopee 商品描述解析失败: {e}")
            return ''

    def _get_shopee_product_attributes(self, info: Dict[str, Any]) -> Dict[str, Any]:
        """提取 Shopee 商品属性"""
        try:
            basic = info.get('basic', {})
            response = basic.get('response', {})
            item_list = response.get('item_list', [])
            if not item_list:
                logger.warning("Shopee 商品列表为空")
                return {}

            item = item_list[0]
            attribute_list = item.get('attribute_list', [])
            result = {}
            for attr in attribute_list:
                name = attr.get('original_attribute_name', '')
                attribute_value_list = attr.get('attribute_value_list', [])
                values_names = [value.get('original_value_name', '') + ' ' + value.get('value_unit', '') for value in
                                attribute_value_list]
                combined_values = ', '.join(values_names)
                if name and combined_values:
                    result[name] = combined_values
            return result
        except Exception as e:
            logger.error(f"shopee 商品属性解析失败: {e}")
            return {}


def merge_and_deduplicate_image_url(list1, list2):
    # 合并两个列表并去重（不保持顺序）
    merged_list = list(set(list1 + list2))
    # 去除空字符串 ''
    result = [item for item in merged_list if item != '']
    return result


def remove_image_url_query_params(image_url: str) -> str:
    """
    去除图片URL中的查询参数（?后面的部分）
    参数:
        url (str): 原始URL
    返回:
        str: 去除查询参数后的URL
    """
    image_url = image_url.split('?')[0] if '?' in image_url else image_url
    image_url = image_url.replace('300:300.jpeg', '1080:1080.jpeg')  # tiktok图片分辨率调整
    return image_url


def remove_ocr_result_invalid_lines(text):
    """
    删除所有以"这些文字出现在图片"开头的行

    参数:
        text (str): 输入文本

    返回:
        str: 处理后的文本
    """
    # 定义需要过滤的开头短语列表
    filter_phrases = ["这些文字", "图片中"]

    lines = text.split('\n')
    filtered_lines = []

    for line in lines:
        # 检查行是否以任何一个过滤短语开头（忽略行首空白）
        line = line.strip()
        should_remove = any(
            line.startswith(phrase)
            for phrase in filter_phrases
        )

        # 如果不匹配任何过滤短语，则保留该行
        if not should_remove and line not in filtered_lines:
            filtered_lines.append(line)

    return '\n'.join(filtered_lines)


async def ocr_image_urls(image_urls: list) -> str:
    """
    对图片URL列表进行OCR识别
    Args:
        image_urls: 图片URL列表
    Returns:
        OCR识别结果，用换行符连接
    """
    ocr_results = []
    for image_url in image_urls:
        try:
            ocr_result = await llm.ocr_image_with_llm(llm_config, image_url, encode_image=False)
            if ocr_result:
                ocr_results.append(remove_ocr_result_invalid_lines(ocr_result))
                logger.info(f"{image_url} ocr-result: {ocr_result}")
        except Exception as e:
            logger.opt(exception=e).error(f"OCR识别失败 for {image_url}: {str(e)}")
    return "\n".join(ocr_results)


def get_item_info(platform, region, external_shop_id, item_id, need_refresh=False):
    try:
        req = BatchGetItemInfoRequest(item_id=[str(item_id)], external_shop_id=str(external_shop_id), platform=platform,
                                      region=region, need_description=need_refresh)
        rsp = item_stub.BatchGetItemInfo(req)
        items = rsp.items
        for item in items:
            if item is not None:
                logger.info(f"{platform}-{region}-{external_shop_id}-{item_id} item_url: {item.item_url}")
            return item
    except Exception as e:
        logger.error(f"{platform}-{region}-{external_shop_id}-{item_id} fail to get item info: {e}")
    return None


def get_item_url_only(item_info) -> str:
    """
    从item_info中获取商品的URL
    Args:
        item_info: 商品信息对象，可能为None
    Returns:
        str: item_url 如果获取失败则返回空字符串
    """
    if item_info is None:
        return ''
    else:
        return item_info.item_url.strip() if item_info.item_url else ''


def get_item_name_only(item_info) -> str:
    """
    从item_info中获取商品的名称
    Args:
        item_info: 商品信息对象，可能为None
    Returns:
        str: item_name 如果获取失败则返回空字符串
    """
    if item_info is None:
        return ''
    else:
        return item_info.item_name.strip() if item_info.item_name else ''


async def batch_index_points(indexer, points: list, batch_size: int = 8, max_retries: int = 3,
                             retry_delay: float = 2.0):
    """
    将points按批次写入Qdrant，带重试机制
    Args:
        indexer: Qdrant索引器
        points: 要索引的点列表
        batch_size: 批次大小，默认8条
        max_retries: 最大重试次数，默认3次
        retry_delay: 重试延迟（秒），默认2秒
    """
    if not points:
        logger.info("batch_index_points: 空列表，跳过导入")
        return

    total = len(points)
    total_batches = (total + batch_size - 1) // batch_size
    logger.info(f"准备批量导入Qdrant: 总数={total}, batch_size={batch_size}, max_retries={max_retries}")

    for i in range(0, total, batch_size):
        batch = points[i:i + batch_size]
        batch_num = i // batch_size + 1
        current_retry_delay = retry_delay

        # 重试逻辑
        for retry_count in range(max_retries + 1):
            try:
                await indexer.index_batch(batch)
                logger.info(f"已导入批次: {batch_num}/{total_batches}, 本批数量={len(batch)}/{total}")
                break  # 成功则跳出重试循环

            except Exception as e:
                error_msg = str(e)
                if retry_count < max_retries:
                    logger.warning(
                        f"批次 {batch_num}/{total_batches} 导入失败 (重试 {retry_count + 1}/{max_retries + 1}): {error_msg}"
                    )
                    if current_retry_delay > 0:
                        logger.info(f"等待 {current_retry_delay} 秒后重试...")
                        await asyncio.sleep(current_retry_delay)
                    # 指数退避：每次重试延迟时间翻倍
                    current_retry_delay *= 2
                else:
                    logger.error(
                        f"批次 {batch_num}/{total_batches} 导入失败，已达到最大重试次数 {max_retries + 1}: {error_msg}"
                    )
                    raise  # 重试次数用完，抛出异常
