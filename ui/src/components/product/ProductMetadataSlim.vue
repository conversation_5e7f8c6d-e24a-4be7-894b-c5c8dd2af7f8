<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const props = defineProps({
  product: {
    type: Object,
    required: true
  },
  domain: {
    type: String,
    required: true
  }
})

const formattedPrice = computed(() => {
  if (!props.product.price) return 'N/A'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(props.product.price)
})

const navigateToProduct = () => {
  router.push(`/sites/${props.domain}/products/${props.product.product_id}`)
}
</script>

<template>
  <div class="bg-white rounded border border-gray-300 p-3 px-4 transition-all duration-200 cursor-pointer hover:border-green-500 hover:shadow hover:-translate-y-0.5" @click="navigateToProduct">
    <div class="flex justify-between items-center gap-4">
      <h3 class="m-0 text-sm text-gray-800 font-medium flex-1 whitespace-nowrap overflow-hidden text-ellipsis">{{ product.title }}</h3>
      <span class="text-sm text-green-500 font-semibold whitespace-nowrap">{{ formattedPrice }}</span>
    </div>
  </div>
</template> 