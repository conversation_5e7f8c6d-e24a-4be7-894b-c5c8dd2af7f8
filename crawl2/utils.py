import inspect
import json
import os
import re
from collections.abc import Callable
from urllib.parse import urlparse
from typing import Any, Dict, get_type_hints, Type, TypeVar
import hashlib

from json_repair import repair_json
from loguru import logger
from pydantic import create_model, BaseModel


ModelT = TypeVar("ModelT", bound=BaseModel)


def extract_domain_from_shopify_site_url(url: str) -> str:
    """
    Extract the domain from a Shopify site URL.
    """
    pattern = r'^(?!-)[A-Za-z0-9-]{1,63}(?<!-)(\.(?!-)[A-Za-z0-9-]{1,63}(?<!-))*\.[A-Za-z]{2,}$'
    if re.fullmatch(pattern, url) is not None:
        domain = url
    elif url.startswith("http://") or url.startswith("https://"):
        parsed_url = urlparse(url)
        domain = parsed_url.hostname
    else:
        raise ValueError(f"Invalid Shopify site URL: {url}")
    if domain.startswith("www."):
        domain = domain[4:]  # Remove 'www.' prefix
    return domain


def split_domain_and_path(url: str) -> tuple[str, str]:
    from urllib.parse import urlparse
    if not url.startswith("http"):
        url = "https://" + url
    parsed_url = urlparse(url)
    domain = parsed_url.hostname
    if domain.startswith("www."):
        domain = domain[4:]
    path = parsed_url.path
    if path.endswith("/"):
        path = path[:-1]
    return domain, path


def is_null_like(value: Any) -> bool:
    """
    判断一个值是否为空值（None、"None"、"null"等）

    Args:
        value: 要检查的值

    Returns:
        bool: 如果值为空值返回True，否则返回False
    """
    if value is None:
        return True
    if isinstance(value, str) and value.lower().strip() in ("none", "null", "undefined", ""):
        return True
    return False


def generate_point_id(product_id: str, document: Dict[str, Any]) -> str:
    data = product_id + document.get("title", "") + document.get("content", "") + document.get("topic", "")
    return hashlib.md5(data.encode()).hexdigest()


def parse_llm_json_response(response: str):
    """通用函数：解析和修复 LLM 返回的 JSON 响应

    处理以下情况:
    1. 标准 JSON 字符串
    2. Markdown 格式的 JSON (```json)
    3. 需要修复的 JSON

    Args:
        response: LLM 返回的响应字符串

    Returns:
        解析后的 JSON 对象

    Raises:
        json.JSONDecodeError: 当无法解析为有效 JSON 时
    """
    try:
        # 处理可能的 markdown 格式
        if "```json" in response:
            response = response.split("```json")[1].split("```")[0].strip()
        elif "```" in response:
            response = response.split("```")[1].strip()

        # 尝试直接解析
        try:
            return json.loads(response)
        except json.JSONDecodeError:
            # 如果直接解析失败，尝试修复
            repaired = repair_json(response)
            # 确保修复后的结果是字符串
            if not isinstance(repaired, str):
                repaired = json.dumps(repaired)
            return json.loads(repaired)

    except Exception as e:
        logger.error("Failed to parse LLM JSON response: {}", e)
        logger.error("Original response: {}", response)
        raise json.JSONDecodeError("Invalid JSON response", response, 0)


def parse_cookie_string(cookie_str: str, domain: str = None) -> list:
    if not cookie_str:
        return []
    cookies = []
    for item in cookie_str.split(';'):
        if '=' in item:
            name, value = item.strip().split('=', 1)
            cookie = {
                "name": name,
                "value": value,
                "domain": domain if domain else urlparse(value).netloc or "localhost",
                "path": "/"
            }
            cookies.append(cookie)
    return cookies


def get_cookies(domain: str) -> tuple[str, list]:
    map = {
        "shop-genius-demo-stq.myshopify.com": "localization=HK; _shopify_y=50C88717-a500-46D4-bddb-6f028a909c2d; cart=Z2NwLXVzLXdlc3QxOjAxSlkzVzdKWUJLNkU2SlowQjU3TlBKQjJI%3Fkey%3D4fd63ae31504ab8cf8d90aff222a6f99; cart_currency=CNY; _shopify_s=3c3581bf-1423-4f98-a68d-c3cce8730db1; _tracking_consent=3.AMPS_JP13_f_f_nTI6uy5HQk2p6z0mYvN-Ag; _orig_referrer=; _landing_page=%2Fpassword; _shopify_essential=:AZce_ukQAAEAhCyn99Ggjv8oakjMQ-okoRJqa2wNMFe_dTNHXaWNTSZiRMiCFamPUy4w83TKZbvMPo7mdEHLxyvxS-GWHBDiz1bYRA6Srw1eXYKvAIx3ZQZ9UHfJwBzMi-byVhcnaUZO-Baq_loLcebu-_r6IQJghhTg2aWl-fR2_YG6OUo:; storefront_digest=3ea8484416b4bdb73ad745a9c9e2e69fcf77487bbcdfd17e9de14ccd8e9fc0a4; keep_alive=eyJ2IjoyLCJ0cyI6MTc1MjA1ODQwMTE2NywiZW52Ijp7IndkIjowLCJ1YSI6MSwiY3YiOjEsImJyIjoxfSwiYmh2Ijp7Im1hIjo0LCJjYSI6MCwia2EiOjAsInNhIjowLCJ0YSI6MCwia2JhIjowLCJ0Ijo1LCJubSI6MSwibXMiOjAsIm1qIjowLjcsIm1zcCI6MS4wMiwidmMiOjAsImNwIjowLCJyYyI6MCwia2oiOjAsImtpIjowLCJzcyI6MCwic2oiOjAsInNzbSI6MCwic3AiOjAsInRzIjowLCJ0aiI6MCwidHAiOjAsInRzbSI6MH0sInNlcyI6eyJwIjozLCJzIjoxNzUyMDU2NzkxNDA1LCJkIjoxNjA4fX0%3D" # NOQA
    }
    cookies_str = map.get(domain, "")
    return cookies_str, parse_cookie_string(cookies_str, domain)


def get_global_cookies() -> list:
    _, cookies = get_cookies("shop-genius-demo-stq.myshopify.com")
    return cookies


def extract_pydantic_model_for_function_params(func: Callable) -> Type[ModelT]:
    """为函数的参数提取 Pydantic 模型"""
    sig = inspect.signature(func)
    params = list(sig.parameters.values())
    type_hints = get_type_hints(func)
    params_model_fields = {}
    for param in params:
        type_hint = type_hints[param.name]
        default_value = param.default if param.default is not inspect.Parameter.empty else ...
        params_model_fields[param.name] = (type_hint, default_value)

    params_model: Type[ModelT] = create_model(
        f"{func.__name__}_Params",
        **params_model_fields
    )
    return params_model


def generate_log_url(task_id: str | None = None, workflow_id: int | None = None, env: str | None = None) -> str | None:
    """
    生成日志查看链接

    Args:
        task_id: 任务ID
        workflow_id: 工作流ID
        env: 环境名称，默认为warm-$env

    Returns:
        日志链接URL，如果没有提供task_id或workflow_id则返回None
    """
    base_url = "https://create.infra.leyantech.com/api/v2/redirect/kibana"
    if not env:
        env = f"warm-{os.environ.get('env', 'stq')}"
    
    if task_id:
        return f"{base_url}?env={env}&contextMap.task_id={task_id}&from=now-7d&to=now"
    elif workflow_id:
        return f"{base_url}?env={env}&contextMap.workflow_id={workflow_id}&from=now-7d&to=now"
    else:
        return None
