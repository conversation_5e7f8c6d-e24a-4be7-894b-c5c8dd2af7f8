from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX IF EXISTS "idx_product_typ_site_id_493ff1";
        ALTER TABLE "product_type_pv_schema" DROP CONSTRAINT IF EXISTS "fk_product__llm_reco_1c9df6e2";
        DROP INDEX IF EXISTS "idx_product_typ_site_id_4bcefb";
        ALTER TABLE "product_type_expanded_concerns" DROP CONSTRAINT IF EXISTS "fk_product__llm_reco_362f6f22";
        DROP INDEX IF EXISTS "idx_product_typ_site_id_aefba7";
        ALTER TABLE "product_type_expanded_aspects" DROP CONSTRAINT IF EXISTS "fk_product__llm_reco_b8dd5a35";
        DROP INDEX IF EXISTS "idx_product_typ_site_id_dcb6cd";
        ALTER TABLE "product_type_concerns" DROP CONSTRAINT IF EXISTS "fk_product__llm_reco_c5425495";
        DROP INDEX IF EXISTS "idx_product_typ_site_id_9bebf8";
        ALTER TABLE "product_type_aspects" DROP CONSTRAINT IF EXISTS "fk_product__llm_reco_70db3cf3";
        DROP INDEX IF EXISTS "idx_product_sum_product_bfaad9";
        ALTER TABLE "product_summary" DROP CONSTRAINT IF EXISTS "fk_product__llm_reco_bf6904a6";
        DROP INDEX IF EXISTS "idx_product_sel_product_39e0b7";
        ALTER TABLE "product_selling_point" DROP CONSTRAINT IF EXISTS "fk_product__llm_reco_edc84327";
        DROP INDEX IF EXISTS "idx_product_sea_product_de7ec6";
        ALTER TABLE "product_search_enhancement" DROP CONSTRAINT IF EXISTS "fk_product__llm_reco_05c36c69";
        DROP INDEX IF EXISTS "idx_product_pro_metadat_be1d06";
        ALTER TABLE "product_property_and_value" DROP CONSTRAINT IF EXISTS "fk_product__llm_reco_0f3b83b4";
        DROP INDEX IF EXISTS "idx_product_key_product_023dd3";
        ALTER TABLE "product_keyword" DROP CONSTRAINT IF EXISTS "fk_product__llm_reco_7737d8d6";
        DROP INDEX IF EXISTS "idx_product_faq_product_78cea2";
        ALTER TABLE "product_faqs" DROP CONSTRAINT IF EXISTS "fk_product__llm_reco_48e83983";
        DROP INDEX IF EXISTS "idx_product_con_product_323f54";
        ALTER TABLE "product_concern_point" DROP CONSTRAINT IF EXISTS "fk_product__llm_reco_d0f50a92";
        ALTER TABLE "product_concern_point" DROP COLUMN "llm_record_id";
        ALTER TABLE "product_faqs" DROP COLUMN "llm_record_id";
        ALTER TABLE "product_keyword" DROP COLUMN "llm_record_id";
        ALTER TABLE "product_property_and_value" DROP COLUMN "llm_record_id";
        ALTER TABLE "product_search_enhancement" DROP COLUMN "llm_record_id";
        ALTER TABLE "product_selling_point" DROP COLUMN "llm_record_id";
        ALTER TABLE "product_summary" DROP COLUMN "llm_record_id";
        ALTER TABLE "product_type_aspects" DROP COLUMN "llm_record_id";
        ALTER TABLE "product_type_concerns" DROP COLUMN "llm_record_id";
        ALTER TABLE "product_type_expanded_aspects" DROP COLUMN "llm_record_id";
        ALTER TABLE "product_type_expanded_concerns" DROP COLUMN "llm_record_id";
        ALTER TABLE "product_type_pv_schema" DROP COLUMN "llm_record_id";
        DROP TABLE IF EXISTS "product_concern_faqs";
        DROP TABLE IF EXISTS "product_selling_point_faqs";
        DROP TABLE IF EXISTS "llm_record";
        CREATE INDEX IF NOT EXISTS "idx_product_con_product_f3e140" ON "product_concern_point" ("product_id", "status");
        CREATE INDEX IF NOT EXISTS "idx_product_faq_product_af6ea2" ON "product_faqs" ("product_id", "status");
        CREATE INDEX IF NOT EXISTS "idx_product_key_product_31ad26" ON "product_keyword" ("product_id");
        CREATE INDEX IF NOT EXISTS "idx_product_pro_metadat_a086bc" ON "product_property_and_value" ("metadata_id");
        CREATE INDEX IF NOT EXISTS "idx_product_sea_product_df840c" ON "product_search_enhancement" ("product_id");
        CREATE INDEX IF NOT EXISTS "idx_product_sel_product_2d35ea" ON "product_selling_point" ("product_id", "status");
        CREATE INDEX IF NOT EXISTS "idx_product_sum_product_95686e" ON "product_summary" ("product_id");
        CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_84533b" ON "product_type_aspects" ("site_id");
        CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_05c53d" ON "product_type_concerns" ("site_id");
        CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_db108b" ON "product_type_expanded_aspects" ("site_id");
        CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_6a2d1c" ON "product_type_expanded_concerns" ("site_id");
        CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_e24557" ON "product_type_pv_schema" ("site_id", "product_type");"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX IF EXISTS "idx_product_typ_site_id_6a2d1c";
        DROP INDEX IF EXISTS "idx_product_typ_site_id_db108b";
        DROP INDEX IF EXISTS "idx_product_sea_product_df840c";
        DROP INDEX IF EXISTS "idx_product_pro_metadat_a086bc";
        DROP INDEX IF EXISTS "idx_product_typ_site_id_e24557";
        DROP INDEX IF EXISTS "idx_product_typ_site_id_05c53d";
        DROP INDEX IF EXISTS "idx_product_sel_product_2d35ea";
        DROP INDEX IF EXISTS "idx_product_con_product_f3e140";
        DROP INDEX IF EXISTS "idx_product_typ_site_id_84533b";
        DROP INDEX IF EXISTS "idx_product_sum_product_95686e";
        DROP INDEX IF EXISTS "idx_product_key_product_31ad26";
        DROP INDEX IF EXISTS "idx_product_faq_product_af6ea2";
        ALTER TABLE "product_faqs" ADD "llm_record_id" INT NOT NULL;
        ALTER TABLE "product_keyword" ADD "llm_record_id" INT NOT NULL;
        ALTER TABLE "product_summary" ADD "llm_record_id" INT NOT NULL;
        ALTER TABLE "product_type_aspects" ADD "llm_record_id" INT NOT NULL;
        ALTER TABLE "product_concern_point" ADD "llm_record_id" INT NOT NULL;
        ALTER TABLE "product_selling_point" ADD "llm_record_id" INT NOT NULL;
        ALTER TABLE "product_type_concerns" ADD "llm_record_id" INT NOT NULL;
        ALTER TABLE "product_type_pv_schema" ADD "llm_record_id" INT NOT NULL;
        ALTER TABLE "product_property_and_value" ADD "llm_record_id" INT NOT NULL;
        ALTER TABLE "product_search_enhancement" ADD "llm_record_id" INT NOT NULL;
        ALTER TABLE "product_type_expanded_aspects" ADD "llm_record_id" INT NOT NULL;
        ALTER TABLE "product_type_expanded_concerns" ADD "llm_record_id" INT NOT NULL;
        ALTER TABLE "product_faqs" ADD CONSTRAINT "fk_product__llm_reco_48e83983" FOREIGN KEY ("llm_record_id") REFERENCES "llm_record" ("id") ON DELETE CASCADE;
        CREATE INDEX IF NOT EXISTS "idx_product_faq_product_78cea2" ON "product_faqs" ("product_id", "llm_record_id", "status");
        ALTER TABLE "product_keyword" ADD CONSTRAINT "fk_product__llm_reco_7737d8d6" FOREIGN KEY ("llm_record_id") REFERENCES "llm_record" ("id") ON DELETE CASCADE;
        CREATE INDEX IF NOT EXISTS "idx_product_key_product_023dd3" ON "product_keyword" ("product_id", "llm_record_id");
        ALTER TABLE "product_summary" ADD CONSTRAINT "fk_product__llm_reco_bf6904a6" FOREIGN KEY ("llm_record_id") REFERENCES "llm_record" ("id") ON DELETE CASCADE;
        CREATE INDEX IF NOT EXISTS "idx_product_sum_product_bfaad9" ON "product_summary" ("product_id", "llm_record_id");
        ALTER TABLE "product_type_aspects" ADD CONSTRAINT "fk_product__llm_reco_70db3cf3" FOREIGN KEY ("llm_record_id") REFERENCES "llm_record" ("id") ON DELETE CASCADE;
        CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_9bebf8" ON "product_type_aspects" ("site_id", "llm_record_id");
        ALTER TABLE "product_concern_point" ADD CONSTRAINT "fk_product__llm_reco_d0f50a92" FOREIGN KEY ("llm_record_id") REFERENCES "llm_record" ("id") ON DELETE CASCADE;
        CREATE INDEX IF NOT EXISTS "idx_product_con_product_323f54" ON "product_concern_point" ("product_id", "llm_record_id", "status");
        ALTER TABLE "product_selling_point" ADD CONSTRAINT "fk_product__llm_reco_edc84327" FOREIGN KEY ("llm_record_id") REFERENCES "llm_record" ("id") ON DELETE CASCADE;
        CREATE INDEX IF NOT EXISTS "idx_product_sel_product_39e0b7" ON "product_selling_point" ("product_id", "llm_record_id", "status");
        ALTER TABLE "product_type_concerns" ADD CONSTRAINT "fk_product__llm_reco_c5425495" FOREIGN KEY ("llm_record_id") REFERENCES "llm_record" ("id") ON DELETE CASCADE;
        CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_dcb6cd" ON "product_type_concerns" ("site_id", "llm_record_id");
        ALTER TABLE "product_type_pv_schema" ADD CONSTRAINT "fk_product__llm_reco_1c9df6e2" FOREIGN KEY ("llm_record_id") REFERENCES "llm_record" ("id") ON DELETE CASCADE;
        CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_493ff1" ON "product_type_pv_schema" ("site_id", "product_type", "llm_record_id");
        ALTER TABLE "product_property_and_value" ADD CONSTRAINT "fk_product__llm_reco_0f3b83b4" FOREIGN KEY ("llm_record_id") REFERENCES "llm_record" ("id") ON DELETE CASCADE;
        CREATE INDEX IF NOT EXISTS "idx_product_pro_metadat_be1d06" ON "product_property_and_value" ("metadata_id", "llm_record_id");
        ALTER TABLE "product_search_enhancement" ADD CONSTRAINT "fk_product__llm_reco_05c36c69" FOREIGN KEY ("llm_record_id") REFERENCES "llm_record" ("id") ON DELETE CASCADE;
        CREATE INDEX IF NOT EXISTS "idx_product_sea_product_de7ec6" ON "product_search_enhancement" ("product_id", "llm_record_id");
        ALTER TABLE "product_type_expanded_aspects" ADD CONSTRAINT "fk_product__llm_reco_b8dd5a35" FOREIGN KEY ("llm_record_id") REFERENCES "llm_record" ("id") ON DELETE CASCADE;
        CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_aefba7" ON "product_type_expanded_aspects" ("site_id", "llm_record_id");
        ALTER TABLE "product_type_expanded_concerns" ADD CONSTRAINT "fk_product__llm_reco_362f6f22" FOREIGN KEY ("llm_record_id") REFERENCES "llm_record" ("id") ON DELETE CASCADE;
        CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_4bcefb" ON "product_type_expanded_concerns" ("site_id", "llm_record_id");"""
