import time

import pytest
import sentry_sdk
from loguru import logger
from tortoise import Tortoise

from crawl2 import utils
from crawl2.clients import llm
from crawl2.db import models, operations
from crawl2.tasks import (
    selling_point,
    product_concern,
    product_raw_pages,
    product_metadata,
    collection_metadata,
    product_type_aspects,
    site_metadata,
    product_knowledge,
    product_type_concerns,
    product_faq,
    product_selling_point_translation,
    product_type_pv_schema,
    product_pv_extraction,
    page_blog_metadata,
    product_title_keywords,
    product_summary,
    product_type_fix,
)
from crawl2.tasks.store_knowledge_extraction import (
    store_knowledge_value,
    store_knowledge,
    collection_knowledge,
)
from crawl2.qdrant_index.product_knowledge_point_index import load_all_product_knowledge_points
from crawl2.qdrant_index.store_knowledge_point_index import load_all_store_knowledge_points
from crawl2.qdrant_index.collection_knowledge_point_index import load_all_collection_knowledge_points

sentry_sdk.init()  # NOQA
domain = utils.extract_domain_from_shopify_site_url("https://www.cearvol.com")


@pytest.fixture(scope="module", autouse=True)
async def db_init():
    await Tortoise.init(db_url="sqlite://db.sqlite3", modules={"models": [models]})
    await Tortoise.generate_schemas()
    logger.info("Database initialized.")
    yield
    await Tortoise.close_connections()


@pytest.fixture(scope="module", autouse=True)
def patch_llm_for_fast_test():
    # patch llm_config for unit test
    def patch(conf: llm.LLMConfig):
        qwen3_14b = "qwen2.5-32b-chat-fp8-offline"  # qwen3被下掉了
        conf.service = "usq"
        conf.model = qwen3_14b
        conf.enable_thinking = False

    patch(product_knowledge.llm_conf)
    patch(selling_point.llm_conf)
    patch(product_concern.llm_conf)
    patch(product_concern.translate_llm_config)
    patch(product_type_pv_schema.llm_conf)
    patch(product_title_keywords.llm_conf)
    patch(product_summary.llm_conf)
    patch(product_type_fix.llm_conf)


@pytest.mark.order(1)
async def test_crawl_site_metadata():
    site = await site_metadata.crawl_site_metadata(domain)
    assert site is not None
    loaded_site = await operations.get_site(domain)
    assert loaded_site is not None
    assert site.id == loaded_site.id


@pytest.mark.order(1)
async def test_crawl_site_metadata_for_test_shop():
    test_shop_domain = "shop-genius-demo-stq.myshopify.com"
    site = await site_metadata.crawl_site_metadata(test_shop_domain)
    assert site is not None
    loaded_site = await operations.get_site(test_shop_domain)
    assert loaded_site is not None
    assert site.id == loaded_site.id
    logger.info(f"test_shop_domain: {test_shop_domain} site urls: {site.product_urls}")


@pytest.mark.order(2)
async def test_crawl_products_metadata():
    site = await operations.get_site(domain)
    assert site is not None
    await product_metadata.crawl_multiple_products_metadata(
        site.product_urls[:2]
    )
    assert len(await operations.list_product_metadata(domain)) == len(
        site.product_urls[:2]
    )


@pytest.mark.order(2)
async def test_crawl_products_metadata_for_test_shop():
    test_shop_domain = "shop-genius-demo-stq.myshopify.com"
    site = await operations.get_site(test_shop_domain)
    assert site is not None
    await product_metadata.crawl_multiple_products_metadata(site.product_urls[:2])
    assert len(await operations.list_product_metadata(test_shop_domain)) == len(site.product_urls[:2])


@pytest.mark.order(2)
async def test_crawl_collections_metadata():
    site = await operations.get_site(domain)
    assert site is not None
    site.collection_urls.insert(
        0, "https://www.zgrills.com/collections/wood-pellet-grills"
    )
    await collection_metadata.crawl_multiple_collections_metadata(
        domain, site.collection_urls[:2]
    )
    assert len(await operations.list_collection_metadata(domain)) == len(
        site.collection_urls[:2]
    )


@pytest.mark.order(2)
async def test_crawl_pages_metadata():
    site = await operations.get_site(domain)
    assert site is not None
    await page_blog_metadata.crawl_multiple_pages_metadata(domain, site.pages_urls[:2], clear_existing=False,
                                                           timeout=60)
    # 获取 PageMetadata 数量
    from crawl2.db import models as db_models

    pages = await db_models.PageMetadata.filter(site=site).all()
    assert len(pages) == len(site.pages_urls[:2])


@pytest.mark.order(2)
async def test_crawl_pages_metadata_for_test_shop():
    test_shop_domain = "shop-genius-demo-stq.myshopify.com"
    site = await operations.get_site(test_shop_domain)
    assert site is not None
    await page_blog_metadata.crawl_multiple_pages_metadata(test_shop_domain, site.pages_urls[:2])
    # 获取 PageMetadata 数量
    from crawl2.db import models as db_models
    pages = await db_models.PageMetadata.filter(site=site).all()
    assert len(pages) == len(site.pages_urls[:2])


@pytest.mark.order(2)
async def test_crawl_blogs_metadata():
    site = await operations.get_site(domain)
    assert site is not None
    await page_blog_metadata.crawl_multiple_blogs_metadata(domain, site.blogs_urls[:2])
    # 获取 BlogMetadata 数量
    from crawl2.db import models as db_models

    blogs = await db_models.BlogMetadata.filter(site=site).all()
    assert len(blogs) == len(site.blogs_urls[:2])


@pytest.mark.order(3)
async def test_crawl_product_raw_page():
    await product_raw_pages.crawl_all_product_page_markdown(
        domain, enable_image_ocr=True
    )
    assert len(await operations.list_product_raw_pages(domain)) > 0


@pytest.mark.order(3)
async def test_crawl_product_raw_page_for_test_shop():
    test_shop_domain = "shop-genius-demo-stq.myshopify.com"
    await product_raw_pages.crawl_all_product_page_markdown(
        test_shop_domain, enable_image_ocr=True
    )
    pages = await operations.list_product_raw_pages(test_shop_domain)
    assert len(pages) > 0
    for page in pages:
        logger.info(f"product_raw_page.markdown: {page.markdown_content}")


@pytest.mark.order(3)
async def test_extract_store_knowledge():
    await store_knowledge.extract_all_store_knowledge(domain)
    assert len(await operations.list_store_knowledge(domain)) > 0


@pytest.mark.order(3)
async def test_extract_collection_knowledge():
    await collection_knowledge.extract_all_collection_knowledge(domain)
    assert len(await operations.list_collection_knowledge(domain)) > 0


@pytest.mark.order(3)
async def test_crawl_product_knowledge():
    await product_knowledge.extract_all_product_knowledge(domain)
    assert len(await operations.list_product_knowledge(domain)) > 0


@pytest.mark.order(4)
async def test_extract_store_knowledge_value():
    await store_knowledge_value.extract_store_knowledge_value(domain)
    assert len(await operations.list_store_knowledge_value(domain)) > 0


@pytest.mark.order(4)
async def test_extract_product_type_aspects():
    await product_type_aspects.extract_all_product_type_aspects(domain)


@pytest.mark.order(4)
async def test_extract_product_type_concerns():
    await product_type_concerns.extract_all_product_type_concerns(domain)


@pytest.mark.order(5)
async def test_expand_batch_product_type_aspects():
    await product_type_aspects.expand_batch_product_type_aspects(domain)


@pytest.mark.order(5)
async def test_expand_batch_product_type_concerns():
    await product_type_concerns.expand_all_product_type_concerns(domain)


@pytest.mark.order(6)
async def test_extract_batch_product_selling_points():
    await selling_point.extract_all_selling_points(domain)
    logger.info("Extracted selling points successfully.")
    start = time.time()
    await selling_point.extract_all_selling_points(domain)
    elapsed = time.time() - start
    assert elapsed < 60, "repeatedly extracting selling points took too long."


@pytest.mark.order(7)
async def test_translate_selling_point():
    await product_selling_point_translation.translate_all_product_selling_points(domain)


@pytest.mark.order(7)
async def test_extract_all_product_concerns():
    await product_concern.extract_all_product_concern_points(domain)
    logger.info("Extracted concern points successfully.")
    await product_concern.translate_all_product_concern_points(domain)


@pytest.mark.order(8)
async def test_extract_faqs():
    await product_faq.extract_and_save_all_product_faqs(domain)


@pytest.mark.order(9)
async def test_extract_product_type_pv_schema():
    await product_type_pv_schema.extract_all_product_type_pv_schema(
        domain, clear_existing=True
    )
    product_types = await operations.get_product_type_list(domain)
    assert len(product_types) > 0
    pv_schema = await operations.get_product_type_pv_schema(domain, product_types[0])
    logger.info("pv_schema", pv_schema)


@pytest.mark.order(10)
async def test_extract_product_pv():
    await product_pv_extraction.extract_product_pv_for_all_products(
        domain, clear_existing=True
    )
    product_types = await operations.get_product_type_list(domain)
    assert len(product_types) > 0
    pv_schema = await operations.get_product_type_pv_schema(domain, product_types[0])
    logger.info("pv_schema", pv_schema)
    product_metas = await operations.list_product_metadata(domain)
    assert len(product_metas) > 0
    for product_meta in product_metas:
        product_pv = await operations.get_product_property_and_value(product_meta)
        assert product_pv is not None
        logger.info("product_pv.property_values", product_pv.property_values)


@pytest.mark.order(10)
async def test_extract_product_title_keywords():
    await product_title_keywords.extract_product_title_keywords_for_all_products(
        domain, clear_existing=True
    )
    product_metas = await operations.list_product_metadata(domain)
    for product_meta in product_metas:
        product_keywords = await operations.get_product_keywords(product_meta)
        logger.info(
            "product_keywords for ", product_meta.url, product_keywords.keywords
        )


@pytest.mark.order(10)
async def test_extract_product_summary():
    await product_summary.extract_product_summary_for_all_products(
        domain, clear_existing=True
    )
    product_metas = await operations.list_product_metadata(domain)
    for product_meta in product_metas:
        summary = await operations.get_product_summary(product_meta)
        logger.info("product_summary for ", product_meta.url, summary.summary)


@pytest.mark.order(11)
async def test_fix_product_type():
    await product_type_fix.fix_product_type_for_all_products(domain)
    product_metas = await operations.list_product_metadata(domain)
    for product_meta in product_metas:
        product_type = product_meta.product_type
        logger.info("product_type for ", product_meta.url, product_type)


@pytest.mark.skip(reason="skip for ci")
@pytest.mark.order(11)
async def test_import_product_knowledge_points_to_qdrant():
    store_domain, product_knowledge_points = await load_all_product_knowledge_points(
        domain
    )
    assert store_domain is not None
    assert len(product_knowledge_points) > 0
    logger.info(f"store_domain: {store_domain}")
    url = product_knowledge_points[0].url
    logger.info(f"url: {url}")
    from crawl2.qdrant_index.product_knowledge_point_index import ProductKnowledgePointIndex
    from crawl2.qdrant_index.base_qdrant_indexer import IndexConfig
    index = ProductKnowledgePointIndex(IndexConfig(
        collection_name="product_knowledge_point_test_v2",
        embedding_client_url="http://*************:8002/embed",
        sparse_embedding_client_url="http://*************:8002/sparse_embed",
        qdrant_host="localhost",
        qdrant_port=6333,
        vector_size=1024,
    ))
    await index.import_all_knowledge_points(
        domain, batch_size=10, force_recreate_collection=False,
        delete_previous_shop_domain_data=True
    )
    await index.import_single_product_knowledge_points(url)


@pytest.mark.skip(reason="skip for ci")
@pytest.mark.order(11)
async def test_import_store_knowledge_points_to_qdrant():
    store_domain, store_knowledge_points = await load_all_store_knowledge_points(
        domain
    )
    assert store_domain is not None
    assert len(store_knowledge_points) > 0
    logger.info(f"store_domain: {store_domain}")
    point_id = store_knowledge_points[0].point_id
    logger.info(f"point_id: {point_id}")
    from crawl2.qdrant_index.store_knowledge_point_index import StoreKnowledgePointIndex
    from crawl2.qdrant_index.base_qdrant_indexer import IndexConfig
    index = StoreKnowledgePointIndex(IndexConfig(
        collection_name="store_knowledge_point_test",
        embedding_client_url="http://*************:8002/embed",
        sparse_embedding_client_url="http://*************:8002/sparse_embed",
        qdrant_host="localhost",
        qdrant_port=6333,
        vector_size=1024,
    ))
    await index.import_all_knowledge_points(
        domain, batch_size=10, force_recreate_collection=False,
        delete_previous_shop_domain_data=True
    )


@pytest.mark.skip(reason="skip for ci")
@pytest.mark.order(11)
async def test_import_collection_knowledge_points_to_qdrant():
    store_domain, collection_knowledge_points = await load_all_collection_knowledge_points(
        domain
    )
    logger.info(f"collection_knowledge_points size: {len(collection_knowledge_points)}")
    assert store_domain is not None
    assert len(collection_knowledge_points) > 0
    logger.info(f"store_domain: {store_domain}")
    point_id = collection_knowledge_points[0].point_id
    logger.info(f"point_id: {point_id}")
    from crawl2.qdrant_index.collection_knowledge_point_index import CollectionKnowledgePointIndex
    from crawl2.qdrant_index.base_qdrant_indexer import IndexConfig
    index = CollectionKnowledgePointIndex(IndexConfig(
        collection_name="collection_knowledge_point_test",
        embedding_client_url="http://*************:8002/embed",
        sparse_embedding_client_url="http://*************:8002/sparse_embed",
        qdrant_host="localhost",
        qdrant_port=6333,
        vector_size=1024,
    ))
    await index.import_all_knowledge_points(
        domain, batch_size=10, force_recreate_collection=False,
        delete_previous_shop_domain_data=True
    )


@pytest.mark.skip(reason="skip for ci")
@pytest.mark.order(12)
async def test_sync_product_title_keywords_to_knowledge_base():
    await product_title_keywords.sync_product_title_keywords_to_knowledge_base(domain)


@pytest.mark.skip(reason="skip for ci")
@pytest.mark.order(12)
async def test_sync_product_pv_to_knowledge_base():
    await product_pv_extraction.sync_product_pv_to_knowledge_base(domain)


@pytest.mark.skip(reason="skip for ci")
@pytest.mark.order(12)
async def test_sync_all_product_knowledge_to_knowledge_base():
    await product_knowledge.sync_all_product_knowledge_to_knowledge_base(domain)


@pytest.mark.skip(reason="skip for ci")
@pytest.mark.order(12)
async def test_sync_single_product_knowledge_to_knowledge_base():
    store_domain, product_knowledge_points = await load_all_product_knowledge_points(
        domain
    )
    assert store_domain is not None
    assert len(product_knowledge_points) > 0
    logger.info(f"store_domain: {store_domain}")
    url = product_knowledge_points[0].url
    logger.info(f"url: {url}")
    await product_knowledge.sync_single_product_knowledge_to_knowledge_base(url)


@pytest.mark.skip(reason="skip for ci")
@pytest.mark.order(12)
async def test_sync_store_knowledge_to_knowledge_base():
    await store_knowledge.sync_store_knowledge_to_knowledge_base(domain)


@pytest.mark.skip(reason="skip for ci")
@pytest.mark.order(12)
async def test_crawl_product_metadata():
    url = "https://cyxus.com/products/dexter-8150"
    product = await product_metadata._crawl_product_metadata(url)
    logger.info(product)
