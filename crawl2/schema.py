import json
import re
import uuid
from collections import defaultdict
from datetime import datetime
from typing import TypeVar, Union, Dict, Optional, Any, Generic, List, Literal
from enum import Enum

from loguru import logger
from pydantic import BaseModel, Field

T = TypeVar('T', bound=BaseModel)


class PaginatedResponse(BaseModel, Generic[T]):
    items: List[T]
    page: int
    size: int
    total: int
    total_pages: int


class SellingPointStatus(str, Enum):
    FRESH = "fresh"
    EDITING = "editing"
    DISABLED = "disabled"
    READY = "ready"
    ARCHIVED = "archived"


class EditStatusStats(BaseModel):
    """各状态的数量统计"""
    fresh: int = 0
    editing: int = 0
    ready: int = 0
    disabled: int = 0
    archived: int = 0
    total: int = 0

    def model_post_init(self, context):
        self.total = self.fresh + self.editing + self.ready + self.disabled + self.archived


class ProductVariant(BaseModel):
    variant_id: int
    variant_name: str
    option_values: Dict[str, str]


class VariantInfos(BaseModel):
    variants: list[ProductVariant]
    variant_options: Dict[str, list[str]]


class PageElementStatus(str, Enum):
    FRESH = "fresh"
    EDITED = "edited"


class ProductMetaData(BaseModel):
    url: str
    product_id: str
    title: str
    price: str
    vendor: str
    tags: str
    product_type: str
    variant_info: Optional[VariantInfos] = None

    def parse_product_variants(product: Dict[str, Any]) -> VariantInfos:
        variant_options = {p["name"]: p["values"] for p in product["options"]}
        position_to_option = {p["position"]: p["name"] for p in product["options"]}
        variants = []
        for variant in product["variants"]:
            option_values = {}
            for index in range(1, 4):
                key = f"option{index}"
                if key in variant and index in position_to_option.keys():
                    name = position_to_option[index]
                    option_values[name] = variant[key]
            variants.append(ProductVariant(
                variant_id=variant["id"],
                variant_name=variant["title"],
                option_values=option_values,
            ))
        return VariantInfos(variants=variants, variant_options=variant_options)

    @classmethod
    def from_dict(cls, url: str, data: dict) -> 'ProductMetaData':
        product_id = str(data["product"]["id"])
        title = data["product"]["title"]
        price = data["product"].get("variants", [{}])[0].get("price", "")
        vendor = data["product"]["vendor"]
        tags = data["product"]["tags"]
        product_type = data["product"]["product_type"]
        variant_info = cls.parse_product_variants(data["product"])
        logger.info(f"variants: {variant_info}")
        return ProductMetaData(
            url=url, product_id=product_id, title=title,
            price=price, vendor=vendor, tags=tags, product_type=product_type,
            variant_info=variant_info)


class CollectionMetaData(BaseModel):
    url: str
    collection_id: str
    title: str | None = None
    description: str | None = None
    product_ids: list[str]
    raw_content: str = ""

    @classmethod
    def from_dict(cls, url: str, data: dict, raw_content: str = "") -> 'CollectionMetaData':
        collection_id = str(data["collection"]["id"])
        title = data["collection"]["title"]
        description = data["collection"]["description"]
        product_ids = [str(product["id"]) for product in data["collection"]["products"]]
        return CollectionMetaData(url=url, collection_id=collection_id, title=title, description=description,
                                  product_ids=product_ids, raw_content=raw_content)


class ProductDetails(ProductMetaData):
    """通过 RESTful API 返回的产品详情"""
    page_markdown: str | None
    knowledge_markdown: str | None
    knowledge_documents: list['ProductKnowledgeWithTopic'] | None
    selling_points: list['ProductSellingPoint']
    faqs: list['Faq'] | None = None


class SiteBrief(BaseModel):
    domain: str
    shopify_domain: str
    crawl_status: str
    product_count: int
    selling_point_stats: EditStatusStats
    concern_point_stats: EditStatusStats
    faq_stats: EditStatusStats


class SiteDetails(BaseModel):
    domain: str
    shopify_domain: str
    crawl_status: str
    products: list[ProductMetaData]
    product_type_aspects: Dict[str, list['NameAndDescription']]
    product_type_concerns: Dict[str, list['NameAndDescription']]


class ProductPageMarkdownRawContent(BaseModel):
    markdown: str
    crawl_time: str


class ProductPageMarkdown(BaseModel):
    url: str
    raw_content: ProductPageMarkdownRawContent

    def page_name(self) -> str:
        return self.url.split("/")[-1]


class MarkdownLink(BaseModel):
    url: str
    title: str


class ProductKnowledgeDocument(BaseModel):
    title: str = ''
    content: str = ''


class ProductKnowledgeGroupByTopic(BaseModel):
    topic: str = ''
    documents: list[ProductKnowledgeDocument] = Field(default_factory=list)

    @classmethod
    def merge_topics_as_markdown(cls, url: str, topics: list['ProductKnowledgeGroupByTopic']) -> str:
        if not topics:
            return ''
        markdown_content = []
        markdown_content.append(f"# Product URL:\n{url}\n")
        markdown_content.append("\n")
        for doc in topics:
            markdown_content.append(f"# {doc.topic}\n")

            for item in doc.documents:
                markdown_content.append(f"## {item.title}\n")

                content = item.content.strip()
                if content.startswith('*'):
                    # 将 * 开头的行转换为无序列表
                    lines = content.split('\n')
                    for line in lines:
                        if line.strip().startswith('*'):
                            markdown_content.append(f"* {line.strip()[1:].strip()}\n")
                        else:
                            markdown_content.append(f"{line}\n")
                else:
                    markdown_content.append(f"{content}\n")

                markdown_content.append("\n")

            markdown_content.append("\n")

        return "".join(markdown_content)


class ProductKnowledgeWithTopic(BaseModel):
    topic: str = ''
    title: str = ''
    content: str = ''

    @classmethod
    def from_product_knowledge_topic_group(cls, topic: ProductKnowledgeGroupByTopic) -> (
            list)['ProductKnowledgeWithTopic']:
        documents = []
        for doc in topic.documents:
            if len(doc.content.strip()) > 0:
                documents.append(cls(topic=topic.topic, title=doc.title, content=doc.content))
        return documents


class ProductKnowledge(BaseModel):
    url: str
    markdown_content: str
    documents: list[ProductKnowledgeWithTopic] = Field(default_factory=list)

    @classmethod
    def from_product_knowledge_topics(cls, url,
                                      knowledge_topics: list[ProductKnowledgeGroupByTopic]) -> 'ProductKnowledge':
        documents = [doc for topic in knowledge_topics for doc in
                     ProductKnowledgeWithTopic.from_product_knowledge_topic_group(topic)]
        return cls(
            url=url,
            markdown_content=ProductKnowledgeGroupByTopic.merge_topics_as_markdown(url, knowledge_topics),
            documents=documents
        )


class CleanedProductPageMarkdown(ProductPageMarkdown):
    extracted_links: list[MarkdownLink]
    json_objects: list

    @classmethod
    def from_raw_page_markdown(cls, page_markdown: ProductPageMarkdown) -> 'CleanedProductPageMarkdown':
        """
        Create a CleanedProductPageMarkdown from a raw page markdown.
        """
        # Extract links and JSON objects
        links, cleaned_markdown = cls.extract_links(page_markdown.raw_content.markdown)
        json_objects, cleaned_markdown = cls.extract_json_objects(cleaned_markdown)

        # Create a new instance with the cleaned data
        return cls(
            url=page_markdown.url,
            raw_content=ProductPageMarkdownRawContent(markdown=cleaned_markdown,
                                                      crawl_time=page_markdown.raw_content.crawl_time),
            extracted_links=links,
            json_objects=json_objects
        )

    @classmethod
    def extract_links(cls, markdown: str) -> (list[MarkdownLink], str):
        """
        Extract markdown links and image links from the content and replace them with just the title/alt text.
        Handles nested links by processing from inner to outer.
        Also handles image links without titles.
        """
        # Pattern to match:
        # 1. Regular markdown links [text](url)
        # 2. Image links with title ![alt text](url)
        # 3. Image links without title ![](url)
        link_pattern = r"!?\[([^\]]*?)\]\s*\(([^)]+)\)"

        # Find all links
        links = []
        cleaned_markdown = markdown

        # Keep processing until no more links are found
        while True:
            # Find the innermost link
            match = re.search(link_pattern, cleaned_markdown)
            if not match:
                break

            title = match.group(1).strip()
            url = match.group(2).strip()
            # logger.debug(f"match: {match.group(0)}")

            if match.group(0).startswith("![") and not title:
                title = ""

            links.append(MarkdownLink(title=title, url=url))

            # Replace this link with its title
            pattern = re.escape(match.group(0))
            cleaned_markdown = re.sub(pattern, title + "\n", cleaned_markdown, count=1)

        return links, cleaned_markdown

    @classmethod
    def extract_json_objects(cls, markdown: str) -> (list, str):
        """
        Extract JSON strings from markdown content and remove them.

        Args:
            markdown: The markdown content to process

        Returns:
            Tuple containing:
            - List of extracted JSON objects
            - The markdown content with JSON strings removed
        """
        # Pattern to match JSON strings
        json_pattern = r'\[(\{.*?\})\]'

        # Find all JSON strings
        json_objects = []
        for match in re.finditer(json_pattern, markdown):
            json_str = match.group(1)
            try:
                json_obj = json.loads(json_str)
                json_objects.append(json_obj)
            except json.JSONDecodeError as e:
                logger.warning("Failed to decode JSON string: {}, error: {}", json_str, e)
                continue

        # Remove all JSON strings from the markdown
        cleaned_markdown = re.sub(json_pattern, '', markdown)
        return json_objects, cleaned_markdown


class NameAndDescription(BaseModel):
    name: str
    description: str

    @classmethod
    def merge_name_description_lists(
            cls, list1: list['NameAndDescription'], list2: list['NameAndDescription']) -> list['NameAndDescription']:
        """
        Merge two lists of NameAndDescription objects, combining their descriptions if names match.
        """
        added_names = set()
        res: list['NameAndDescription'] = []
        for item in list1 + list2:
            if item.name in added_names:
                continue
            added_names.add(item.name)
            res.append(item)
        return res


class ProductsAspects(BaseModel):
    product_type: str
    aspect_list: list[NameAndDescription] = Field(default_factory=list)

    @classmethod
    def from_llm_result(cls, result) -> Union["ProductsAspects", None]:
        if not isinstance(result, dict):
            return None
        if 'aspect_list' not in result:
            return None
        product_type = result.get('product_type', '')
        aspect_list = result.get('aspect_list', [])
        if not isinstance(aspect_list, list):
            return None
        aspects = []
        for item in aspect_list:
            name, description = item.get('name', ''), item.get('description', '')
            if not name or not description or not isinstance(name, str) or not isinstance(description, str):
                logger.warning("Invalid aspect item: {}", item)
                continue
            aspects.append(NameAndDescription(name=name, description=description))
        return ProductsAspects(product_type=product_type, aspect_list=aspects)


class ProductsConcerns(BaseModel):
    product_type: str
    concern_list: list[NameAndDescription] = Field(default_factory=list)


class ProductSellingPoint(BaseModel):
    name: str
    value: str
    marketing_copies: list[str]

    @classmethod
    def from_llm_result(cls, result) -> list["ProductSellingPoint"]:
        if not isinstance(result, dict):
            return []
        if 'selling_points' not in result:
            return []
        selling_points = result.get('selling_points', [])
        if not isinstance(selling_points, list):
            return []
        points = []
        for item in selling_points:
            if not isinstance(item, dict):
                logger.warning("Invalid selling point item: {}", item)
                continue
            name, value = item.get('name', ''), item.get('value', '')
            if not name or not value or not isinstance(name, str) or not isinstance(value, str):
                logger.warning("Invalid selling point item: {}", item)
                continue
            marketing_copies = item.get('marketing_copies', [])
            if not isinstance(marketing_copies, list):
                logger.warning("Invalid marketing copies for selling point: {}", item)
                continue
            valid_marketing_copies = [copy for copy in marketing_copies if isinstance(copy, str)]
            if not valid_marketing_copies:
                logger.warning("No valid marketing copies for selling point: {}", item)
                continue
            points.append(ProductSellingPoint(name=name, value=value, marketing_copies=valid_marketing_copies))
        return cls.merge_selling_points_by_value(points)

    @classmethod
    def merge_selling_points_by_value(cls, selling_points: list["ProductSellingPoint"]) -> list["ProductSellingPoint"]:
        merged = {}
        for sp in selling_points:
            value = sp.value
            if not value:
                continue

            if value not in merged:
                merged[value] = sp
            else:
                if not merged[value].name and sp.name:
                    merged[value].name = sp.name
                merged[value].marketing_copies.extend(sp.marketing_copies)
        return list(merged.values())


class ProductConcernPoint(BaseModel):
    concern: str
    marketing_copies: list[str]

    @classmethod
    def from_llm_result(cls, result) -> list["ProductConcernPoint"]:
        if not isinstance(result, dict):
            return []
        if 'concern_points' not in result:
            return []
        concern_points = result.get('concern_points', [])
        if not isinstance(concern_points, list):
            return []
        concerns = defaultdict(set)
        for item in concern_points:
            concern = item.get('concern', '')
            if not concern or not isinstance(concern, str):
                continue
            marketing_copies = item.get('marketing_copies', [])
            if not marketing_copies or not isinstance(marketing_copies, list):
                continue
            for marketing_copy in marketing_copies:
                if not isinstance(marketing_copy, str):
                    continue
                concerns[concern].add(marketing_copy)
        res = []
        for concern, marketing_copies in concerns.items():
            res.append(ProductConcernPoint(
                concern=concern,
                marketing_copies=list(marketing_copies)
            ))
        return res


class ProductMetaAndConcern(BaseModel):
    """对应顾虑点表格中的一行数据"""
    id: int
    product_url: str
    product_id: str
    product_type: str
    product_price: str
    product_title: str
    concern: str
    concern_chn: str | None = None
    marketing_copies: list[str] | None = None
    marketing_copies_chn: list[str] | None = None
    status: SellingPointStatus = SellingPointStatus.FRESH
    created_at: datetime | None = None
    last_edited_at: datetime | None = None
    version_id: int | None = None


class ScenarioAndContent(BaseModel):
    scenario: str
    content: str
    element_targets: list[str]


class ProductScenarioSellingPoint(BaseModel):
    product_url: str
    product_id: str
    selling_points: list[ScenarioAndContent]


class ProductMetaAndSellingPoint(BaseModel):
    """对应卖点表格中的一行数据"""
    id: int
    product_url: str
    product_id: str
    product_type: str
    product_price: str
    product_title: str
    selling_point_name: str
    selling_point_value: str
    selling_point_marketing_copies: list[str] | None = None
    selling_point_name_chn: str | None = None
    selling_point_value_chn: str | None = None
    selling_point_marketing_copies_chn: list[str] | None = None
    status: SellingPointStatus = SellingPointStatus.FRESH
    created_at: datetime | None = None
    last_edited_at: datetime | None = None
    version_id: int | None = None
    element_targets: list[str] = []


class SellingPointPaginatedResponse(PaginatedResponse[ProductMetaAndSellingPoint]):
    """卖点分页响应，包含各状态统计"""
    status_counts: EditStatusStats


class ConcernPaginatedResponse(PaginatedResponse[ProductMetaAndConcern]):
    """顾虑点分页响应，包含各状态统计"""
    status_counts: EditStatusStats


class FaqItem(BaseModel):
    """单个FAQ项"""
    question: str
    answer: str
    question_chn: str | None = None
    answer_chn: str | None = None


class ProductMetaAndFaq(BaseModel):
    """对应FAQ表格中的一行数据 - 一个产品对应一个FAQ记录"""
    id: int
    shopify_domain: str
    product_url: str
    product_id: str
    product_type: str
    product_price: str
    product_title: str
    faqs: list[FaqItem] = []
    status: SellingPointStatus = SellingPointStatus.FRESH
    created_at: datetime | None = None
    last_edited_at: datetime | None = None
    version_id: int | None = None


class FaqPaginatedResponse(PaginatedResponse[ProductMetaAndFaq]):
    """FAQ分页响应，包含各状态统计"""
    status_counts: EditStatusStats



class Faq(BaseModel):
    question: str
    answer: str

    @classmethod
    def from_llm_result(cls, faqs) -> list["Faq"]:
        faq_objects = []
        if not isinstance(faqs, list):
            logger.warning("Invalid FAQ items: {}", faqs)
            return []
        for faq in faqs:
            if not isinstance(faq, dict):
                logger.warning("Invalid FAQ item: {}", faq)
                continue
            faq_question = faq.get('question', '')
            faq_answer = faq.get('answer', '')
            if any((not faq_question, not faq_answer,
                    not isinstance(faq_question, str),
                    not isinstance(faq_answer, str))):
                logger.warning("Invalid FAQ item: {}", faq)
                continue
            faq_objects.append(Faq(question=faq_question, answer=faq_answer))
        return faq_objects


class ProductSearchEnhancement(BaseModel):
    new_title: str
    new_description: str
    new_product_type: str
    filterable_attributes: dict

    @classmethod
    def from_llm_result(cls, result) -> "ProductSearchEnhancement":
        if not isinstance(result, dict):
            return None
        new_title = result.get('new_title', '')
        new_description = result.get('new_description', '')
        new_product_type = result.get('new_product_type', '')
        filterable_attributes = result.get('filterable_attributes', {})
        return cls(new_title=new_title, new_description=new_description, new_product_type=new_product_type,
                   filterable_attributes=filterable_attributes)


class Progress(BaseModel):
    n: int = Field(..., description="当前进度")
    total: int = Field(..., description="总进度")
    desc: str = Field(..., description="进度描述")
    last_updated_at: datetime = Field(..., description="最后更新时间")


class CeleryTaskDetail(BaseModel):
    id: int
    task_id: str
    workflow_id: int | None = None
    name: str
    status: str
    params: dict
    result: str | None
    error: str | None = None
    created_at: datetime
    finished_at: datetime | None = None
    progress: Progress | None = None
    log_url: str | None = None


class CeleryTaskSignature(BaseModel):
    name: str
    kwargs: dict


class CeleryWorkflowFactory(BaseModel):
    name: str
    description: str | None = None
    params: dict | None = None


class CeleryWorkflowSignature(BaseModel):
    tasks: list[CeleryTaskSignature]
    options: dict | None = None


class CeleryWorkflowDetail(BaseModel):
    id: int
    name: str
    status: str
    signature: CeleryWorkflowSignature
    tasks: list[CeleryTaskDetail]
    created_at: datetime
    updated_at: datetime
    finished_at: datetime | None = None
    log_url: str | None = None


class CeleryWorkflowDefinition(CeleryWorkflowSignature):
    name: str = Field(..., description="工作流名称")


class DataVersion(BaseModel):
    """数据版本信息"""
    id: int
    prev_id: int | None = None
    value: dict
    data_type: str
    data_id: int
    comment: str | None = None
    created_at: datetime


class SellingPointUpdateRequest(BaseModel):
    """卖点更新请求"""
    name: str | None = None
    description: str | None = None
    marketing_copies: list[str] | None = None
    name_chn: str | None = None
    description_chn: str | None = None
    marketing_copies_chn: list[str] | None = None
    status: SellingPointStatus | None = None
    comment: str | None = None


class FaqUpdateRequest(BaseModel):
    """FAQ更新请求"""
    faqs: list[FaqItem] | None = None
    status: SellingPointStatus | None = None
    comment: str | None = None


class ProductConcernUpdateRequest(BaseModel):
    status: SellingPointStatus | None = None
    marketing_copies: list[str] | None = None
    marketing_copies_chn: list[str] | None = None
    comment: str | None = None


class ProductTypePvSchema(BaseModel):
    domain: str
    product_type: str
    properties: dict


class PageMetaData(BaseModel):
    url: str
    raw_content: str


class BlogMetaData(BaseModel):
    url: str
    raw_content: str


class CrawlTaskDefinition(BaseModel):
    name: str
    description: str
    params: dict
    authors: list[str]


class TaskRunStats(BaseModel):
    """任务执行统计信息"""
    name: str = Field(..., description="任务名称")
    total_runs: int = Field(..., description="总执行次数")
    success_count: int = Field(..., description="成功次数")
    failed_count: int = Field(..., description="失败次数")
    running_count: int = Field(..., description="正在运行次数")
    pending_count: int = Field(..., description="等待中次数")
    last_run_time: datetime | None = Field(None, description="最近一次运行时间")
    last_run_status: str | None = Field(None, description="最近一次运行状态")


class StoreKnowledgePoint(BaseModel):
    point_id: str
    topic: str | None = None
    question: str
    answer: str
    source: str
    source_detail: str | None = None
    quality: int = 1
    label: str | None = None
    detailed_label: str | None = None
    extra_questions: list[str] | None = None
    is_deleted: int = 0


class CollectionKnowledgePoint(BaseModel):
    point_id: str
    topic: str | None = None
    title: str
    content: str
    collection_id: str
    source: str
    source_detail: str | None = None
    label: str | None = None


class ParseFileTaskRequest(BaseModel):
    task_id: str = Field(..., description="shopify-knowledge 上的 task id，回调 shopify-knowledge 通知任务结果时需要用到.")
    file_key: str = Field(..., description="待解析文件的 id 类信息，读取文件内容时，需要向 store-center 申请文件下载链接.")
    store_domain: str = Field(..., description="店铺的 myshopify domain")
    product_ids: list[str] = Field(default_factory=list, description="产品 ID 列表，表示需要解析的产品列表.")
    knowledge_type: str = Field(..., description="知识类型，例如 PRODUCT 或 STORE")


class ExtractFileKnowledgeTaskRequest(ParseFileTaskRequest):
    markdown: str = Field(..., description="从文件中提取的 markdown 内容.")


class MessageOnlyResponse(BaseModel):
    message: str = Field(..., description="返回的消息内容，通常用于通知操作结果或状态。")


class CrawlProductReq(BaseModel):
    product: str = Field(..., description="产品的 URL 或 ID，表示需要爬取的产品信息。")


class ProductTypeScenario(BaseModel):
    label: str
    explanation: str


class ScenarioSellingPoint(BaseModel):
    name: str = ""
    reason: str = ""


class ShopifyPage(BaseModel):
    """Shopify 页面数据模型"""
    id: int = Field(..., description="页面的唯一标识符")
    created_at: datetime = Field(..., description="页面创建时间")
    crawl_status: str = Field(..., description="页面的爬取状态")
    page_url: str
    data: dict


class InitShopifyPage(BaseModel):
    domain: str
    clear_existing: bool = Field(False, description="是否清除现有页面元素，默认为 False")


class PageElementData(BaseModel):
    element_tag: str = Field(..., description="页面元素的标签，用于标识元素类型，例如 '商品卖点'")
    product_id: int | None = Field(None, description="如果是商品详情页，表示商品id")
    main_image_url: str | None = Field(None, description="如果 element_tag=主图, 这个字段表示图片链接")
    main_image_index: int | None = Field(None, description="如果 element_tag=主图，这个字段表示图片顺序")
    selling_point_summary: str | None = Field(None, description="如果 element_tag=商品卖点, 这个字段表示商品卖点摘要")
    bounding_box: dict | None = Field(None, description="绑定的页面元素坐标")


class PageElement(BaseModel):

    id: int = Field(..., description="页面元素数据库 ID，用于更新或删除元素")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="最后更新时间")
    status: PageElementStatus = Field(PageElementStatus.FRESH, description="标注状态")
    target: str = Field(..., description="页面元素标识，用于行为上报")
    selectors: list[str] = Field(..., description="页面元素的选择器列表，目前为 xpath 列表")
    data: PageElementData = Field(..., description="页面元素的其他数据，例如标签等")


class PageElementEditable(BaseModel):
    target: str = Field(default_factory=lambda: uuid.uuid4().hex)
    selectors: list[str]
    data: PageElementData = Field(..., description="页面元素的其他数据，例如标签等")


class PageElementCreate(PageElementEditable):
    page_url: str


class HukouUserInfo(BaseModel):
    """hukou 服务 oauth2 callback 返回的 id_token 中的用户信息"""
    name: str
    email: str = ''
    given_name: str = ''
    phone_number: str = ''


class ShopPupilUser(BaseModel):
    id: int
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    phone_number: Optional[str] = None
    is_active: bool
    is_superuser: bool
    last_login: Optional[datetime] = None
    created_at: datetime


class ProductsSelector(BaseModel):
    """产品选择器"""
    target: Literal['all', 'specific'] = Field(..., description="目标类型：'all' 表示所有产品，'specific' 表示指定产品")
    product_ids: list[str] = Field(default_factory=list, description="当 target='specific' 时，指定要处理的产品ID列表")


class SiteCrawlStrategy(BaseModel):
    """站点爬取策略"""
    product_selling_point: ProductsSelector = Field(default_factory=lambda: ProductsSelector(target='all'))
    product_concern_point: ProductsSelector = Field(default_factory=lambda: ProductsSelector(target='all'))
    product_faq: ProductsSelector = Field(default_factory=lambda: ProductsSelector(target='all'))

    @classmethod
    def get_default_strategy(cls) -> 'SiteCrawlStrategy':
        """获取默认策略"""
        return cls()

    @classmethod
    def create_specific_strategy(cls, product_ids: list[str]) -> 'SiteCrawlStrategy':
        """创建指定产品的策略"""
        return cls(
            product_selling_point=ProductsSelector(target='specific', product_ids=product_ids),
            product_concern_point=ProductsSelector(target='specific', product_ids=product_ids),
            product_faq=ProductsSelector(target='specific', product_ids=product_ids)
        )


# 策略管理相关的 schema 类
class SiteCrawlStrategyUpdateRequest(BaseModel):
    """站点挖掘策略更新请求"""
    strategy: SiteCrawlStrategy
    comment: str | None = Field(None, description="策略变更原因")


class SiteCrawlStrategyResponse(BaseModel):
    """站点挖掘策略响应"""
    domain: str
    strategy: SiteCrawlStrategy | None = Field(None, description="当前策略，为 None 时使用默认策略")
    strategy_updated_at: datetime | None = Field(None, description="策略更新时间")
    is_using_default: bool = Field(..., description="是否使用默认策略")


class SiteCrawlStrategyHistoryResponse(BaseModel):
    """站点挖掘策略历史响应"""
    domain: str
    history: list[DataVersion] = Field(default_factory=list, description="策略历史版本")


class SiteCreateRequest(BaseModel):
    site_url: str
    task_id: str | None = Field(None, description='shopify-knowledge 上的 task id，回调 shopify-knowledge 通知任务结果时需要用到.')
    crawl_strategy: SiteCrawlStrategy | None = Field(None, description='站点爬取策略，如果不指定则使用默认策略')


class ReCrawlSiteRequest(BaseModel):
    begin_with: None | str = Field(None, description="重新爬取时的起始任务名称，如果为 None，则从头开始爬取。")
    crawl_strategy: SiteCrawlStrategy | None = Field(None, description="重新爬取时使用的策略，如果不指定则使用当前站点的策略")
