<script setup>
import { ref, onMounted } from 'vue'
import { useProduct } from '@/composables/useProduct.js'

const props = defineProps({
  domain: {
    type: String,
    required: true
  },
  productId: {
    type: String,
    required: true
  },
  productUrl: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['dataCountChange'])

const { getProductSellingPoints } = useProduct()
const sellingPoints = ref([])
const loading = ref(false)
const error = ref(null)

const fetchSellingPoints = async () => {
  try {
    loading.value = true
    error.value = null
    sellingPoints.value = await getProductSellingPoints(props.domain, props.productId)
    
    // 发送数据量变化事件
    emit('dataCountChange', sellingPoints.value.length)
  } catch (err) {
    error.value = err.message
    console.error('Error fetching product selling points:', err)
    emit('dataCountChange', 0)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchSellingPoints()
})
</script>

<template>
  <div class="w-full">
    <div v-if="loading" class="text-center p-4 text-gray-600">
      Loading selling points...
    </div>
    <div v-else-if="error" class="text-center p-4 text-red-500">
      Error: {{ error }}
    </div>
    <div v-else-if="sellingPoints && sellingPoints.length > 0" class="space-y-4">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">Product Selling Points({{ sellingPoints.length }})</h3>
      </div>
      
      <div class="space-y-3">
        <div 
          v-for="point in sellingPoints" 
          :key="point.id || point.name"
          class="bg-gray-50 rounded-lg p-4 border border-gray-200"
        >
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-700 font-medium">{{ point.selling_point_name || 'No name provided' }}</p>
                <p v-if="point.selling_point_name_chn" class="text-gray-600 text-sm mt-1">{{ point.selling_point_name_chn }}</p>
                <p class="text-gray-600 mt-1">{{ point.selling_point_value || 'No description provided' }}</p>
                <p v-if="point.selling_point_value_chn" class="text-gray-600 text-sm mt-1">{{ point.selling_point_value_chn }}</p>
              </div>

              <div>
                <span
                    v-if="point.status"
                    :class="{
                    'bg-blue-100 text-blue-800': point.status === 'fresh',
                    'bg-yellow-100 text-yellow-800': point.status === 'editing',
                    'bg-green-100 text-green-800': point.status === 'ready',
                    'bg-gray-100 text-gray-800': point.status === 'disabled',
                    'bg-red-100 text-red-800': point.status === 'archived'
                  }"
                    class="px-2 py-1 text-xs font-medium rounded-full capitalize"
                >
                  {{ point.status }}
                </span>
                <div v-if="point.last_edited_at" class="text-xs text-gray-500 text-right">
                  Last edited: {{ new Date(point.last_edited_at).toLocaleString() }}
                </div>
              </div>
            </div>
            
            <div v-if="point.selling_point_marketing_copies && point.selling_point_marketing_copies.length > 0">
              <h5 class="font-medium text-gray-900">Marketing Copies:</h5>
              <div class="space-y-2">
                <div 
                  v-for="(copy, index) in point.selling_point_marketing_copies"
                  :key="index"
                  class="bg-white rounded p-2 border border-gray-200"
                >
                  <p class="text-gray-700">{{ copy }}</p>
                  <p 
                    v-if="point.selling_point_marketing_copies_chn && point.selling_point_marketing_copies_chn[index]"
                    class="text-gray-600 text-sm mt-1"
                  >
                    {{ point.selling_point_marketing_copies_chn[index] }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="text-center p-8 text-gray-500">
      No selling points available for this product.
    </div>
  </div>
</template>