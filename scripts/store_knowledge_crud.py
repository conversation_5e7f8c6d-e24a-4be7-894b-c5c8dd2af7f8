#!/usr/bin/env python3
"""
Store Knowledge Point CRUD 脚本
用于管理 Store Knowledge Points 的增删改查操作
通过 REST API 接口操作，不直接操作 qdrant_importer
"""

import json
import argparse
import sys
import os
import requests
from typing import List, Dict, Any, Optional
from uuid import uuid4


class StoreKnowledgeAPIClient:
    """Store Knowledge Point REST API 客户端"""

    def __init__(self, base_url: str = "https://shop-pupil.leyanbot.com/api/v1", api_key: str = ""):
        """
        初始化 API 客户端

        Args:
            base_url: API 基础 URL
            api_key: API 密钥
        """
        self.base_url = base_url.rstrip('/')
        self.api_prefix = "/inner"
        self.api_key = api_key

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送 HTTP 请求"""
        url = f"{self.base_url}{self.api_prefix}{endpoint}"
        # 添加 API key 到 headers
        headers = kwargs.get('headers', {})
        headers['apikey'] = self.api_key
        kwargs['headers'] = headers

        try:
            response = requests.request(method, url, **kwargs)
            response.raise_for_status()

            # 如果响应是空的，返回成功状态
            if not response.text:
                return {"success": True}

            return response.json()
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"API 请求失败: {method} {url}"
            }

    def create_point(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建单个知识点（通过更新接口实现）"""
        try:
            # 生成 point_id 如果没有提供
            if 'point_id' not in data:
                data['point_id'] = str(uuid4())

            point_id = data['point_id']

            # 使用 PUT 接口创建/更新知识点
            result = self._make_request(
                "PUT",
                f"/store_knowledge_point/{point_id}",
                json=data
            )

            if result.get("success", True):  # API 成功或没有 success 字段表示成功
                return {
                    "success": True,
                    "point_id": point_id,
                    "message": "知识点创建成功",
                    "data": result
                }
            else:
                return result

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "知识点创建失败"
            }

    def get_point(self, point_id: str) -> Dict[str, Any]:
        """获取单个知识点"""
        return self._make_request("GET", f"/store_knowledge_point/{point_id}")

    def update_point(self, point_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """更新知识点"""
        # 确保 point_id 在数据中
        data['point_id'] = point_id
        return self._make_request("PUT", f"/store_knowledge_point/{point_id}", json=data)

    def delete_point(self, point_id: str) -> Dict[str, Any]:
        """删除知识点"""
        return self._make_request("DELETE", f"/store_knowledge_point/{point_id}")

    def list_points(
        self, store_domain: Optional[str] = None,
        source: Optional[str] = None,
        source_detail: Optional[str] = None,
        limit: int = 10, offset: int = 0
    ) -> Dict[str, Any]:
        """列出知识点"""
        params = {"limit": limit, "offset": offset}

        if store_domain:
            params["store_domain"] = store_domain
        if source:
            params["source"] = source
        if source_detail:
            params["source_detail"] = source_detail

        return self._make_request("GET", "/store_knowledge_point", params=params)

    def search_points(
        self, query: str, store_domain: Optional[str] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """搜索知识点"""
        params = {"query": query, "limit": limit}

        if store_domain:
            params["store_domain"] = store_domain

        return self._make_request("GET", "/store_knowledge_point/search", params=params)

    def batch_create_points(self, data_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量创建知识点"""
        results: Dict[str, Any] = {
            "success_count": 0,
            "failed_count": 0,
            "successes": [],
            "failures": []
        }

        for i, data in enumerate(data_list):
            print(f"正在处理第 {i+1}/{len(data_list)} 条数据...")
            print(f"数据内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            result = self.create_point(data)
            print(f"创建结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result["success"]:
                results["success_count"] += 1
                results["successes"].append(result)
            else:
                results["failed_count"] += 1
                results["failures"].append(result)

        return results

    def batch_delete_by_filter(
        self, store_domain: Optional[str] = None,
        source: Optional[str] = None,
        source_detail: Optional[str] = None
    ) -> Dict[str, Any]:
        """根据筛选条件批量删除知识点"""
        # 先列出符合条件的知识点
        points_result = self.list_points(
            store_domain=store_domain,
            source=source,
            source_detail=source_detail,
            limit=1000  # 获取更多数据
        )

        # 检查API响应是否成功
        if isinstance(points_result, dict) and not points_result.get("success", True):
            return {
                "success": False,
                "error": "无法获取知识点列表",
                "message": "批量删除失败",
                "details": points_result
            }

        # 处理不同的响应格式
        points_list = []
        if isinstance(points_result, list):
            points_list = points_result
        elif isinstance(points_result, dict) and "data" in points_result:
            points_list = points_result["data"]
        elif isinstance(points_result, dict) and "points" in points_result:
            points_list = points_result["points"]
        else:
            return {
                "success": False,
                "error": "无法解析知识点列表",
                "message": "批量删除失败",
                "response": points_result
            }

        results: Dict[str, Any] = {
            "success_count": 0,
            "failed_count": 0,
            "successes": [],
            "failures": []
        }

        # 逐个删除
        for point in points_list:
            point_id = point.get("point_id")
            if point_id:
                result = self.delete_point(point_id)
                if result.get("success", True):
                    results["success_count"] += 1
                    results["successes"].append({"point_id": point_id})
                else:
                    results["failed_count"] += 1
                    results["failures"].append({"point_id": point_id, "error": result})

        return results


def load_json_data(file_path: str) -> Any:
    """从文件加载 JSON 数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading JSON file {file_path}: {e}")
        return None


def save_json_data(data: Any, file_path: str) -> bool:
    """保存数据到 JSON 文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"Error saving JSON file {file_path}: {e}")
        return False


def format_output(data: Any) -> str:
    """格式化输出数据"""
    if isinstance(data, (dict, list)):
        return json.dumps(data, ensure_ascii=False, indent=2)
    return str(data)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Store Knowledge CRUD API 客户端")
    parser.add_argument(
        "--base-url", default="https://shop-pupil.leyanbot.com/api/v1",
        help="API 基础 URL (默认: https://shop-pupil.leyanbot.com/api/v1)"
    )
    parser.add_argument(
        "--api-key", default="",
        help="API 密钥"
    )

    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 创建命令
    create_parser = subparsers.add_parser("create", help="创建知识点")
    create_group = create_parser.add_mutually_exclusive_group(required=True)
    create_group.add_argument("--file", help="从 JSON 文件创建")
    create_group.add_argument("--data", help="从 JSON 字符串创建")

    # 获取命令
    get_parser = subparsers.add_parser("get", help="获取知识点")
    get_parser.add_argument("point_id", help="知识点 ID")

    # 更新命令
    update_parser = subparsers.add_parser("update", help="更新知识点")
    update_parser.add_argument("point_id", help="知识点 ID")
    update_parser.add_argument("--data", required=True, help="更新数据 (JSON 字符串)")

    # 删除命令
    delete_parser = subparsers.add_parser("delete", help="删除知识点")
    delete_parser.add_argument("point_id", help="知识点 ID")

    # 列表命令
    list_parser = subparsers.add_parser("list", help="列出知识点")
    list_parser.add_argument("--store-domain", help="按店铺域名筛选")
    list_parser.add_argument("--source", help="按来源筛选")
    list_parser.add_argument("--source-detail", help="按来源详情筛选")
    list_parser.add_argument("--limit", type=int, default=10, help="限制数量")
    list_parser.add_argument("--offset", type=int, default=0, help="偏移量")

    # 搜索命令
    search_parser = subparsers.add_parser("search", help="搜索知识点")
    search_parser.add_argument("query", help="搜索查询")
    search_parser.add_argument("--store-domain", help="按店铺域名筛选")
    search_parser.add_argument("--limit", type=int, default=10, help="限制数量")

    # 批量删除命令
    batch_delete_parser = subparsers.add_parser("batch-delete", help="批量删除知识点")
    batch_delete_parser.add_argument("--store-domain", help="按店铺域名筛选")
    batch_delete_parser.add_argument("--source", help="按来源筛选")
    batch_delete_parser.add_argument("--source-detail", help="按来源详情筛选")

    # 特定任务命令
    subparsers.add_parser("specific-task", help="执行特定任务")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # 创建 API 客户端
    client = StoreKnowledgeAPIClient(args.base_url, args.api_key)

    try:
        if args.command == "create":
            if args.file:
                data = load_json_data(args.file)
                if data is None:
                    sys.exit(1)
            else:
                data = json.loads(args.data)

            if isinstance(data, list):
                result = client.batch_create_points(data)
            else:
                result = client.create_point(data)

            print(format_output(result))

        elif args.command == "get":
            result = client.get_point(args.point_id)
            print(format_output(result))

        elif args.command == "update":
            data = json.loads(args.data)
            result = client.update_point(args.point_id, data)
            print(format_output(result))

        elif args.command == "delete":
            result = client.delete_point(args.point_id)
            print(format_output(result))

        elif args.command == "list":
            result = client.list_points(
                store_domain=args.store_domain,
                source=args.source,
                source_detail=args.source_detail,
                limit=args.limit,
                offset=args.offset
            )
            print(format_output(result))

        elif args.command == "search":
            result = client.search_points(
                query=args.query,
                store_domain=args.store_domain,
                limit=args.limit
            )
            print(format_output(result))

        elif args.command == "batch-delete":
            result = client.batch_delete_by_filter(
                store_domain=args.store_domain,
                source=args.source,
                source_detail=args.source_detail
            )
            print(format_output(result))

        elif args.command == "specific-task":
            # 执行特定任务：删除指定条件的数据，然后导入新数据
            print("执行特定任务...")

            # 1. 删除现有数据
            print("1. 删除现有数据...")
            delete_result = client.batch_delete_by_filter(
                store_domain="cyxus.myshopify.com",
                source="blogs",
                source_detail="https://cyxus.com/blogs/eye-care/how-to-order-prescription-glasses-online"
            )
            print(f"删除结果: {delete_result.get('success_count', 0)} 成功, {delete_result.get('failed_count', 0)} 失败")

            # 2. 从 sample_data.json 导入新数据
            print("2. 从 sample_data.json 导入新数据...")

            # 获取脚本所在目录
            script_dir = os.path.dirname(os.path.abspath(__file__))
            sample_data_path = os.path.join(script_dir, "sample_data.json")

            # 读取示例数据
            new_data = load_json_data(sample_data_path)
            if new_data is None:
                print("错误: 无法读取 sample_data.json 文件")
                sys.exit(1)

            create_result = client.batch_create_points(new_data)
            print(f"导入结果: {create_result.get('success_count', 0)} 成功, {create_result.get('failed_count', 0)} 失败")

            print("特定任务完成！")

    except json.JSONDecodeError as e:
        print(f"JSON 解析错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
