from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE "page_element_selling_point" (
    "pageelement_id" INT NOT NULL REFERENCES "page_elements" ("id") ON DELETE CASCADE,
    "product_selling_point_id" INT NOT NULL REFERENCES "product_selling_point" ("id") ON DELETE CASCADE
);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "page_element_selling_point";"""
