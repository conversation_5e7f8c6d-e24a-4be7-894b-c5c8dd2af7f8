<script setup>
import { ref } from 'vue'
import CrawlTaskTrigger from '../task/CrawlTaskTrigger.vue'
import { ChevronDown } from 'lucide-vue-next'

const props = defineProps({
  productType: {
    type: String,
    required: true
  },
  aspectList: {
    type: Array,
    required: true
  },
  domain: {
    type: String,
    required: true
  }
})

const isExpanded = ref(false)

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}
</script>

<template>
  <div class="bg-white rounded-lg shadow overflow-hidden">
    <div class="flex justify-between items-center p-4 bg-gray-50 border-b border-gray-200 cursor-pointer transition-colors duration-200 hover:bg-gray-100" @click="toggleExpand">
      <div class="flex items-center gap-2">
        <button class="bg-transparent border-none p-1 cursor-pointer flex items-center justify-center text-gray-600 transition-transform duration-200" :class="{ 'rotate-180': isExpanded }">
          <ChevronDown size="16" />
        </button>
        <h3 class="m-0 text-gray-800 text-lg font-semibold">{{ productType }} ({{ aspectList.length }})</h3>
      </div>
      <CrawlTaskTrigger
        taskName="extract_and_save_single_product_type_aspects"
        displayName="Re-extract Aspects"
        :defaultParams="{ domain: domain, product_type: productType, clear_existing: true, do_expand: true }"
      />
    </div>
    <div v-show="isExpanded" class="p-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div v-for="aspect in aspectList" :key="aspect.name" class="bg-gray-50 p-4 rounded-md transition-all duration-200 hover:-translate-y-0.5 hover:shadow-md">
        <h4 class="m-0 mb-2 text-gray-800 text-base font-semibold">{{ aspect.name }}</h4>
        <p class="m-0 text-gray-600 leading-relaxed">{{ aspect.description }}</p>
      </div>
    </div>
  </div>
</template> 