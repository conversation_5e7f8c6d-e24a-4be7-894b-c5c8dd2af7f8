<template>
  <Card>
    <CardHeader>
      <CardTitle>Page Element Annotation</CardTitle>
      <CardDescription>
        We have <span class="font-mono px-1 bg-muted">{{ pageElementCount }}</span> elements annotated on this page.
      </CardDescription>
    </CardHeader>
    <CardContent>
      <Alert>
        <AlertTitle>Ready to Annotate?</AlertTitle>
        <AlertDescription>
          Go to the original product page to start marking elements with our browser extension. This will help our AI understand the page structure.
        </AlertDescription>
      </Alert>
    </CardContent>
    <CardFooter>
      <Button as-child>
        <a :href="productUrl" target="_blank">Go to Origin Page</a>
      </Button>
    </CardFooter>
  </Card>
</template>

<script setup>
import { ref, watchEffect } from 'vue'
import { api } from '@/api/api'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Al<PERSON>, AlertDescription, AlertTitle } from '@/components/ui/alert'

const props = defineProps({
  productUrl: {
    type: String,
    required: true
  }
})

const pageElementCount = ref(0)

watchEffect(async () => {
  if (!props.productUrl) return
  try {
    const { data } = await api.get(`/api/v1/behavior-analysis/page-elements`, {
      params: {
        url: props.productUrl
      }
    })
    pageElementCount.value = data.length
  } catch (error) {
    console.error('Failed to fetch page elements:', error)
    pageElementCount.value = 0
  }
})
</script>