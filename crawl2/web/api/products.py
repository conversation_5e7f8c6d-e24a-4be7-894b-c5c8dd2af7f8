from fastapi import APIRouter, HTTPException
from typing import List

from crawl2 import schema, utils, workflows
from crawl2.db import operations
from crawl2.db import models


router = APIRouter(prefix="/products", tags=["products"])


@router.get("/{domain}/{product_id}")
async def get_product_details(domain: str, product_id: str) -> schema.ProductDetails:
    """获取产品详情"""
    details = await operations.get_product_details(domain, product_id)
    if not details:
        raise HTTPException(status_code=404, detail=f"Product {product_id} not found")
    return details


@router.put("")
async def crawl_product(req: schema.CrawlProductReq) -> schema.MessageOnlyResponse:
    """爬取一个指定商品的数据，如果这个产品在 shop-pupil 中已经存在，则旧数据将被覆盖."""
    if not req.product.startswith("https://") and not req.product.startswith("http://"):
        product_metadata = await operations.get_product_metadata_by_product_id_only(req.product)
        if not product_metadata:
            raise HTTPException(status_code=404, detail=f"Product {req.product} not found")
        product_url = product_metadata.url
    else:
        product_url = req.product

    # 从 URL 中提取域名
    domain = utils.extract_domain_from_shopify_site_url(product_url)

    # 检查站点是否已存在
    existing_site = await operations.get_site(domain)
    if not existing_site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    # 创建并启动商品爬取工作流
    celery_chain = workflows.create_crawl_single_product_workflow(product_url)
    await workflows.trigger_workflow('crawl_product', celery_chain)
    return schema.MessageOnlyResponse(message="Product crawling started successfully")


@router.put("/sites/{domain}/products/{product_id}/knowledge_documents")
async def replace_product_knowledge_documents(
    domain: str,
    product_id: str,
    documents: List[schema.ProductKnowledgeWithTopic],
) -> schema.MessageOnlyResponse:
    """替换指定产品的全部知识文档。用于一次性编辑/保存，亦支持前端删除部分文档后整体覆盖。"""
    metadata = await models.ProductMetadata.filter(
        site__domain=domain, product_id=product_id
    ).get_or_none()
    if not metadata:
        raise HTTPException(status_code=404, detail=f"Product {product_id} not found under site {domain}")

    knowledge = await operations.get_product_knowledge(metadata.url)
    if not knowledge:
        raise HTTPException(status_code=404, detail="Product knowledge not found")

    # 允许空列表（清空）或若干文档；过滤掉完全空白的content
    sanitized = []
    for d in documents:
        model = (
            d if isinstance(d, schema.ProductKnowledgeWithTopic)
            else schema.ProductKnowledgeWithTopic.model_validate(d)
        )
        if model.content is not None and model.content.strip() != "":
            sanitized.append(model.model_dump())

    knowledge.documents = sanitized
    await knowledge.save()

    return schema.MessageOnlyResponse(message="Knowledge documents replaced successfully")
