import contextlib
import os.path
import tempfile
from functools import lru_cache


import httpx
from leyan_tracing.httpx.httpx_tracing import AsyncClient

from crawl2.config import settings


class StoreCenterClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key
        self.client = AsyncClient(
            base_url=self.base_url,
            headers={"Authorization": f"Bearer {self.api_key}"},
            timeout=60,
        )

    @classmethod
    @lru_cache
    def get_client(cls) -> 'StoreCenterClient':
        return cls(settings.STORE_CENTER_BASE_URL, settings.STORE_CENTER_API_KEY)

    async def get_file_download_url(self, file_key: str) -> str:
        """
        根据 file_key 从 store-center 获取文件下载链接, 返回的链接通常只有短暂的有效时间.

        api 描述: https://app.apifox.com/project/5849586/apis/api-317866765

        :param file_key:  文件的唯一标识符.
        :return: https 下载链接.
        """
        resp = await self.client.get('/resource/v1/url', params={'fileKey': file_key})
        resp.raise_for_status()
        data = resp.json()
        if data.get('code') != 200:
            raise Exception(f"Failed to get download URL for {file_key}: {data.get('message', 'Unknown error')}")
        return data['data']

    @contextlib.asynccontextmanager
    async def download_file(self, file_key: str):
        """
        从 store-center 下载文件，并返回一个临时文件对象.

        使用方法示例1- 使用返回文件对象的路径:
        async with client.download_file('2025/07/04/1751619440156/9.9活动转化.xlsx') as f:
            # do something with f.name, which is an absolute path to the temporary file
            pass

        使用方法示例2- 直接操作返回的文件对象:
        async with client.download_file('2025/07/04/1751619440156/9.9活动转化.xlsx') as f:
            # do something with f, which behaves just like a file object returned by open('rb')
            pass
        """
        url = await self.get_file_download_url(file_key)
        with httpx.stream('GET', url) as resp:
            resp.raise_for_status()
            file_ext = os.path.splitext(file_key)[-1]
            with tempfile.NamedTemporaryFile(suffix=file_ext) as f:
                for bytes in resp.iter_bytes():
                    f.write(bytes)
                f.seek(0)
                yield f
