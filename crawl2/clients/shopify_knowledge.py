import abc
from enum import Enum
from functools import lru_cache
from typing import ClassVar

import httpx
from loguru import logger
from pydantic import BaseModel

from crawl2.config import settings


class ProductFaqItem(BaseModel):
    """
    ref: https://app.apifox.com/project/5849586/apis/api-299910811
         {
         "spuId": "string",
         "question": "string",
         "answer": "string",
         "sort": 0,
         "isActive": True,
         "externalId": "string"
      }
    """
    spuId: str
    question: str
    answer: str
    sort: int = 0
    isActive: bool = True
    externalId: str = ""


class UpdateProductFaqsReq(BaseModel):
    """
    ref: https://app.apifox.com/project/5849586/apis/api-299910811
    {
    "storeDomain": "string",
    "source": "string",
    "faqList": [
        {
            "spuId": "string",
            "question": "string",
            "answer": "string",
            "sort": 0,
            "isActive": true,
            "externalId": "string"
        }
    ]
    }
    """
    storeDomain: str
    source: str
    faqList: list[ProductFaqItem]


class ProductKeywordItem(BaseModel):
    productType: str
    keyword: str
    productIdList: list[str]


class ImportProductKeywordsReq(BaseModel):
    storeDomain: str
    items: list[ProductKeywordItem]


class ProductAttributesItem(BaseModel):
    productType: str
    attributes: str  # json string


class ImportProductAttributesReq(BaseModel):
    storeDomain: str
    items: list[ProductAttributesItem]


class ProductMetaItem(BaseModel):
    productUrl: str
    productType: str
    productId: str
    productTitle: str
    productTags: str
    productPage: str
    variantInfo: str
    propertyValues: str
    productSummary: str


class ImportProductMetaReq(BaseModel):
    storeDomain: str
    items: list[ProductMetaItem]


class ProductKnowledgePointItem(BaseModel):
    productId: str
    pointId: str
    title: str
    content: str
    label: str
    source: str


class ImportProductKnowledgePointReq(BaseModel):
    storeDomain: str
    items: list[ProductKnowledgePointItem]


class ShopifyKnowledgeCategory(str, Enum):
    SELLING_POINT = 'SELLING_POINT'
    SCENARIO_SELLING_POINT = 'SCENARIO_SELLING_POINT'
    PRODUCT_TYPE_SCENARIO = 'PRODUCT_TYPE_SCENARIO'
    CONCERN = 'CONCERN'
    BRAND_HISTORY = "BRAND_HISTORY"
    PRODUCT_CERTIFICATION = "PRODUCT_CERTIFICATION"
    AWARDS_HONOR = "AWARDS_HONOR"
    OUR_PARTNER = "OUR_PARTNER"
    PRESS_MEDIA = "PRESS_MEDIA"
    PRICE_MATCH_GUARANTEE = "PRICE_MATCH_GUARANTEE"
    RETURN_PROTECTION = "RETURN_PROTECTION"
    WARRANTY_PERIOD = "WARRANTY_PERIOD"
    WARRANTY_EXCLUSION = "WARRANTY_EXCLUSION"
    WARRANTY_CLAIM_PROCESS = "WARRANTY_CLAIM_PROCESS"
    HASSLE_FREE_RETURN = "HASSLE_FREE_RETURN"
    HASSLE_FREE_RETURN_ELIGIBILITY = "HASSLE_FREE_RETURN_ELIGIBILITY"
    DEFECTIVE_PRODUCT_RESOLUTION = "DEFECTIVE_PRODUCT_RESOLUTION"
    RETURN_REQUEST_PROCESS = "RETURN_REQUEST_PROCESS"
    RETURN_EXCHANGE_FEES = "RETURN_EXCHANGE_FEES"
    FREE_RETURN_ELIGIBILITY = "FREE_RETURN_ELIGIBILITY"
    ORDER_SHIPPING_COST = "ORDER_SHIPPING_COST"
    FREE_SHIPPING_THRESHOLD = "FREE_SHIPPING_THRESHOLD"
    PROCESSING_TIME = "PROCESSING_TIME"
    ESTIMATED_DELIVERY_TIME = "ESTIMATED_DELIVERY_TIME"
    SERVICEABLE_AREA = "SERVICEABLE_AREA"
    AVAILABLE_SHIPPING_METHOD = "AVAILABLE_SHIPPING_METHOD"
    ADDRESS_ERROR_RESOLUTION = "ADDRESS_ERROR_RESOLUTION"
    ORDER_CANCELLATION_POLICY = "ORDER_CANCELLATION_POLICY"
    CUSTOMS_IMPORT_DUTIES = "CUSTOMS_IMPORT_DUTIES"
    SALES_TAX = "SALES_TAX"


class ShopifyKnowledgePayload(BaseModel, abc.ABC):
    # ref: https://git.leyantech.com/oversea/shopify/shopify-knowledge/-/issues/2
    category: ClassVar[ShopifyKnowledgeCategory]


class ProductSellingPointItem(BaseModel):
    aspect: str
    description: str
    marketingCopies: list[str]
    element_targets: list[str] = []


class StoreKnowledgeValueItem(ShopifyKnowledgePayload):
    value: str


class BrandHistoryValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.BRAND_HISTORY


class ProductCertificationValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.PRODUCT_CERTIFICATION


class AwardsHonorValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.AWARDS_HONOR


class OurPartnerValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.OUR_PARTNER


class PressMediaValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.PRESS_MEDIA


class PriceMatchGuaranteeValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.PRICE_MATCH_GUARANTEE


class ReturnProtectionValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.RETURN_PROTECTION


class WarrantyPeriodValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.WARRANTY_PERIOD


class WarrantyExclusionValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.WARRANTY_EXCLUSION


class WarrantyClaimProcessValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.WARRANTY_CLAIM_PROCESS


class HassleFreeReturnValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.HASSLE_FREE_RETURN


class HassleFreeReturnEligibilityValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.HASSLE_FREE_RETURN_ELIGIBILITY


class DefectiveProductResolutionValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.DEFECTIVE_PRODUCT_RESOLUTION


class ReturnRequestProcessValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.RETURN_REQUEST_PROCESS


class ReturnExchangeFeesValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.RETURN_EXCHANGE_FEES


class FreeReturnEligibilityValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.FREE_RETURN_ELIGIBILITY


class OrderShippingCostValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.ORDER_SHIPPING_COST


class FreeShippingThresholdValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.FREE_SHIPPING_THRESHOLD


class ProcessingTimeValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.PROCESSING_TIME


class EstimatedDeliveryTimeValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.ESTIMATED_DELIVERY_TIME


class ServiceableAreaValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.SERVICEABLE_AREA


class AvailableShippingMethodValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.AVAILABLE_SHIPPING_METHOD


class AddressErrorResolutionValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.ADDRESS_ERROR_RESOLUTION


class OrderCancellationPolicyValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.ORDER_CANCELLATION_POLICY


class CustomsImportDutiesValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.CUSTOMS_IMPORT_DUTIES


class SalesTaxValueItem(StoreKnowledgeValueItem):
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.SALES_TAX


class ProductSellingPoints(ShopifyKnowledgePayload):
    """
    {
     spuId = "",
     sellingPointsList:[
         aspect = "";
         description = "";
         marketingCopies =  [""],
         element_targets = [""]
     ]
    }
    """
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.SELLING_POINT
    spuId: str
    sellingPointsList: list[ProductSellingPointItem]


class ProductConcernItem(BaseModel):
    aspect: str
    marketingCopy: str


class ProductConcerns(ShopifyKnowledgePayload):
    """
       {
     spuId = "",
     concernList:[
        aspect = "";
        marketingCopy = "";
     ]
    }
    """
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.CONCERN
    spuId: str
    concernList: list[ProductConcernItem]


class ScenarioSellingPointItem(BaseModel):
    scenario: str
    content: str
    element_targets: list[str] = []


class ProductScenarioSellingPoint(ShopifyKnowledgePayload):
    """
    {
     "spuId" = "",
     "scenarioSellingPoints": [
         {
             scenario: "",
             content: ""
         }
     ]
    }
    """
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.SCENARIO_SELLING_POINT
    spuId: str
    scenarioSellingPoints: list[ScenarioSellingPointItem]


class ProductTypeScenarioItem(BaseModel):
    label: str
    explanation: str


class ProductTypeScenario(ShopifyKnowledgePayload):
    """
    {
        "scenarios": [
            {
                "label": "",
                "explanation": ""
            }
        ]
    }
    """
    category: ClassVar[ShopifyKnowledgeCategory] = ShopifyKnowledgeCategory.PRODUCT_TYPE_SCENARIO
    scenarios: list[ProductTypeScenarioItem]


class StoreKnowledgeItem(BaseModel):
    pointId: str
    topic: str
    question: str
    answer: str
    source: str
    sourceDetail: str
    quality: int
    label: str
    detailedLabel: str
    extraQuestions: list[str]


class ImportStoreKnowledgeReq(BaseModel):
    storeDomain: str
    items: list[StoreKnowledgeItem]


class SyncPageElementsItem(BaseModel):
    target: str
    selectors: list[str]
    data: dict


class SyncPageElementsReq(BaseModel):
    storeDomain: str
    pageUrl: str
    pageElements: list[SyncPageElementsItem]


class ShopifyKnowledgeClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={"Authorization": f"Bearer {self.api_key}"},
            timeout=60,
        )

    @classmethod
    @lru_cache
    def get_client(cls) -> 'ShopifyKnowledgeClient':
        return cls(settings.SHOPIFY_KNOWLEDGE_BASE_URL, settings.SHOPIFY_KNOWLEDGE_API_KEY)

    async def generate_faqs(self, req: UpdateProductFaqsReq):
        # ref:  https://app.apifox.com/project/5849586/apis/api-299910811
        response = await self.client.post('/shopify-knowledge/faq/generate', json=req.model_dump())
        response.raise_for_status()
        logger.info(f"Generated {len(req.faqList)} FAQs for {req.storeDomain} succeed: {response.text}")

    async def import_product_keywords(self, req: ImportProductKeywordsReq):
        response = await self.client.post('/shopify-knowledge/product-keywords/import', json=req.model_dump())
        response.raise_for_status()
        logger.info(f"Imported {len(req.items)} product keywords for {req.storeDomain} succeed: {response.text}")
        return response.json()

    async def import_product_attributes(self, req: ImportProductAttributesReq):
        response = await self.client.post('/shopify-knowledge/product-type-attributes/import', json=req.model_dump())
        response.raise_for_status()
        logger.info(
            f"Imported {len(req.items)} product attributes for {req.storeDomain} succeed: {response.text}")
        return response.json()

    async def import_product_meta(self, req: ImportProductMetaReq):
        response = await self.client.post('/shopify-knowledge/product-data/metadata/import', json=req.model_dump())
        response.raise_for_status()
        logger.info(f"Imported {len(req.items)} product meta for {req.storeDomain} succeed: {response.text}")
        return response.json()

    async def import_product_knowledge_point(self, req: ImportProductKnowledgePointReq):
        response = await self.client.post('/shopify-knowledge/product-data/knowledge-point/import', json=req.model_dump())  # NOQA
        response.raise_for_status()
        logger.info(f"Imported {len(req.items)} product knowledge point for {req.storeDomain} succeed: {response.text}")
        return response.json()

    async def delete_all_product_metadata(self, store_domain: str):
        """Delete all product metadata for a specific store domain"""
        response = await self.client.post(f'/shopify-knowledge/product-data/metadata/delete?storeDomain={store_domain}')  # NOQA
        response.raise_for_status()
        logger.info(f"Deleted product metadata for {store_domain} succeed: {response.text}")
        return response.json()

    async def delete_all_product_knowledge_point(self, store_domain: str):
        """Delete all product knowledge points for a specific store domain"""
        response = await self.client.post(f'/shopify-knowledge/product-data/knowledge-point/delete?storeDomain={store_domain}')  # NOQA
        response.raise_for_status()
        logger.info(f"Deleted product knowledge points for {store_domain} succeed: {response.text}")
        return response.json()

    async def delete_single_product_metadata(self, store_domain: str, product_id: str):
        """Delete all product knowledge points for a specific store domain"""
        response = await self.client.post(f'/shopify-knowledge/product-data/metadata/delete-by-product?storeDomain={store_domain}&productId={product_id}')  # NOQA
        response.raise_for_status()
        logger.info(f"Deleted product metadata for {store_domain}, product_id: {product_id}, succeed: {response.text}")
        return response.json()

    async def delete_single_product_knowledge_point(self, store_domain: str, product_id: str):
        """Delete all product knowledge points for a specific store domain"""
        response = await self.client.post(f'/shopify-knowledge/product-data/knowledge-point/delete-by-product?storeDomain={store_domain}&productId={product_id}')  # NOQA
        response.raise_for_status()
        logger.info(f"Deleted product knowledge points for {store_domain}, product_id: {product_id}, succeed: {response.text}")# NOQA
        return response.json()

    async def import_store_knowledge(self, req: ImportStoreKnowledgeReq):
        response = await self.client.post('/shopify-knowledge/store-data/knowledge-point/import', json=req.model_dump())  # NOQA
        response.raise_for_status()
        logger.info(f"Imported {len(req.items)} store knowledge for {req.storeDomain} succeed: {response.text}")
        return response.json()

    async def delete_all_store_knowledge(self, store_domain: str):
        """Delete all store knowledge for a specific store domain"""
        response = await self.client.post(f'/shopify-knowledge/store-data/knowledge-point/delete?storeDomain={store_domain}')  # NOQA
        response.raise_for_status()
        logger.info(f"Deleted store knowledge for {store_domain} succeed: {response.text}")
        return response.json()

    async def notify_task_status(self, task_id: str, fail_reason: str | None = None):
        # ref: https://app.apifox.com/project/5849586/apis/api-317286065
        payload = {'taskId': task_id}
        if fail_reason:
            payload['failureReason'] = fail_reason
        await self.client.post('/shopify-knowledge/task/v1/notify', json=payload)

    async def sync_shopify_knowledge(self, store_domain: str, payload: ShopifyKnowledgePayload):
        """
        ref: https://git.leyantech.com/oversea/shopify/shopify-knowledge/-/issues/2

        :param store_domain: shopify 站点的 myshopify_domain
        :param payload: ShopifyKnowledgePayload 的子类实例，包含要同步的知识点数据
        """
        category = payload.category.value
        payload_json = payload.model_dump()
        url = f'/shopify-knowledge/v1/callback/{store_domain}/{category}'
        response = await self.client.post(url, json=payload_json)
        logger.info(f'sync knowledge {category} for {store_domain} with {payload_json} response: {response.text}')  # NOQA
        response.raise_for_status()
        return response.json()

    async def sync_page_elements(self, payload: SyncPageElementsReq):
        """同步标注好的页面元素到 shopify-knowledge 服务"""
        payload_json = payload.model_dump()
        url = "/shopify-knowledge/page-elements/sync"
        response = await self.client.post(url, json=payload_json)
        logger.info(f"sync page elements for {payload.pageUrl} response: {response.text}")
        response.raise_for_status()


def create_store_knowledge_value_item(detailed_label: str, value: str) -> StoreKnowledgeValueItem | None:
    """
    根据 detailed_label 创建对应的 StoreKnowledgeValueItem 子类实例
    """
    category_mappings = {
        "品牌历史": BrandHistoryValueItem,
        "产品认证": ProductCertificationValueItem,
        "所获奖项": AwardsHonorValueItem,
        "合作伙伴": OurPartnerValueItem,
        "媒体报道": PressMediaValueItem,
        "保价政策": PriceMatchGuaranteeValueItem,
        "退货险": ReturnProtectionValueItem,
        "退货运费险": ReturnProtectionValueItem,
        "保修期": WarrantyPeriodValueItem,
        "保修免责条款": WarrantyExclusionValueItem,
        "申请保修方式": WarrantyClaimProcessValueItem,
        "无理由退换保证": HassleFreeReturnValueItem,
        "无理由退换条件": HassleFreeReturnEligibilityValueItem,
        "商品问题处理方式": DefectiveProductResolutionValueItem,
        "退换发起方式": ReturnRequestProcessValueItem,
        "退换费用": ReturnExchangeFeesValueItem,
        "免费退换条件": FreeReturnEligibilityValueItem,
        "订单运费": OrderShippingCostValueItem,
        "免运费条件": FreeShippingThresholdValueItem,
        "发货时间": ProcessingTimeValueItem,
        "配送时间": EstimatedDeliveryTimeValueItem,
        "可送达地区": ServiceableAreaValueItem,
        "可选运输方式": AvailableShippingMethodValueItem,
        "地址错误处理方式": AddressErrorResolutionValueItem,
        "取消订单政策": OrderCancellationPolicyValueItem,
        "关税/进口税": CustomsImportDutiesValueItem,
        "销售税": SalesTaxValueItem,
    }

    item_class = category_mappings.get(detailed_label)
    if item_class:
        return item_class(value=value)
    return None
