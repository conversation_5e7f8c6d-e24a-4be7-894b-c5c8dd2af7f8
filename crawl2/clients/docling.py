import httpx
from loguru import logger

from crawl2.config import settings


async_client = httpx.AsyncClient()


async def get_file_markdown_content(file_url):
    data = {
        "file_url": file_url
    }
    try:
        resp = await async_client.post(url=settings.DOCLING_ENDPOINT, json=data)
        return resp.text
    except Exception as e:
        logger.opt(exception=e).error("fail to get file content: {}", file_url)
    return None
