<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { api } from '@/api/api.js'
import Navigation from '../components/common/Navigation.vue'
import TaskItem from '../components/task/TaskItem.vue'

const route = useRoute()
const workflow = ref(null)
const loading = ref(true)
const error = ref(null)
let pollTimer = null

// 判断工作流是否处于终结状态
const isWorkflowFinished = computed(() => {
  if (!workflow.value) return true
  return ['SUCCESS', 'FAILURE', 'FAILED', 'CANCELED'].includes(workflow.value.status)
})

// 计算待执行的任务
const pendingTasks = computed(() => {
  if (!workflow.value?.signature?.tasks) return []
  
  const executedTaskNames = new Set(
    workflow.value.tasks?.map(task => task.name) || []
  )
  
  return workflow.value.signature.tasks.filter(
    signatureTask => !executedTaskNames.has(signatureTask.name)
  )
})

// 计算已执行的任务
const executedTasks = computed(() => {
  return workflow.value?.tasks || []
})

const fetchWorkflowDetails = async () => {
  try {
    const workflowId = route.params.workflowId
    const response = await api.get(`/api/v1/workflows/runs/${workflowId}`)
    workflow.value = response.data
  } catch (err) {
    error.value = err.message
    console.error('Error fetching workflow details:', err)
  } finally {
    loading.value = false
  }
}

// 开始轮询
const startPolling = () => {
  if (pollTimer) {
    clearInterval(pollTimer)
  }
  
  // 如果工作流未完成，开始轮询
  if (!isWorkflowFinished.value) {
    pollTimer = setInterval(fetchWorkflowDetails, 1000) // 1秒轮询一次
  }
}

// 停止轮询
const stopPolling = () => {
  if (pollTimer) {
    clearInterval(pollTimer)
    pollTimer = null
  }
}

// 监听工作流状态变化，自动开始或停止轮询
const updatePolling = () => {
  if (isWorkflowFinished.value) {
    stopPolling()
  } else {
    startPolling()
  }
}

onMounted(() => {
  fetchWorkflowDetails()
})

onUnmounted(() => {
  stopPolling()
})

// 处理任务取消事件
const handleTaskCanceled = (taskId) => {
  // 找到被取消的任务并更新其状态
  if (workflow.value?.tasks) {
    const task = workflow.value.tasks.find(t => t.task_id === taskId)
    if (task) {
      task.status = 'CANCELED'
      task.error = 'Task was canceled by user'
      task.finished_at = new Date().toISOString()
    }
  }
}

// 处理任务重试事件：立即刷新工作流详情
const handleTaskRetried = async () => {
  await fetchWorkflowDetails()
  // 若工作流因失败/取消而停止轮询，重试后有机会继续运行，确保轮询状态正确
  updatePolling()
}

// 监听工作流状态变化
watch(() => workflow.value?.status, updatePolling)
</script>

<template>
  <div class="flex flex-col h-screen w-full pt-0">
    <Navigation
        :breadcrumbs="[
        { text: 'Sites', path: '/' },
        { text: 'Workflow History', path: '/workflow-history' },
        { text: `Workflow ${workflow?.id || route.params.workflowId}` }
      ]"
    />

    <div class="flex justify-between items-center p-4 px-8 w-full bg-white shadow sticky top-16 z-10">
      <div class="workflow-info" v-if="workflow">
        <h1 class="m-0 mb-2 text-2xl text-gray-800">Workflow: {{ workflow.name }}</h1>
        <div class="flex gap-8 flex-wrap">
          <span class="text-sm text-gray-600">
            <strong>ID:</strong> {{ workflow.id }}
          </span>
          <span class="text-sm text-gray-600">
            <strong>Status:</strong> 
            <span :style="{ 
              color: workflow.status === 'SUCCESS' ? '#28a745' : 
                     workflow.status === 'FAILURE' || workflow.status === 'FAILED' ? '#dc3545' : 
                     workflow.status === 'CANCELED' ? '#6c757d' : '#ffc107' 
            }">
              {{ workflow.status }}
            </span>
            <span v-if="!isWorkflowFinished" class="text-green-500 animate-pulse ml-2">●</span>
          </span>
          <span class="text-sm text-gray-600">
            <strong>Created:</strong> {{ new Date(workflow.created_at).toLocaleString() }}
          </span>
          <span class="text-sm text-gray-600">
            <strong>Updated:</strong> {{ new Date(workflow.updated_at).toLocaleString() }}
          </span>
          <span class="text-sm text-gray-600" v-if="workflow.finished_at">
            <strong>Finished:</strong> {{ new Date(workflow.finished_at).toLocaleString() }}
          </span>
        </div>
      </div>
    </div>

    <div class="flex-1 p-4 px-8 flex flex-col w-full bg-gray-50">
      <div v-if="loading" class="flex justify-center items-center h-full text-gray-600 bg-white rounded-lg shadow">
        Loading workflow details...
      </div>
      <div v-else-if="error" class="flex justify-center items-center h-full text-red-500 bg-white rounded-lg shadow">
        Error: {{ error }}
      </div>
      <div v-else-if="workflow" class="flex-1 flex flex-col">
        <div class="flex-1 bg-white rounded-lg shadow p-6">
          <h2 class="m-0 mb-6 text-xl text-gray-800">Tasks ({{ (workflow.signature?.tasks?.length || 0) }})</h2>
          
          <!-- 已执行的任务 -->
          <div v-if="executedTasks.length > 0" class="mb-8 last:mb-0">
            <h3 class="flex items-center gap-2 m-0 mb-4 text-lg font-semibold p-3 rounded-md bg-gray-50 text-green-600 border-l-4 border-green-500">
              <span class="text-xl font-bold text-green-500">✓</span>
              Executed Tasks ({{ executedTasks.length }})
            </h3>
            <div class="flex flex-col gap-0">
              <TaskItem 
                v-for="task in executedTasks" 
                :key="task.task_id" 
                :task="task"
                v-memo="[task.task_id, task.status, task.progress?.n, task.progress?.total, task.progress?.desc]"
                @task-canceled="handleTaskCanceled"
                @task-retried="handleTaskRetried"
              />
            </div>
          </div>

          <!-- 待执行的任务 -->
          <div v-if="pendingTasks.length > 0" class="mb-8 last:mb-0">
            <h3 class="flex items-center gap-2 m-0 mb-4 text-lg font-semibold p-3 rounded-md bg-yellow-50 text-yellow-800 border-l-4 border-yellow-500">
              <span class="text-xl font-bold text-yellow-500">⏳</span>
              Pending Tasks ({{ pendingTasks.length }})
            </h3>
            <div class="flex flex-col gap-3">
              <div 
                v-for="task in pendingTasks" 
                :key="task.name" 
                class="bg-gray-50 border border-gray-200 rounded-md p-4 mb-2"
              >
                <div class="flex justify-between items-center mb-2">
                  <span class="font-semibold text-gray-700 font-mono text-sm">{{ task.name }}</span>
                  <span class="bg-yellow-500 text-yellow-800 px-2 py-1 rounded text-xs font-semibold">Pending</span>
                </div>
                <div v-if="task.kwargs && Object.keys(task.kwargs).length > 0" class="mt-2">
                  <strong>Parameters:</strong>
                  <pre class="bg-gray-100 border border-gray-200 rounded p-2 text-xs text-gray-700 m-2 mt-0 overflow-x-auto whitespace-pre-wrap break-words">{{ JSON.stringify(task.kwargs, null, 2) }}</pre>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="!executedTasks.length && !pendingTasks.length" class="flex justify-center items-center h-full text-gray-600 bg-white rounded-lg shadow">
            No tasks found for this workflow.
          </div>
        </div>
      </div>
      <div v-else class="flex justify-center items-center h-full text-gray-600 bg-white rounded-lg shadow">
        Workflow not found.
      </div>
    </div>
  </div>
</template> 