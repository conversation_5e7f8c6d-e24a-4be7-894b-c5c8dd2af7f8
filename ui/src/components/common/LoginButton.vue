<template>
  <div class="flex items-center gap-3">
    <div v-if="authStore.isLoading" class="flex items-center gap-2 text-gray-600 text-sm">
      <div class="w-4 h-4 border-2 border-gray-200 border-t-blue-500 rounded-full animate-spin"></div>
      <span>检查登录状态...</span>
    </div>
    
    <div v-else-if="authStore.isAuthenticated" class="flex items-center gap-2">
      <span class="text-sm font-medium text-gray-700">{{ authStore.displayName }}</span>
      <button @click="authStore.logout" class="px-4 py-2 text-sm font-medium text-gray-600 bg-transparent border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-700 transition-colors">
        退出
      </button>
    </div>
    
    <button v-else @click="authStore.login" class="px-4 py-2 text-sm font-medium text-white bg-blue-500 border border-blue-500 rounded-md hover:bg-blue-600 transition-colors">
      登录
    </button>
  </div>
</template>

<script setup>
import { useAuthStore } from '@/stores/auth.js'

const authStore = useAuthStore()
</script> 