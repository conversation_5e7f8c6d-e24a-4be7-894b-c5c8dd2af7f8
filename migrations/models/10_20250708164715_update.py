from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "parse_file_tasks" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "task_id" VARCHAR(256) NOT NULL UNIQUE,
    "file_key" VARCHAR(512) NOT NULL,
    "product_ids" JSONB NOT NULL,
    "knowledge_type" VARCHAR(50) NOT NULL,
    "markdown" TEXT NOT NULL,
    "status" VARCHAR(24) NOT NULL DEFAULT 'pending',
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_parse_file__task_id_d11361" ON "parse_file_tasks" ("task_id", "status", "created_at");
COMMENT ON COLUMN "parse_file_tasks"."id" IS '自增主键 ID';
COMMENT ON COLUMN "parse_file_tasks"."task_id" IS 'shopify-knowledge 的任务 ID, 同步任务状态时需要用';
COMMENT ON COLUMN "parse_file_tasks"."file_key" IS '待解析文件的 id 类信息，读取文件内容时，需要向 store-center 申请文件下载链接.';
COMMENT ON COLUMN "parse_file_tasks"."product_ids" IS '任务处理的产品 ID 列表, 用于关联到具体的产品知识点';
COMMENT ON COLUMN "parse_file_tasks"."knowledge_type" IS '知识类型, 如 PRODUCT, STORE 等';
COMMENT ON COLUMN "parse_file_tasks"."markdown" IS '通过 AI 从文件内容中提取出来的 Markdown 格式文本, 这部分文本将用于进一步知识挖掘';
COMMENT ON COLUMN "parse_file_tasks"."status" IS '任务状态';
COMMENT ON COLUMN "parse_file_tasks"."created_at" IS '任务创建时间';
COMMENT ON COLUMN "parse_file_tasks"."updated_at" IS '任务最后更新时间';
COMMENT ON COLUMN "parse_file_tasks"."site_id" IS '关联的 Shopify 站点';
COMMENT ON TABLE "parse_file_tasks" IS '文件解析任务记录.';
        DROP TABLE IF EXISTS "knowledge_tasks";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "parse_file_tasks";"""
