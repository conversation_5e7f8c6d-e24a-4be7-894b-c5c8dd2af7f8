from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
    CREATE TABLE IF NOT EXISTS "shopify_page" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "page_url" VARCHAR(1024) NOT NULL UNIQUE,
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
        CREATE TABLE IF NOT EXISTS "page_elements" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" VARCHAR(6) NOT NULL DEFAULT 'fresh',
    "element" VARCHAR(255) NOT NULL,
    "selectors" JSONB NOT NULL,
    "page_id" INT NOT NULL REFERENCES "shopify_page" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_page_elemen_page_id_377133" UNIQUE ("page_id", "element")
);
COMMENT ON COLUMN "page_elements"."id" IS '自增主键 ID';
COMMENT ON COLUMN "page_elements"."created_at" IS '创建时间';
COMMENT ON COLUMN "page_elements"."updated_at" IS '最后更新时间';
COMMENT ON COLUMN "page_elements"."status" IS '标注状态';
COMMENT ON COLUMN "page_elements"."element" IS '元素唯一标识，如：相关推荐/推荐商品[1]';
COMMENT ON COLUMN "page_elements"."selectors" IS '元素选择器列表, 用于定位页面元素';
COMMENT ON TABLE "page_elements" IS '页面元素定义';
        
CREATE INDEX IF NOT EXISTS "idx_shopify_pag_page_ur_6783ed" ON "shopify_page" ("page_url");
COMMENT ON COLUMN "shopify_page"."created_at" IS '创建时间';
COMMENT ON COLUMN "shopify_page"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "shopify_page"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "shopify_page"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "shopify_page"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "shopify_page"."page_url" IS 'shopify 页面完整 URL, 如 example.com/products/womens-heated-softshell-vest';
COMMENT ON TABLE "shopify_page" IS '和一个 shopify 站点相关，区别于 ShopifySite 这里维护的是页面渲染模板的元数据';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "page_elements";
        DROP TABLE IF EXISTS "shopify_page";"""
