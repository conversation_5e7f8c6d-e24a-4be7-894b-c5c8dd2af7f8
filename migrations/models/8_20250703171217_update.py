from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "collection_knowledge_points" (
    "gmt_create" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "gmt_modified" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "id" SERIAL NOT NULL PRIMARY KEY,
    "point_id" VARCHAR(36) NOT NULL UNIQUE,
    "topic" VARCHAR(255),
    "title" VARCHAR(255) NOT NULL,
    "content" TEXT NOT NULL,
    "collection_id" VARCHAR(255) NOT NULL,
    "source" VARCHAR(50) NOT NULL,
    "source_detail" TEXT,
    "label" VARCHAR(255),
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_collection__point_i_f5e2c6" ON "collection_knowledge_points" ("point_id", "collection_id", "site_id");
COMMENT ON COLUMN "collection_knowledge_points"."gmt_create" IS '创建时间';
COMMENT ON COLUMN "collection_knowledge_points"."gmt_modified" IS '最后更新时间';
COMMENT ON COLUMN "collection_knowledge_points"."id" IS '自增主键 ID';
COMMENT ON COLUMN "collection_knowledge_points"."point_id" IS '知识点业务 ID (UUID)';
COMMENT ON COLUMN "collection_knowledge_points"."topic" IS '知识点主题';
COMMENT ON COLUMN "collection_knowledge_points"."title" IS '知识点标题';
COMMENT ON COLUMN "collection_knowledge_points"."content" IS '知识点内容';
COMMENT ON COLUMN "collection_knowledge_points"."collection_id" IS 'Collection ID';
COMMENT ON COLUMN "collection_knowledge_points"."source" IS '来源类型';
COMMENT ON COLUMN "collection_knowledge_points"."source_detail" IS '来源详情';
COMMENT ON COLUMN "collection_knowledge_points"."label" IS '标签';
COMMENT ON TABLE "collection_knowledge_points" IS 'collection知识点表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "collection_knowledge_points";"""
