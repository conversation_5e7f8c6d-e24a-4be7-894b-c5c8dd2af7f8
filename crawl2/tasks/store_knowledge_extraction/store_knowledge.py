import json
from uuid import uuid4

from loguru import logger

from crawl2 import schema
from crawl2.clients import llm
from crawl2.clients import shopify_knowledge
from crawl2.clients.qdrant_importer import qdrant_importer_instance
from crawl2.db import operations, models
from crawl2.tasks.celery_wrap import celery_task
from .labels import <PERSON><PERSON><PERSON>
from .prompts import SHOP_KNOWLEDGE_PROMPT_TEMPLATE, CHECK_QA_QUALITY_PROMPT, TAGGING_QAPAIR_LABEL_PROMPT, \
    TAGGING_QAPAIR_DETAILED_LABEL_PROMPT, ANSWER_GENERATE_QUESTION_PROMPT
from .utils import (convert_label_to_markdown, convert_store_knowledge_to_store_knowledge_point,
                    validate_label_value, validate_detailed_label_value)
from ..task_tqdm import CeleryTaskTqdmAsync

llm_conf = llm.LLMConfig('usq', 'qwen2.5-32b-chat-fp8-offline', 0, 1024)


def make_quality_int(quality):
    if quality == '合适':
        return 1
    if quality == '不合适':
        return 0
    else:
        return 1


async def extract_store_knowledge_from_content(url: str, raw_content: str, source: str) -> (
        list[schema.StoreKnowledgePoint] | None):
    prompt = SHOP_KNOWLEDGE_PROMPT_TEMPLATE.format(url, raw_content)
    extra_body = {
        "guided_json": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "topic": {"type": "string"},
                    "question": {"type": "string"},
                    "answer": {"type": "string"}
                },
                "required": ["topic", "question", "answer"],
                "additionalProperties": False
            }
        }
    }
    extra_body_extra_question = {
        "guided_json": {
            "type": "object",
            "properties": {
                "question_list": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            },
            "required": ["question_list"],
            "additionalProperties": False
        }
    }
    result = await llm.call_llm(llm_conf, prompt, parse_json=True, extra_body=extra_body)
    if result is None:
        return None
    logger.info(f"crawled store knowledge for {url}: {result}")
    knowledge_points = []
    for item in result:
        if type(item) is not dict:
            logger.info(f"extract_store_knowledge_from_content不是dict: {item}")
            continue
        if item.get("question", "") == "" or item.get("answer", "") == "":
            continue
        point_id = str(uuid4())
        question = item.get("question")
        answer = item.get("answer")
        logger.info(f"{point_id} question: {question}, answer: {answer}")
        qa_str = json.dumps({"question": question, "answer": answer}, ensure_ascii=False)
        # 1. 质量判断
        if source == "uploaded_file":
            # 客户上传的文件，质量默认是1
            quality_result = 1
        else:
            quality_prompt = CHECK_QA_QUALITY_PROMPT.format(qa_str)
            quality_result = await llm.call_llm(llm_conf, quality_prompt, parse_json=False)
            quality_result = make_quality_int(quality_result)
        logger.info(f"{point_id} quality_result: {quality_result}")
        # 2. label打标
        label_list_str, detailed_label_dict = convert_label_to_markdown(LABELS)
        label_prompt = TAGGING_QAPAIR_LABEL_PROMPT.format(label_list_str, qa_str)
        label = await llm.call_llm(llm_conf, label_prompt, parse_json=False)
        label = str(label).split('\n')[0].split(' ')[0]
        # 校验 label
        label = validate_label_value(label, LABELS)
        logger.info(f"{point_id} label: {label}")
        # 3. detailed_label打标
        detailed_label = None
        if label in detailed_label_dict:
            detailed_label_prompt = TAGGING_QAPAIR_DETAILED_LABEL_PROMPT.format(detailed_label_dict.get(label), qa_str)
            detailed_label = await llm.call_llm(llm_conf, detailed_label_prompt, parse_json=False)
            print(detailed_label)
            detailed_label = str(detailed_label).split('\n')[0].split(' ')[0]
        # 校验 detailed_label
        detailed_label = validate_detailed_label_value(label, detailed_label, detailed_label_dict)
        logger.info(f"{point_id} detailed_label: {detailed_label}")
        # 4. 生成extra_questions
        extra_prompt = ANSWER_GENERATE_QUESTION_PROMPT.format(qa_str)
        extra_result = await llm.call_llm(llm_conf, extra_prompt, parse_json=True, extra_body=extra_body_extra_question)
        extra_questions = None
        if extra_result and isinstance(extra_result, dict):
            extra_questions = extra_result.get('question_list', None)
        logger.info(f"{point_id} extra_questions: {extra_questions}")
        knowledge_points.append(schema.StoreKnowledgePoint(
            point_id=point_id,
            topic=item.get("topic", ""),
            question=question,
            answer=answer,
            source=source,
            source_detail=url,
            quality=quality_result,
            label=label,
            detailed_label=detailed_label,
            extra_questions=extra_questions,
            is_deleted=0
        ))
    return knowledge_points


async def _process_single_metadata(domain, url, raw_content, source: str, clear_existing: bool = False):
    existed = await operations.get_store_knowledge_point(source, url)
    if existed and not clear_existing:
        logger.info(f"Store knowledge point already exists for {url} and source {source}, skipping extraction.")
        return
    elif existed and clear_existing:
        logger.info(f"Clearing existing store knowledge point for {url} and source {source}.")
        await operations.delete_store_knowledge_points_by_source(domain, source, url)

    knowledge_points = await extract_store_knowledge_from_content(url, raw_content, source)
    if knowledge_points:
        await operations.save_store_knowledge_points(domain, knowledge_points)
    return knowledge_points


@celery_task
async def extract_all_store_knowledge(domain: str, clear_existing: bool = False):
    """
    抽取指定站点的所有知识点（页面+博客）
    """
    logger.info(f"开始extract_all_store_knowledge任务: {domain}")
    pages = await operations.list_pages_raw_pages(domain)
    blogs = await operations.list_blogs_raw_pages(domain)
    tasks = []
    for page in pages:
        tasks.append(_process_single_metadata(domain, page.url, page.raw_content, "pages", clear_existing))
    for blog in blogs:
        tasks.append(_process_single_metadata(domain, blog.url, blog.raw_content, "blogs", clear_existing))
    await CeleryTaskTqdmAsync.gather(*tasks, desc="Extracting store knowledge")


async def _process_single_metadata_all_pipeline(domain: str, url: str, raw_content: str, source: str):
    await _process_single_metadata(domain, url, raw_content, source)
    await sync_store_knowledge_to_knowledge_base(domain)
    await sync_all_store_knowledge_to_qdrant(domain)


async def _process_single_knowledge_extracting_task(domain: str, task: schema.ExtractFileKnowledgeTaskRequest):
    if task.knowledge_type != 'STORE':
        logger.error(f"Task {task.task_id} is not a STORE knowledge extraction task.")
        return
    if not task.markdown:
        logger.error(f"Task {task.task_id} has no markdown content to process.")
        return
    try:
        await _process_single_metadata_all_pipeline(domain, task.file_key, task.markdown, 'uploaded_file')
        await operations.update_parse_file_task_status(task.task_id, models.FileProcessStatus.KNOWLEDGE_EXTRACTED,
                                                       None)
    except Exception as e:
        logger.opt(exception=e).error(f"Failed to process task {task.task_id} for domain {domain}: {str(e)}")
        await operations.update_parse_file_task_status(
            task.task_id, models.FileProcessStatus.KNOWLEDGE_EXTRACT_FAILED, None
        )


@celery_task
async def extract_store_knowledge_from_uploaded_files(domain: str):
    """
    抽取指定站点所有客户上传文件中的店铺知识
    """
    tasks = await operations.list_tasks_for_knowledge_extracting(domain, 'STORE')
    async_tasks = [_process_single_knowledge_extracting_task(domain, task) for task in tasks]
    await CeleryTaskTqdmAsync.gather(*async_tasks, desc="Extracting store knowledge from uploaded files")


@celery_task
async def extract_store_knowledge_from_uploaded_file(task_id: str):
    """
    从指定的上传文件任务中抽取店铺知识点。
    :param task_id:  来自 shopify-knowledge 的 task_id
    """
    with logger.contextualize(knowledge_task_id=task_id):
        task = await operations.get_extract_file_knowledge_task(task_id)
        if not task:
            logger.error(f"Task with ID {task_id} not found.")
            return
        site = await operations.get_site_by_myshopify_main(task.store_domain)
        if not site:
            logger.error(f"No site found for {task.store_domain}")
            return
        await _process_single_knowledge_extracting_task(site.domain, task)


async def parse_store_knowledge(
    knowledge: models.StoreKnowledgePoint,
) -> shopify_knowledge.StoreKnowledgeItem:
    _, new_knowledge = await convert_store_knowledge_to_store_knowledge_point(knowledge)
    return shopify_knowledge.StoreKnowledgeItem(
        pointId=new_knowledge.point_id,
        topic=new_knowledge.topic,
        question=new_knowledge.question,
        answer=new_knowledge.answer,
        source=new_knowledge.source,
        sourceDetail=new_knowledge.source_detail,
        quality=new_knowledge.quality,
        label=new_knowledge.label,
        detailedLabel=new_knowledge.detailed_label if new_knowledge.detailed_label else "",
        extraQuestions=knowledge.extra_questions if knowledge.extra_questions else []
    )


@celery_task
async def sync_store_knowledge_to_knowledge_base(
    domain: str,
    batch_size: int = 500,
    delete_previous_shop_domain_data: bool = True,
):
    """
    同步指定站点的店铺知识到知识库
    """
    knowledge_client = shopify_knowledge.ShopifyKnowledgeClient.get_client()
    site = await operations.get_site(domain)
    if not site:
        logger.error(f"No site found for {domain}")
        return
    store_domain = site.shopify_domain
    if delete_previous_shop_domain_data:
        result = await knowledge_client.delete_all_store_knowledge(store_domain)
        logger.info(f"Deleted store knowledge for {store_domain} succeed: {result}")
    store_knowledge_list = await operations.list_store_knowledge(domain)
    store_knowledge_list = [
        await parse_store_knowledge(knowledge) for knowledge in store_knowledge_list
    ]
    logger.info(f"Found {len(store_knowledge_list)} store knowledge for {domain}")
    for i in range(0, len(store_knowledge_list), batch_size):
        batch = store_knowledge_list[i: i + batch_size]
        req = shopify_knowledge.ImportStoreKnowledgeReq(
            storeDomain=store_domain, items=batch
        )
        result = await knowledge_client.import_store_knowledge(req)
        logger.info(
            f"Synced store knowledge to knowledge base batch {i} for {store_domain}, result: {result}"
        )
    logger.info(
        f"Synced all store knowledge to knowledge base for {store_domain}, total: {len(store_knowledge_list)}"
    )


@celery_task
async def sync_all_store_knowledge_to_qdrant(domain: str):
    """
    同步站点所有店铺知识点到 Qdrant
    """
    await qdrant_importer_instance.import_all_store_knowledge_points(domain)


@celery_task
async def extract_single_page_store_knowledge(domain: str, url: str, source: str):
    """
    抽取单个页面的店铺知识点
    :param domain: 站点域名
    :param url: 页面 URL
    :param source: 来源类型 (如 'pages', 'blogs')
    """
    logger.info(f"开始抽取单个页面知识点: domain={domain}, url={url}, source={source}")
    raw_content = None
    # 根据 source 类型获取页面内容
    if source == "pages":
        page_metadata = await operations.get_page_metadata(url)
        if page_metadata:
            raw_content = page_metadata.raw_content
    elif source == "blogs":
        blog_metadata = await operations.get_blog_metadata(url)
        if blog_metadata:
            raw_content = blog_metadata.raw_content
    else:
        logger.error(f"不支持的 source 类型: {source}")
        return
    if not raw_content:
        logger.error(f"未找到页面内容: url={url}, source={source}")
        return
    # 处理知识点抽取
    knowledge_points = await _process_single_metadata(domain, url, raw_content, source)
    logger.info(f"完成单个页面知识点抽取: domain={domain}, url={url}, "
                f"抽取到 {len(knowledge_points) if knowledge_points else 0} 个知识点")
    return knowledge_points
