import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '../api/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const isAuthenticated = ref(false)
  const isLoading = ref(false)

  // 计算属性
  const displayName = computed(() => user.value?.full_name || user.value?.username || '用户')

  // 动作
  const login = async () => {
    // 重定向到后端登录接口
    let baseUrl = import.meta.env.VITE_API_BASE_URL
    if (baseUrl.endsWith('/')) {
      baseUrl = baseUrl.slice(0, -1)
    }
    // set redirect_uri to '${cur_domain}/api/v1/auth/callback'
    window.location.href = baseUrl + '/api/v1/auth/login' + `?redirect_uri=${window.location.origin}/api/v1/auth/callback`
  }

  const logout = async () => {
    try {
      await api.post('/api/v1/auth/logout')
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除本地状态
      user.value = null
      isAuthenticated.value = false
      // 重定向到登录页
      window.location.href = '/'
    }
  }

  const checkAuth = async () => {
    try {
      isLoading.value = true
      const response = await api.get('/api/v1/auth/me')
      user.value = response.data
      isAuthenticated.value = true
      return true
    } catch (error) {
      console.error('Auth check failed:', error)
      user.value = null
      isAuthenticated.value = false
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 初始化时检查认证状态
  const init = async () => {
    await checkAuth()
  }

  return {
    // 状态
    user,
    isAuthenticated,
    isLoading,
    
    // 计算属性
    displayName,
    
    // 动作
    login,
    logout,
    checkAuth,
    init
  }
}) 