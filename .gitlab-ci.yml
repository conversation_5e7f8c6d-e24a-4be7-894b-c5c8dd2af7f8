检查migrate是否正确:
  image: shop-pupil
  tags:
    - oversea-crawler  # 在办公室网络的 ************ 服务器上执行测试
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always
    - when: never
  script:
    - pip install -r dev-requirements.txt
    - python scripts/check_db_migrations.py

test:
  image: shop-pupil
  tags:
    - oversea-crawler  # 在办公室网络的 ************ 服务器上执行测试
  needs: ['检查migrate是否正确']
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: on_success
    - when: never
  script:
    - pip install -r dev-requirements.txt
    - pytest -v --disable-warnings --tb=short --cov-report term --cov-report=xml:coverage.xml --junitxml report.xml --cov=crawl2 tests/
  artifacts:
    when: always
    reports:
      junit: report.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - db.sqlite3

build-docker-image:
  stage: build
  image: m.daocloud.io/docker.io/docker
  needs: []
  tags:
    - oversea-crawler
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: on_success
    - when: never
  script:
    - docker build -f Dockerfile -t shop-pupil .


.deploy:
  stage: deploy
  image: m.daocloud.io/docker.io/docker
  tags:
    - oversea-crawler
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: on_success
    - when: never


stg-db-migrate:
  extends: .deploy
  image: shop-pupil
  needs:
    - job: build-docker-image
  script:
    - aerich upgrade

deploy-stg:
  extends: .deploy
  needs:
    - job: stg-db-migrate
  script:
    - docker compose up -d


更新office环境代码但不做DDL:
  extends: .deploy
  needs: []
  script:
    - docker build -f Dockerfile -t shop-pupil .
    - docker compose up -d
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: manual
    - when: never


stq-db-migrate:
  extends: .deploy
  image: shop-pupil
  variables:
    DATABASE_URL: $STQ_DATABASE_URL
  needs:
    - job: stg-db-migrate
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: manual
    - when: never
  script:
    - aerich upgrade


prq-db-migrate:
  extends: .deploy
  image: shop-pupil
  variables:
    DATABASE_URL: $PRQ_DATABASE_URL
  needs:
    - job: stq-db-migrate
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: manual
    - when: never
  script:
    - aerich upgrade

include:
  - template: arc-lint.gitlab-ci.yml