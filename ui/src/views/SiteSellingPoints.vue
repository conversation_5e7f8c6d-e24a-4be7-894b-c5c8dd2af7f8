<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { api } from '@/api/api.js'
import { AgGridVue } from 'ag-grid-vue3'
import { formatDateTime } from '@/lib/utils.js'

import Navigation from '../components/common/Navigation.vue'
import TruncatedCellRenderer from '../components/common/TruncatedCellRenderer.vue'
import SellingPointEditor from '../components/product/SellingPointEditor.vue'
import CrawlTaskTrigger from '../components/task/CrawlTaskTrigger.vue'
import BatchStatusDialog from '../components/product/BatchStatusDialog.vue'
import BilingualCellRenderer from '../components/common/BilingualCellRenderer.vue'
import { Download, CheckSquare, Square } from 'lucide-vue-next'


const route = useRoute()
const sellingPoints = ref([])
const loading = ref(true)
const error = ref(null)

// 分页相关状态
const currentPage = ref(1)
const pageSize = ref(2000)
const totalItems = ref(0)
const totalPages = ref(0)

// 批量选择相关状态
const selectedRows = ref(new Set())
const isSelectAll = ref(false)
const showBatchStatusDialog = ref(false)
const showSuccessMessage = ref(false)
const successMessage = ref('')

// 使用 Vue 组件作为单元格渲染器
const bilingualCellRenderer = BilingualCellRenderer

const statusSortOrder = {
  'fresh': 1,
  'editing': 2,
  'ready': 3,
  'disabled': 4,
  'archived': 5
}

const customStatusSort = (valueA, valueB) => {
  const orderA = statusSortOrder[valueA] || 999;
  const orderB = statusSortOrder[valueB] || 999;
  return orderA - orderB;
}

const columnDefs = [
  {
    field: 'id',
    headerName: 'ID',
    sortable: true,
    filter: true,
    width: 20,
    cellStyle: { fontWeight: 'bold' }
  },
  {
    field: 'product_title', 
    headerName: 'Product Title',
    sortable: true,
    filter: true,
    cellRenderer: TruncatedCellRenderer,
    width: 200,
    flex: 0
  },
  {
    field: 'status',
    headerName: 'Status',
    sortable: true,
    filter: true,
    width: 50,
    flex: 0,
    sort: 'asc',
    sortIndex: 0,
    comparator: customStatusSort,
    filterParams: {
      filterOptions: ['equals', 'notEqual', 'inRange'],
      buttons: ['apply', 'reset'],
      closeOnApply: true
    },
    cellRenderer: (params) => {
      const status = params.value;
      let color, bgColor;
      
      switch (status) {
        case 'fresh':
        case 'editing':
          color = '#0d9488';
          bgColor = '#ccfbf1';
          break;
        case 'ready':
          color = '#16a34a';
          bgColor = '#dcfce7';
          break;
        case 'disabled':
          color = '#6b7280';
          bgColor = '#f3f4f6';
          break;
        case 'archived':
          color = '#9ca3af';
          bgColor = '#f9fafb';
          break;
        default:
          color = '#6b7280';
          bgColor = '#f3f4f6';
      }
      
      return `<span style="display: inline-block; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; color: ${color}; background-color: ${bgColor}; text-transform: capitalize;">${status}</span>`;
    }
  },
  {
    field: 'created_at',
    headerName: 'Created At',
    sortable: true,
    filter: true,
    width: 120,
    flex: 0,
    valueFormatter: (params) => {
      return formatDateTime(params.value);
    },
    filterParams: {
      filterOptions: ['equals', 'notEqual', 'inRange', 'lessThan', 'greaterThan'],
      buttons: ['apply', 'reset'],
      closeOnApply: true
    }
  },
  {
    field: 'last_edited_at',
    headerName: 'Last Edited',
    sortable: true,
    filter: true,
    width: 120,
    flex: 0,
    valueFormatter: (params) => {
      if (!params.value) return 'Never';
      return formatDateTime(params.value);
    },
    filterParams: {
      filterOptions: ['equals', 'notEqual', 'inRange', 'lessThan', 'greaterThan'],
      buttons: ['apply', 'reset'],
      closeOnApply: true
    }
  },
  {
    field: 'selling_point_name',
    headerName: 'Selling Point',
    sortable: true,
    filter: true,
    flex: 1.5,
    cellRenderer: bilingualCellRenderer,
    cellRendererParams: {
      chineseField: 'selling_point_name_chn',
      englishField: 'selling_point_name',
      mode: 'single'
    },
    // 为过滤和排序提供值
    valueGetter: (params) => {
      const chinese = params.data.selling_point_name_chn || ''
      const english = params.data.selling_point_name || ''
      return `${chinese} ${english}`.trim()
    }
  },
  {
    field: 'selling_point_value',
    headerName: 'Description',
    sortable: true,
    filter: true,
    flex: 2.5,
    cellRenderer: bilingualCellRenderer,
    cellRendererParams: {
      chineseField: 'selling_point_value_chn',
      englishField: 'selling_point_value',
      mode: 'single'
    },
    // 为过滤和排序提供值
    valueGetter: (params) => {
      const chinese = params.data.selling_point_value_chn || ''
      const english = params.data.selling_point_value || ''
      return `${chinese} ${english}`.trim()
    }
  },
  {
    field: 'selling_point_marketing_copies',
    headerName: 'Marketing Copies',
    sortable: true,
    filter: true,
    flex: 2.5,
    cellRenderer: bilingualCellRenderer,
    cellRendererParams: {
      chineseField: 'selling_point_marketing_copies_chn',
      englishField: 'selling_point_marketing_copies',
      mode: 'multiple'
    },
    // 为过滤和排序提供值
    valueGetter: (params) => {
      const chineseArray = params.data.selling_point_marketing_copies_chn || []
      const englishArray = params.data.selling_point_marketing_copies || []
      return `${chineseArray.join(' ')} ${englishArray.join(' ')}`.trim()
    }
  }
]

const defaultColDef = {
  resizable: true,
  minWidth: 100,
  sortable: true,
  filter: true
}

// 为行添加悬停效果，提示可以双击编辑
const getRowClass = (params) => {
  let classes = []
  
  if (params.data && params.data.is_disabled) {
    classes.push('disabled-row')
  }
  
  // 添加悬停提示样式
  classes.push('cursor-pointer hover:bg-blue-50')
  
  return classes.join(' ')
}

// 批量选择相关函数
let gridApi = null

const onGridReady = (params) => {
  gridApi = params.api
}

const onSelectionChanged = () => {
  if (gridApi) {
    const selectedNodes = gridApi.getSelectedNodes()
    selectedRows.value = new Set(selectedNodes.map(node => node.data.id))
    
    // 检查是否全选
    const totalRows = gridApi.getDisplayedRowCount()
    isSelectAll.value = selectedNodes.length === totalRows && totalRows > 0
  }
}

const gridOptions = {
  domLayout: 'normal',
  columnDefs,
  defaultColDef,
  rowHeight: 80, // 基础行高
  headerHeight: 48,
  rowSelection: {
    mode: 'multiRow',
    selectAll: 'filtered'
  },
  getRowClass: getRowClass,
  onSelectionChanged: onSelectionChanged,
  onGridReady: onGridReady
}

const fetchSellingPointStats = async () => {
  try {
    const response = await api.get(`/api/v1/sites/${route.params.domain}/selling_points/edit_stats`)
    if (response.data) {
      statusCounts.value = response.data
    }
  } catch (err) {
    console.error('Error fetching selling point stats:', err)
  }
}

const fetchSellingPoints = async (page = 1) => {
  try {
    loading.value = true
    const [mainRes] = await Promise.all([
      api.get(`/api/v1/sites/${route.params.domain}/selling_points`, {
        params: { page: page, size: pageSize.value }
      }),
      fetchSellingPointStats()
    ])
    if (mainRes.data && 'items' in mainRes.data) {
      sellingPoints.value = mainRes.data.items
      totalItems.value = mainRes.data.total
      totalPages.value = mainRes.data.total_pages
      currentPage.value = mainRes.data.page
    } else {
      sellingPoints.value = mainRes.data
      totalItems.value = mainRes.data.length
      totalPages.value = 1
      currentPage.value = 1
    }
    
    // 清空选择状态
    if (gridApi) {
      gridApi.deselectAll()
    }
    selectedRows.value.clear()
    isSelectAll.value = false
  } catch (err) {
    error.value = err.message
    console.error('Error fetching selling points:', err)
  } finally {
    loading.value = false
  }
}

const downloadSellingPoints = async () => {
  try {
    const response = await api.get(`/api/v1/sites/${route.params.domain}/selling_points.excel`, {
      responseType: 'blob',
      timeout: 30000
    })
    
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `${route.params.domain}_selling_points.xlsx`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Error downloading selling points:', error)
  }
}

const handlePageChange = (newPage) => {
  if (newPage >= 1 && newPage <= totalPages.value) {
    fetchSellingPoints(newPage)
  }
}

const handlePageSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchSellingPoints(1)
}

const getVisiblePages = () => {
  const pages = []
  const maxVisible = 5
  let start = Math.max(1, currentPage.value - Math.floor(maxVisible / 2))
  let end = Math.min(totalPages.value, start + maxVisible - 1)
  
  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
}

const isPopoverVisible = ref(false)
const selectedRowData = ref(null)

const onCellClicked = (event) => {
  if (event.colDef.field !== undefined) {
    openEditPopover(event.data)
  }
}

const openEditPopover = (rowData) => {
  selectedRowData.value = rowData
  isPopoverVisible.value = true
}

const closeEditPopover = () => {
  isPopoverVisible.value = false
  selectedRowData.value = null
}

const saveAndNextSellingPoint = async (updatedData) => {
  try {
    const payload = {
      marketing_copies: updatedData.selling_point_marketing_copies,
      marketing_copies_chn: updatedData.selling_point_marketing_copies_chn,
      status: updatedData.status
    }
    const sellingPointId = parseInt(updatedData.id, 10)
    await api.put(`/api/v1/sites/${route.params.domain}/selling_points/${sellingPointId}`, payload)

    const index = sellingPoints.value.findIndex(item => item.id === updatedData.id);
    if (index !== -1) {
      sellingPoints.value[index].selling_point_marketing_copies = updatedData.selling_point_marketing_copies
      sellingPoints.value[index].selling_point_marketing_copies_chn = updatedData.selling_point_marketing_copies_chn
      sellingPoints.value[index].status = updatedData.status
    }
    await fetchSellingPointStats();

    // 检查当前页面是否还有 fresh 状态的卖点
    console.log('当前页面状态分布:', sellingPoints.value.map(sp => sp.status));
    console.log('是否有 fresh 状态:', hasStatusInCurrentPage('fresh'));
    
    if (!hasStatusInCurrentPage('fresh')) {
      // 如果当前页面没有 fresh 状态，跳转到第一页并重新加载数据
      console.log('当前页面没有 fresh 状态的卖点，跳转到第一页重新加载数据');
      
      // 临时关闭弹窗，避免用户困惑
      closeEditPopover();
      
      // 显示加载状态
      loading.value = true;
      
      currentPage.value = 1;
      await fetchSellingPoints(1);
      
      // 重新加载后，如果第一页有 fresh 状态的卖点，自动选中第一个
      const firstFreshIndex = getFirstStatusIndex('fresh');
      if (firstFreshIndex !== -1) {
        // 延迟一下再打开弹窗，让用户看到页面跳转
        setTimeout(() => {
          selectedRowData.value = sellingPoints.value[firstFreshIndex];
          isPopoverVisible.value = true;
        }, 500);
      }
    } else {
      // 如果当前页面还有 fresh 状态，继续编辑下一个
      const nextIndex = index + 1;
      if (nextIndex < sellingPoints.value.length) {
        selectedRowData.value = sellingPoints.value[nextIndex];
      } else {
        closeEditPopover();
      }
    }
  } catch (error) {
    console.error('Failed to save selling point:', error);
    alert('Failed to save selling point. Please try again.');
    return;
  }
}

const statusCounts = ref({
  fresh: 0,
  editing: 0,
  ready: 0,
  disabled: 0,
  archived: 0
})

// 辅助函数：检查当前页面是否有指定状态的卖点
const hasStatusInCurrentPage = (status) => {
  return sellingPoints.value.some(sp => sp.status === status);
}

// 辅助函数：获取当前页面第一个指定状态的卖点索引
const getFirstStatusIndex = (status) => {
  return sellingPoints.value.findIndex(sp => sp.status === status);
}

const handleBatchStatusUpdate = () => {
  if (selectedRows.value.size > 0) {
    showBatchStatusDialog.value = true
  }
}

const handleBatchStatusConfirm = async (newStatus) => {
  try {
    const selectedIds = Array.from(selectedRows.value)
    
    // 限制批量操作数量，避免瞬时压力过大
    if (selectedIds.length > 50) {
      alert('一次最多只能批量更新50个卖点，请减少选择数量')
      return
    }
    
    // 显示进度提示
    const progressMessage = `正在更新 ${selectedIds.length} 个卖点状态...`
    console.log(progressMessage)
    
    // 逐个调用现有的更新API
    const updatePromises = selectedIds.map(async (id) => {
      try {
        const payload = {
          status: newStatus
        }
        await api.put(`/api/v1/sites/${route.params.domain}/selling_points/${id}`, payload)
        return { id, success: true }
      } catch (error) {
        console.error(`Failed to update selling point ${id}:`, error)
        return { id, success: false, error: error.message }
      }
    })
    
    // 等待所有更新完成
    const results = await Promise.all(updatePromises)
    
    // 统计成功和失败的数量
    const successCount = results.filter(r => r.success).length
    const failCount = results.filter(r => !r.success).length
    
    // 更新本地数据
    sellingPoints.value.forEach(sp => {
      if (selectedIds.includes(sp.id)) {
        const result = results.find(r => r.id === sp.id)
        if (result && result.success) {
          sp.status = newStatus
        }
      }
    })
    
    // 清空选择
    if (gridApi) {
      gridApi.deselectAll()
    }
    selectedRows.value.clear()
    isSelectAll.value = false
    
    // 刷新统计数据
    await fetchSellingPointStats()
    
    // 简化提示，只在控制台显示详细信息
    if (failCount > 0) {
      console.warn(`批量更新完成！成功：${successCount} 个，失败：${failCount} 个。`)
      // 只在有失败的情况下显示简单提示
      alert(`更新完成，${failCount} 个失败`)
    } else {
      console.log(`批量更新成功！共更新 ${successCount} 个卖点状态。`)
      // 显示优雅的成功提示
      successMessage.value = `成功更新 ${successCount} 个卖点状态`
      showSuccessMessage.value = true
      setTimeout(() => {
        showSuccessMessage.value = false
      }, 3000) // 3秒后自动消失
    }
    
    showBatchStatusDialog.value = false
  } catch (error) {
    console.error('Failed to update batch status:', error)
    alert('批量更新状态失败，请重试')
  }
}

const closeBatchStatusDialog = () => {
  showBatchStatusDialog.value = false
}

const clearSelection = () => {
  if (gridApi) {
    gridApi.deselectAll()
  }
  selectedRows.value.clear()
  isSelectAll.value = false
}

onMounted(() => {
  fetchSellingPoints()
})
</script>

<template>
  <div class="flex flex-col h-screen w-full pt-0">
    <Navigation 
      :breadcrumbs="[
        { text: 'Sites', path: '/' },
        { text: 'Site Details', path: `/sites/${route.params.domain}` },
        { text: 'Selling Points' }
      ]"
    />

    <div class="flex justify-between items-center p-4 px-8 w-full bg-white shadow sticky top-16 z-10">
      <h1 class="m-0 text-2xl">Selling Points</h1>
      <div class="flex items-center gap-2">
        <!-- 批量操作按钮 -->
        <div v-if="selectedRows.size > 0" class="flex items-center gap-2 mr-4">
          <button 
            class="flex items-center gap-2 p-2 px-4 bg-blue-500 text-white border-none rounded cursor-pointer text-sm transition-colors duration-200 hover:bg-blue-600" 
            @click="handleBatchStatusUpdate"
          >
            <CheckSquare size="16" />
            批量标注 ({{ selectedRows.size }})
          </button>
          <button 
            class="flex items-center gap-2 p-2 px-3 bg-gray-500 text-white border-none rounded cursor-pointer text-sm transition-colors duration-200 hover:bg-gray-600" 
            @click="clearSelection"
            title="取消所有选择"
          >
            <Square size="16" />
            取消选择
          </button>
        </div>
        
        <button class="flex items-center gap-2 p-2 px-4 bg-green-500 text-white border-none rounded cursor-pointer text-sm transition-colors duration-200 hover:bg-green-600" @click="downloadSellingPoints">
          <Download size="16" />
          下载Excel
        </button>
        <CrawlTaskTrigger 
          taskName="sync_product_selling_points_to_knowledge_base" 
          displayName="同步到线上" 
          :defaultParams="{ domain: route.params.domain }"
        />
      </div>
    </div>

    <div class="flex gap-8 items-center mx-8 mt-4 mb-0 text-base text-gray-800">
      <div>卖点总数：{{ totalItems }}</div>
      <div>
        <span v-for="(count, status) in statusCounts" :key="status" class="mr-4 capitalize">
          {{ status }}：{{ count }}
        </span>
      </div>
    </div>

    <div class="flex-1 p-4 px-8 flex flex-col w-full bg-gray-50">
      <!-- 成功提示消息 -->
      <div v-if="showSuccessMessage" class="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg transition-all duration-300">
        <div class="flex items-center gap-2 text-green-800">
          <span>✅</span>
          <span>{{ successMessage }}</span>
        </div>
      </div>
      
      <div v-if="loading" class="flex justify-center items-center h-full text-gray-600 bg-white rounded-lg shadow">
        Loading selling points...
      </div>
      <div v-else-if="error" class="flex justify-center items-center h-full text-red-500 bg-white rounded-lg shadow">
        Error: {{ error }}
      </div>
      <div v-else-if="sellingPoints.length" class="flex-1 w-full min-w-[1280px] bg-white rounded-lg shadow">
        <AgGridVue
          :rowData="sellingPoints"
          @cell-clicked="onCellClicked"
          v-bind="gridOptions"
          class="w-full h-full"
        />
      </div>
      <div v-else class="flex justify-center items-center h-full text-gray-600 bg-white rounded-lg shadow">
        No selling points found for this site.
      </div>
    </div>

    <div v-if="!loading && !error && totalPages > 1" class="flex justify-between items-center p-4 px-8 bg-white border-t">
      <div class="flex items-center gap-4">
        <span class="text-sm text-gray-600">
          显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalItems) }} 条，共 {{ totalItems }} 条
        </span>
        <div class="flex items-center gap-2">
          <label class="text-sm text-gray-600">每页显示：</label>
          <select 
            v-model="pageSize" 
            @change="handlePageSizeChange(pageSize)"
            class="border border-gray-300 rounded px-2 py-1 text-sm"
          >
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="200">200</option>
            <option value="2000">2000</option>
          </select>
        </div>
      </div>
      
      <div class="flex items-center gap-2">
        <button 
          @click="handlePageChange(1)"
          :disabled="currentPage === 1"
          class="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          首页
        </button>
        <button 
          @click="handlePageChange(currentPage - 1)"
          :disabled="currentPage === 1"
          class="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          上一页
        </button>
        
        <div class="flex items-center gap-1">
          <button 
            v-for="page in getVisiblePages()" 
            :key="page"
            @click="handlePageChange(page)"
            :class="[
              'px-3 py-1 text-sm border rounded',
              page === currentPage 
                ? 'bg-blue-500 text-white border-blue-500' 
                : 'border-gray-300 hover:bg-gray-50'
            ]"
          >
            {{ page }}
          </button>
        </div>
        
        <button 
          @click="handlePageChange(currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          下一页
        </button>
        <button 
          @click="handlePageChange(totalPages)"
          :disabled="currentPage === totalPages"
          class="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          末页
        </button>
      </div>
    </div>

    <SellingPointEditor
      v-if="isPopoverVisible"
      :row-data="selectedRowData"
      @close="closeEditPopover"
      @saveAndNext="saveAndNextSellingPoint"
    />

    <!-- 批量标注对话框 -->
    <BatchStatusDialog
      :show="showBatchStatusDialog"
      :selected-count="selectedRows.size"
      @close="closeBatchStatusDialog"
      @confirm="handleBatchStatusConfirm"
    />
  </div>
</template>

<style scoped>
/* 确保行高能够自适应内容 */
:deep(.ag-row) {
  height: auto !important;
  min-height: 80px !important;
}

/* 确保单元格内容能够正确显示 */
:deep(.ag-cell-wrapper) {
  height: 100% !important;
  min-height: 100% !important;
}

/* 优化中英文单元格的显示 */
:deep(.bilingual-cell-renderer) {
  height: 100% !important;
  min-height: 100% !important;
}

</style>