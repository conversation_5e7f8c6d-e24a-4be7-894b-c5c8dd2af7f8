from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "store_knowledge_points" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "point_id" VARCHAR(36) NOT NULL UNIQUE,
    "topic" VARCHAR(255),
    "question" TEXT NOT NULL,
    "answer" TEXT NOT NULL,
    "store_domain" VARCHAR(255) NOT NULL,
    "source" VARCHAR(50) NOT NULL,
    "source_detail" TEXT,
    "quality" INT NOT NULL DEFAULT 1,
    "label" VARCHAR(255),
    "detailed_label" VARCHAR(255),
    "extra_questions" JSONB,
    "is_deleted" INT NOT NULL DEFAULT 0
);
CREATE INDEX IF NOT EXISTS "idx_store_knowl_point_i_cf73b1" ON "store_knowledge_points" ("point_id", "store_domain");
COMMENT ON COLUMN "store_knowledge_points"."id" IS '自增主键 ID';
COMMENT ON COLUMN "store_knowledge_points"."point_id" IS '知识点业务 ID (UUID)';
COMMENT ON COLUMN "store_knowledge_points"."topic" IS '知识点主题';
COMMENT ON COLUMN "store_knowledge_points"."question" IS '问题';
COMMENT ON COLUMN "store_knowledge_points"."answer" IS '答案';
COMMENT ON COLUMN "store_knowledge_points"."store_domain" IS '适用的店铺域名';
COMMENT ON COLUMN "store_knowledge_points"."source" IS '来源类型';
COMMENT ON COLUMN "store_knowledge_points"."source_detail" IS '来源详情';
COMMENT ON COLUMN "store_knowledge_points"."quality" IS '是否启用，1: 启用，0: 禁用';
COMMENT ON COLUMN "store_knowledge_points"."label" IS '标签';
COMMENT ON COLUMN "store_knowledge_points"."detailed_label" IS '详细标签';
COMMENT ON COLUMN "store_knowledge_points"."extra_questions" IS '问题的其他表达方式列表';
COMMENT ON COLUMN "store_knowledge_points"."is_deleted" IS '逻辑删除标志 (0: 未删除, 1: 已删除)';
COMMENT ON TABLE "store_knowledge_points" IS '店铺知识点表';
        CREATE TABLE IF NOT EXISTS "store_knowledge_value" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "store_domain" VARCHAR(255) NOT NULL,
    "label" VARCHAR(255) NOT NULL,
    "detailed_label" VARCHAR(255) NOT NULL,
    "value" TEXT NOT NULL,
    CONSTRAINT "uid_store_knowl_store_d_86d20c" UNIQUE ("store_domain", "label", "detailed_label")
);
CREATE INDEX IF NOT EXISTS "idx_store_knowl_store_d_86d20c" ON "store_knowledge_value" ("store_domain", "label", "detailed_label");
COMMENT ON COLUMN "store_knowledge_value"."id" IS '自增主键 ID';
COMMENT ON COLUMN "store_knowledge_value"."store_domain" IS '适用的店铺域名';
COMMENT ON COLUMN "store_knowledge_value"."label" IS '标签';
COMMENT ON COLUMN "store_knowledge_value"."detailed_label" IS '详细标签';
COMMENT ON COLUMN "store_knowledge_value"."value" IS '归纳值';
COMMENT ON TABLE "store_knowledge_value" IS '店铺知识归纳值表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "store_knowledge_value";
        DROP TABLE IF EXISTS "store_knowledge_points";"""
