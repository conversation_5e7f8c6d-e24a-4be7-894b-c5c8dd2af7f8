"""
认证相关的API路由
"""
from datetime import datetime, timedelta

import jwt
from authlib.integrations.httpx_client.oauth2_client import AsyncOAuth2<PERSON>lient
from fastapi import APIRouter, Depends, HTTPException, Request, Response, status
from fastapi.responses import RedirectResponse

from crawl2 import schema
from crawl2.config import settings
from crawl2.db import operations

hukou = AsyncOAuth2Client(
    client_id=settings.SSO_CLIENT_ID,
    client_secret=settings.SSO_CLIENT_SECRET,
    token_endpoint_auth_method=settings.SSO_TOKEN_ENDPOINT_AUTH_METHOD,
    scope=settings.SSO_SCOPE,
)

router = APIRouter(prefix="/auth", tags=["认证"])


async def get_current_user(request: Request) -> schema.ShopPupilUser:
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
    )

    token = request.cookies.get("access_token")
    if not token:
        raise credentials_exception

    try:
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        sub: str = payload.get("sub")
        if sub is None:
            raise credentials_exception
    except jwt.InvalidTokenError:
        raise credentials_exception

    try:
        user_id = int(sub)
    except ValueError:
        raise credentials_exception

    user = await operations.get_user_by_id(user_id)
    if user is None or not user.is_active:
        raise credentials_exception
    return user


@router.get("/login")
async def login(request: Request, redirect_uri: str = settings.SSO_REDIRECT_URI):
    """发起SSO登录"""
    auth_url, _ = hukou.create_authorization_url(settings.SSO_AUTHORIZATION_URL,
                                                 redirect_uri=redirect_uri)
    return RedirectResponse(url=auth_url)


@router.get("/callback")
async def auth_callback(request: Request, code: str, state: str):
    """SSO回调处理"""
    try:
        redirect_uri = settings.SSO_REDIRECT_URI
        if request.headers.get('host') == '127.0.0.1:8000': # 本地开发环境来的流量
            redirect_uri = f"http://127.0.0.1:8000/api/v1/auth/callback"
        token = await hukou.fetch_token(url=settings.SSO_TOKEN_URL, code=code, state=state,
                                        grant_type="authorization_code",
                                        redirect_uri=redirect_uri)
        user_info = jwt.decode(token["id_token"], settings.SSO_CLIENT_SECRET,
                               options={"verify_signature": True, "verify_aud": True, "verify_iat": False},
                               algorithms=["HS256"], audience=settings.SSO_CLIENT_ID)
        user = await operations.create_or_update_user_from_hukou_id_token(user_info)
        to_encode = {'sub': str(user.id)}
        expire = datetime.now() + timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
        # 设置cookie
        response = RedirectResponse(url="/")
        response.set_cookie(
            key="access_token",
            value=encoded_jwt,
            httponly=True,
            secure=settings.SSO_REDIRECT_URI.startswith("https"),
            samesite="lax",
            max_age=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        )
        return response
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Authentication failed: {str(e)}")


@router.post("/logout")
async def logout(response: Response):
    """登出"""
    response.delete_cookie("access_token")
    return {"message": "Successfully logged out"}


@router.get("/me", response_model=schema.ShopPupilUser)
async def get_current_user_info(current_user: schema.ShopPupilUser = Depends(get_current_user)):
    """获取当前用户信息"""
    return current_user
