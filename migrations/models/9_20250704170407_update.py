from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "knowledge_tasks" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "task_id" VARCHAR(256) NOT NULL UNIQUE,
    "params" JSONB NOT NULL,
    "status" VARCHAR(50) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS "idx_knowledge_t_task_id_f8b427" ON "knowledge_tasks" ("task_id", "status", "created_at");
COMMENT ON COLUMN "knowledge_tasks"."id" IS '自增主键 ID';
COMMENT ON COLUMN "knowledge_tasks"."task_id" IS 'shopify-knowledge 的任务 ID, 同步任务状态时需要用';
COMMENT ON COLUMN "knowledge_tasks"."params" IS '任务参数, 包含站点域名、任务类型等';
COMMENT ON COLUMN "knowledge_tasks"."status" IS '任务状态';
COMMENT ON COLUMN "knowledge_tasks"."created_at" IS '任务创建时间';
COMMENT ON TABLE "knowledge_tasks" IS '来自 shopify-knowledge 的数据挖掘任务请求记录.';
        ALTER TABLE "product_metadata" ADD "variant_info" JSONB DEFAULT '{}'::jsonb;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "product_metadata" DROP COLUMN "variant_info";
        DROP TABLE IF EXISTS "knowledge_tasks";"""
