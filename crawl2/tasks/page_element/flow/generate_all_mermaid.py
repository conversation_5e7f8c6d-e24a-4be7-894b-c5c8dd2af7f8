"""
遍历 crawl2.tasks.page_element.flow 目录下所有的 flow，
并为每个 flow 在其 package 内生成一个 mermaid.md 文件。
"""

import importlib
import inspect
import sys
from pathlib import Path
from typing import Dict, List, Callable, Any

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from loguru import logger
except ImportError:
    # 如果没有 loguru，使用标准库的 logging
    import logging

    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

try:
    from pocketflow import Flow
except ImportError:
    print("错误: 无法导入 pocketflow。请确保已安装所有依赖。")
    sys.exit(1)


def discover_flow_packages() -> List[Path]:
    """发现所有 flow 子目录"""
    flow_dir = Path(__file__).parent
    flow_packages = []

    for item in flow_dir.iterdir():
        if item.is_dir() and item.name != "__pycache__" and (item / "__init__.py").exists():
            flow_packages.append(item)

    logger.info(f"发现 {len(flow_packages)} 个 flow packages: {[p.name for p in flow_packages]}")
    return flow_packages


def discover_create_functions(package_path: Path) -> Dict[str, Callable]:
    """从指定的 package 中发现所有 create_*_flow 函数"""
    package_name = f"crawl2.tasks.page_element.flow.{package_path.name}"

    try:
        module = importlib.import_module(package_name)
        create_functions = {}

        for name, obj in inspect.getmembers(module):
            if (inspect.isfunction(obj) and
                    name.startswith("create_") and
                    name.endswith("_flow")):
                create_functions[name] = obj

        logger.info(f"在 {package_name} 中发现 {len(create_functions)} 个创建函数: {list(create_functions.keys())}")
        return create_functions

    except ImportError as e:
        logger.error(f"无法导入 {package_name}: {e}")
        return {}


def generate_mermaid_for_flow(flow: Flow, flow_name: str) -> str:
    """为指定的 flow 生成 mermaid 图表"""
    from crawl2.tasks.page_element.visualize import build_mermaid

    try:
        mermaid_content = build_mermaid(flow)
        return f"# {flow_name}\n\n```mermaid\n{mermaid_content}\n```\n"
    except Exception as e:
        logger.error(f"生成 {flow_name} 的 mermaid 图表时出错: {e}")
        return f"# {flow_name}\n\n生成图表时出错: {e}\n"


def generate_mermaid_for_package(package_path: Path) -> bool:
    """为指定的 package 生成 mermaid.md 文件"""
    create_functions = discover_create_functions(package_path)

    if not create_functions:
        logger.warning(f"在 {package_path.name} 中没有发现任何创建函数")
        return False

    mermaid_content_parts = []

    for func_name, func in create_functions.items():
        try:
            # 调用创建函数获取 flow 实例
            flow = func()

            if not isinstance(flow, Flow):
                logger.warning(f"{func_name} 返回的不是 Flow 实例: {type(flow)}")
                continue

            # 生成 mermaid 图表
            flow_display_name = func_name.replace("create_", "").replace("_flow", "").replace("_", " ").title()
            mermaid_part = generate_mermaid_for_flow(flow, flow_display_name)
            mermaid_content_parts.append(mermaid_part)

            logger.info(f"成功为 {func_name} 生成 mermaid 图表")

        except Exception as e:
            logger.error(f"处理 {func_name} 时出错: {e}")
            mermaid_content_parts.append(f"# {func_name}\n\n处理时出错: {e}\n")

    if mermaid_content_parts:
        # 写入 mermaid.md 文件
        mermaid_file = package_path / "mermaid.md"
        full_content = "\n\n".join(mermaid_content_parts)

        with open(mermaid_file, "w", encoding="utf-8") as f:
            f.write(full_content)

        logger.info(f"已生成 {mermaid_file}")
        return True

    return False


"""主函数：遍历所有 flow packages 并生成 mermaid.md"""
logger.info("开始生成所有 flow 的 mermaid 文档...")

flow_packages = discover_flow_packages()
success_count = 0

for package_path in flow_packages:
    logger.info(f"处理 package: {package_path.name}")

    if generate_mermaid_for_package(package_path):
        success_count += 1
    else:
        logger.warning(f"跳过 package: {package_path.name}")

logger.info(f"完成！成功处理了 {success_count}/{len(flow_packages)} 个 packages")
