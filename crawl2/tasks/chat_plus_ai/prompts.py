ORC_PROMPT = """
## Core Instruction
Extract substantive knowledge from text in e-commerce images. Do NOT describe images.
Strictly follow these rules:

## Extraction Rules
1. **Content Requirements**:
- Knowledge must originate ONLY from image text
- `content` must contain complete, coherent descriptive information
- Prohibit fragmented knowledge (e.g., isolated dimensions/numbers)
- Merge contextual information: Content under the same title/area must become a single entry
- Tables must be output as single knowledge entries using markdown
- Product selections/comparisons must be output as single knowledge entries using markdown
- Filter non-descriptive content (navigation menus, buttons, code, etc.)
- Only extract knowledge related to product attributes, such as product ingredients, product usage, etc. Product price does not belong to product attributes

2. **Absolute Prohibitions**:
- Modify, summarize, or paraphrase original text
- Output empty `content` or fragmented content
- Split contextual knowledge (e.g., same-title content)
- Output non-substantive text (e.g., guidance phrases, questions)
- Duplicate text across multiple entries
- Describe images

3. **Language Requirements**:
- Use original language text from images
- NO translation or language conversion
- Return empty list [] if no text exists

## Reference Topics
Variant Information | Brand Introduction | Product Title | Product Price
Product Description | Product Features | Feature Details | Technical Specifications
Usage Instructions | Product Benefits | Use Scenarios | Products Comparison Table
What is included | FAQs | Customer Reviews | Others

## Output Specification
```json
[{
"topic": "Most relevant topic from reference list",
"title": "Knowledge title (optional)",
"content": "Complete coherent descriptive information"
}]
"""

EXTRA_QUESTIONS_PROMPT = '''
## 角色与任务
你是一名经验丰富的电商专家，擅长**精准提炼**并**转化**给定的知识点，模拟真实买家可能提出的、与知识点**直接且具体相关**的问题。

## 生成规则
1.  **语言要求**：生成的买家提问**必须**使用 `{{region}}` 国家/地区对应的官方语言。
2.  **知识点绑定**：生成的每一个问题必须**严格基于且紧扣**提供的 `{knowledge_info}` 的核心要素。问题应直接询问知识点中明确包含或隐含的具体信息、属性、功能、限制、使用方法、效果等。
3.  **准确性与具体性**：确保问题表述清晰、准确，反映知识点的实际内容。**避免生成假设性、引申性或超出知识点范围的问题**。优先使用知识点中出现的具体术语或概念。
4.  **多样性要求**：在确保准确绑定知识点的基础上，通过变换提问角度、句式结构、用词（如同义词替换）来生成不同的问题。确保每个问题反映知识点的**不同方面或细节**。
5.  **数量要求**：生成 **5 个** 不同且符合上述要求的问题。
6.  **输出格式**：输出必须是 **纯 JSON 对象**，格式为：`{{"question_list": ["Question1", "Question2", "Question3", "Question4", "Question5"]}}`。**禁止**包含任何 Markdown 格式、解释性文字、注释或表情符号。

## 输入
*   `region`：目标买家所在国家/地区（用于确定语言）: {region}
*   `knowledge_info`：需要生成问题的原始知识点文本: {knowledge_info}

## 输出
严格按要求输出 JSON 对象。
'''
