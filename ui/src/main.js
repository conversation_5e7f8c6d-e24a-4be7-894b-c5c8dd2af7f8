import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import './style.css'
import { clickOutside } from './directives/click-outside'
import { ModuleRegistry, AllCommunityModule, provideGlobalGridOptions } from 'ag-grid-community'
import { themeAlpine } from 'ag-grid-community'

ModuleRegistry.registerModules([ AllCommunityModule ])
provideGlobalGridOptions({
  theme: themeAlpine,
})

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.directive('click-outside', clickOutside)

// 初始化认证状态
import { useAuthStore } from './stores/auth'
const authStore = useAuthStore()
authStore.init()

app.mount('#app')
