"""
复制qdrant collection数据到新的collection，使用qwen3重新生成向量
"""

import asyncio
import sys
from typing import List, Dict, Any
from loguru import logger
from qdrant_client import AsyncQdrantClient
from qdrant_client.models import (
    PointStruct,
    VectorParams,
    Distance,
    SparseVectorParams,
    MultiVectorConfig,
    MultiVectorComparator,
    SparseVector,
)
from crawl2.clients.embedding import HttpEmbeddingClient


class OptimizedQdrantCollectionCopier:
    """优化的qdrant collection复制器，支持复制到不同的qdrant实例"""

    def __init__(self,
                 source_collection: str,
                 target_collection: str,
                 source_qdrant_host: str,
                 source_qdrant_port: int,
                 target_qdrant_host: str,
                 target_qdrant_port: int,
                 qwen3_embed_url: str = "http://*************:8088/qwen3_4b_embed",
                 sparse_embed_url: str = None,
                 vector_size: int = 2560,
                 store_domains: List[str] = None):
        self.source_collection = source_collection
        self.target_collection = target_collection

        self.source_qdrant_host = source_qdrant_host
        self.source_qdrant_port = source_qdrant_port
        self.target_qdrant_host = target_qdrant_host
        self.target_qdrant_port = target_qdrant_port
        self.vector_size = vector_size
        self.qwen3_embed_url = qwen3_embed_url
        self.sparse_embed_url = sparse_embed_url
        self.store_domains = store_domains or []

        self.source_qdrant = AsyncQdrantClient(host=self.source_qdrant_host, port=self.source_qdrant_port)
        self.target_qdrant = AsyncQdrantClient(host=self.target_qdrant_host, port=self.target_qdrant_port)
        self.embedding_client = HttpEmbeddingClient(self.qwen3_embed_url)
        self.sparse_embedding_client = HttpEmbeddingClient(self.sparse_embed_url)

        self.collection_type = self._detect_collection_type()

        logger.info(
            f"初始化复制器: {self.source_qdrant_host}:{self.source_qdrant_port} -> "
            f"{self.target_qdrant_host}:{self.target_qdrant_port}")
        logger.info(f"Collection: {self.source_collection} -> {self.target_collection} ({self.collection_type})")
        logger.info(f"密集向量服务: {self.qwen3_embed_url}")
        logger.info(f"稀疏向量服务: {self.sparse_embed_url}")
        if self.store_domains:
            logger.info(f"指定store domains: {self.store_domains}")

    def _detect_collection_type(self) -> str:
        # 根据实际的collection名称判断类型
        # 实际使用的collection名称：
        # shopify_product_knowledge_0613
        # seller_knowledge_extra_questions_sparse_vectors
        # shopify_collection_knowledge

        if self.source_collection == "shopify_product_knowledge_0613":
            return "product_knowledge"
        elif self.source_collection == "seller_knowledge_extra_questions_sparse_vectors":
            return "store_knowledge"
        elif self.source_collection == "shopify_collection_knowledge":
            return "collection_knowledge"
        else:
            return "unknown"

    async def get_source_collection_info(self) -> Dict[str, Any]:
        try:
            collection_info = await self.source_qdrant.get_collection(self.source_collection)
            return collection_info
        except Exception as e:
            logger.error(f"获取源collection信息失败: {e}")
            raise

    async def create_target_collection(self, force_recreate: bool = False):
        try:
            collections = await self.target_qdrant.get_collections()
            target_exists = any(
                collection.name == self.target_collection
                for collection in collections.collections
            )

            if target_exists and not force_recreate:
                logger.info(f"目标collection {self.target_collection} 已存在，跳过创建")
                return

            if target_exists and force_recreate:
                await self.target_qdrant.delete_collection(self.target_collection)
                logger.info(f"删除已存在的目标collection: {self.target_collection}")

            # 根据collection类型创建向量配置
            vectors_config = self._create_vectors_config()
            sparse_vectors_config = self._create_sparse_vectors_config()

            await self.target_qdrant.create_collection(
                collection_name=self.target_collection,
                vectors_config=vectors_config,
                sparse_vectors_config=sparse_vectors_config,
            )

            # 创建payload索引
            payload_indexes = self._create_payload_indexes()
            for field, schema in payload_indexes.items():
                await self.target_qdrant.create_payload_index(
                    collection_name=self.target_collection,
                    field_name=field,
                    field_schema=schema,
                )

            logger.info(f"成功创建目标collection: {self.target_collection} (vector_size: {self.vector_size})")

        except Exception as e:
            logger.error(f"创建目标collection失败: {e}")
            raise

    def _create_vectors_config(self) -> Dict[str, Any]:
        """根据collection类型创建向量配置"""
        if self.collection_type == "product_knowledge":
            return {
                "document_dense": VectorParams(
                    size=self.vector_size, distance=Distance.COSINE, on_disk=True
                ),
                "product_title_dense": VectorParams(
                    size=self.vector_size, distance=Distance.COSINE, on_disk=True
                ),
            }
        elif self.collection_type == "store_knowledge":
            return {
                "question-dense": VectorParams(
                    size=self.vector_size, distance=Distance.COSINE, on_disk=True
                ),
                "answer-dense": VectorParams(
                    size=self.vector_size, distance=Distance.COSINE, on_disk=True
                ),
                "extra-questions-dense": VectorParams(
                    size=self.vector_size,
                    distance=Distance.COSINE,
                    multivector_config=MultiVectorConfig(
                        comparator=MultiVectorComparator.MAX_SIM
                    ),
                    on_disk=True,
                ),
            }
        elif self.collection_type == "collection_knowledge":
            return {
                "title-dense": VectorParams(
                    size=self.vector_size, distance=Distance.COSINE, on_disk=True
                ),
                "content-dense": VectorParams(
                    size=self.vector_size, distance=Distance.COSINE, on_disk=True
                ),
            }
        else:
            raise ValueError(f"不支持的collection类型: {self.collection_type}")

    def _create_sparse_vectors_config(self) -> Dict[str, Any]:
        """根据collection类型创建稀疏向量配置"""
        if self.collection_type == "product_knowledge":
            return {
                "document_sparse": SparseVectorParams(),
                "product_title_sparse": SparseVectorParams(),
            }
        elif self.collection_type == "store_knowledge":
            return {
                "question-sparse": SparseVectorParams(),
                "answer-sparse": SparseVectorParams(),
            }
        elif self.collection_type == "collection_knowledge":
            return {
                "title-sparse": SparseVectorParams(),
                "content-sparse": SparseVectorParams(),
            }
        else:
            return {}

    def _create_payload_indexes(self) -> Dict[str, str]:
        """根据collection类型创建payload索引配置"""
        if self.collection_type == "product_knowledge":
            return {
                "store_domain": "keyword",
                "product_id": "keyword",
                "product_type": "keyword",
                "source": "keyword",
                "source_detail": "keyword",
            }
        elif self.collection_type == "store_knowledge":
            return {
                "topic": "keyword",
                "store_domain": "keyword",
                "source": "keyword",
                "source_detail": "keyword",
                "label": "keyword",
                "detailed_label": "keyword",
                "quality": "keyword"
            }
        elif self.collection_type == "collection_knowledge":
            return {
                "store_domain": "keyword",
                "source": "keyword",
                "source_detail": "keyword",
            }
        else:
            return {}

    async def generate_vectors_for_product_knowledge(self, payloads: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """为product_knowledge生成向量，与原始indexer保持一致"""
        # 提取文档文本和产品标题
        document_texts = []
        product_titles = []

        for payload in payloads:
            title = payload.get('title', '')
            content = payload.get('content', '')
            product_title = payload.get('product_title', '')

            document_texts.append(f"{title} {content}".strip())
            product_titles.append(product_title)

        # 并行生成密集向量和稀疏向量
        (document_dense_embeddings, product_title_dense_embeddings,
         document_sparse_embeddings, product_title_sparse_embeddings) = await asyncio.gather(
            self.regenerate_vectors_with_qwen3(document_texts),
            self.regenerate_vectors_with_qwen3(product_titles),
            self.regenerate_sparse_vectors(document_texts),
            self.regenerate_sparse_vectors(product_titles),
        )

        # 构建向量字典
        vectors = []
        for doc_dense, title_dense, doc_sparse, title_sparse in zip(
            document_dense_embeddings, product_title_dense_embeddings,
            document_sparse_embeddings, product_title_sparse_embeddings
        ):
            vectors.append({
                "document_dense": doc_dense,
                "product_title_dense": title_dense,
                "document_sparse": doc_sparse,
                "product_title_sparse": title_sparse,
            })

        return vectors

    async def generate_vectors_for_store_knowledge(self, payloads: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """为store_knowledge生成向量，与原始indexer保持一致"""
        # 提取问题和答案
        questions = [payload.get('question', '') for payload in payloads]
        answers = [payload.get('answer', '') for payload in payloads]
        extra_questions_list = [payload.get('extra_questions', []) for payload in payloads]

        # 并行生成密集向量和稀疏向量
        (question_dense_embeddings, answer_dense_embeddings,
         question_sparse_embeddings, answer_sparse_embeddings) = await asyncio.gather(
            self.regenerate_vectors_with_qwen3(questions),
            self.regenerate_vectors_with_qwen3(answers),
            self.regenerate_sparse_vectors(questions),
            self.regenerate_sparse_vectors(answers),
        )

        # 处理额外问题向量
        extra_questions_embeddings = []
        for extra_questions in extra_questions_list:
            if extra_questions:
                extra_embeddings = await self.regenerate_vectors_with_qwen3(extra_questions)
                extra_questions_embeddings.append(extra_embeddings)
            else:
                extra_questions_embeddings.append([])

        # 构建向量字典
        vectors = []
        for q_dense, a_dense, q_sparse, a_sparse, extra_embs in zip(
            question_dense_embeddings, answer_dense_embeddings,
            question_sparse_embeddings, answer_sparse_embeddings, extra_questions_embeddings
        ):
            vector_dict = {
                "question-dense": q_dense,
                "answer-dense": a_dense,
                "question-sparse": q_sparse,
                "answer-sparse": a_sparse,
            }
            if extra_embs:
                vector_dict["extra-questions-dense"] = extra_embs
            vectors.append(vector_dict)

        return vectors

    async def generate_vectors_for_collection_knowledge(self, payloads: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """为collection_knowledge生成向量，与原始indexer保持一致"""
        # 提取标题和内容
        titles = [payload.get('title', '') for payload in payloads]
        contents = [payload.get('content', '') for payload in payloads]

        # 并行生成密集向量和稀疏向量
        (title_dense_embeddings, content_dense_embeddings,
         title_sparse_embeddings, content_sparse_embeddings) = await asyncio.gather(
            self.regenerate_vectors_with_qwen3(titles),
            self.regenerate_vectors_with_qwen3(contents),
            self.regenerate_sparse_vectors(titles),
            self.regenerate_sparse_vectors(contents),
        )

        # 构建向量字典
        vectors = []
        for title_dense, content_dense, title_sparse, content_sparse in zip(
            title_dense_embeddings, content_dense_embeddings,
            title_sparse_embeddings, content_sparse_embeddings
        ):
            vectors.append({
                "title-dense": title_dense,
                "content-dense": content_dense,
                "title-sparse": title_sparse,
                "content-sparse": content_sparse,
            })

        return vectors

    async def regenerate_vectors_with_qwen3(self, texts: List[str]) -> List[List[float]]:
        if not texts:
            return []

        try:
            embeddings = await self.embedding_client.embed_async(texts)
            return embeddings
        except Exception as e:
            logger.error(f"使用qwen3生成向量失败: {e}")
            raise

    async def regenerate_sparse_vectors(self, texts: List[str]) -> List[SparseVector]:
        """使用稀疏向量服务生成稀疏向量"""
        if not texts:
            return []

        try:
            response = await self.sparse_embedding_client.embed_async(texts)
            embeddings = response["embeddings"]
            sparse_vectors = [
                SparseVector(indices=embedding.keys(), values=embedding.values())
                for embedding in embeddings
            ]
            return sparse_vectors
        except Exception as e:
            logger.error(f"生成稀疏向量失败: {e}")
            raise

    async def copy_collection_data(self, batch_size: int = 100):
        try:
            logger.info(f"开始复制数据: {self.source_collection} -> {self.target_collection}")

            # 如果有指定store domains，使用filter查询
            if self.store_domains:
                from qdrant_client.models import Filter, FieldCondition, MatchAny

                # 构建store domain过滤条件
                filter_condition = Filter(
                    must=[
                        FieldCondition(
                            key="store_domain",
                            match=MatchAny(any=self.store_domains)
                        )
                    ]
                )

                # 获取符合条件的点数量（使用scroll来获取实际数量）
                total_points = 0
                temp_offset = None
                while True:
                    temp_scroll_params = {
                        "collection_name": self.source_collection,
                        "scroll_filter": filter_condition,
                        "limit": 1000,  # 使用较大的limit来快速计算总数
                        "with_payload": False,
                        "with_vectors": False
                    }
                    if temp_offset:
                        temp_scroll_params["offset"] = temp_offset

                    temp_result = await self.source_qdrant.scroll(**temp_scroll_params)
                    temp_points, temp_next_offset = temp_result

                    if not temp_points:
                        break

                    total_points += len(temp_points)

                    if len(temp_points) < 1000:
                        break

                    temp_offset = temp_next_offset

                logger.info(f"源collection中符合条件的点共有 {total_points} 个 (store_domains: {self.store_domains})")

                processed_count = 0
                offset = None

                while True:
                    scroll_params = {
                        "collection_name": self.source_collection,
                        "scroll_filter": filter_condition,
                        "limit": batch_size,
                        "with_payload": True,
                    }
                    if offset:
                        scroll_params["offset"] = offset

                    scroll_response = await self.source_qdrant.scroll(**scroll_params)
                    points, next_offset = scroll_response

                    if not points:
                        break

                    payloads = [point.payload for point in points]

                    # 根据collection类型生成向量
                    if self.collection_type == "product_knowledge":
                        vectors = await self.generate_vectors_for_product_knowledge(payloads)
                    elif self.collection_type == "store_knowledge":
                        vectors = await self.generate_vectors_for_store_knowledge(payloads)
                    elif self.collection_type == "collection_knowledge":
                        vectors = await self.generate_vectors_for_collection_knowledge(payloads)
                    else:
                        raise ValueError(
                            f"不支持的collection类型: {self.collection_type}，"
                            f"仅支持 product_knowledge、store_knowledge、collection_knowledge")

                    # 准备新的点数据
                    new_points = []
                    for i, point in enumerate(points):
                        if i < len(vectors):
                            new_points.append(
                                PointStruct(
                                    id=point.id,
                                    vector=vectors[i],
                                    payload=point.payload,
                                )
                            )

                    if new_points:
                        await self.target_qdrant.upsert(
                            collection_name=self.target_collection,
                            points=new_points,
                            wait=True,
                        )

                    processed_count += len(new_points)

                    # 检查是否还有更多数据
                    if len(points) < batch_size:
                        break

                    # 更新偏移量，继续查询下一页
                    offset = next_offset
                    logger.info(f"已处理 {processed_count}/{total_points} 个点")

            else:
                # 没有指定store domains，复制所有数据
                logger.info("没有指定store domains，复制所有数据")

                processed_count = 0
                offset = None

                while True:
                    scroll_params = {
                        "collection_name": self.source_collection,
                        "limit": batch_size,
                        "with_payload": True,
                    }
                    if offset:
                        scroll_params["offset"] = offset

                    scroll_response = await self.source_qdrant.scroll(**scroll_params)
                    points, next_offset = scroll_response

                    if not points:
                        break

                    payloads = [point.payload for point in points]

                    # 根据collection类型生成向量
                    if self.collection_type == "product_knowledge":
                        vectors = await self.generate_vectors_for_product_knowledge(payloads)
                    elif self.collection_type == "store_knowledge":
                        vectors = await self.generate_vectors_for_store_knowledge(payloads)
                    elif self.collection_type == "collection_knowledge":
                        vectors = await self.generate_vectors_for_collection_knowledge(payloads)
                    else:
                        raise ValueError(
                            f"不支持的collection类型: {self.collection_type}，"
                            f"仅支持 product_knowledge、store_knowledge、collection_knowledge")

                    # 准备新的点数据
                    new_points = []
                    for i, point in enumerate(points):
                        if i < len(vectors):
                            new_points.append(
                                PointStruct(
                                    id=point.id,
                                    vector=vectors[i],
                                    payload=point.payload,
                                )
                            )

                    if new_points:
                        await self.target_qdrant.upsert(
                            collection_name=self.target_collection,
                            points=new_points,
                            wait=True,
                        )

                    processed_count += len(new_points)

                    # 检查是否还有更多数据
                    if len(points) < batch_size:
                        break

                    # 更新偏移量，继续查询下一页
                    offset = next_offset

                    logger.info(f"已处理 {processed_count} 个点")

            logger.info(f"数据复制完成，共复制 {processed_count} 个点")

        except Exception as e:
            logger.error(f"复制collection数据失败: {e}")
            raise

    async def copy_collection(self, force_recreate: bool = False, batch_size: int = 100):
        try:
            await self.create_target_collection(force_recreate)
            await self.copy_collection_data(batch_size)

            source_count = await self.source_qdrant.count(self.source_collection)
            target_count = await self.target_qdrant.count(self.target_collection)

            logger.info(f"复制完成验证: 源{source_count.count}个点 -> 目标{target_count.count}个点")

            if source_count.count == target_count.count:
                logger.info("复制验证成功！")
            else:
                logger.warning(
                    f"复制验证失败！源collection有 {source_count.count} 个点，目标collection有 {target_count.count} 个点")

        except Exception as e:
            logger.error(f"复制collection失败: {e}")
            raise


async def copy_qdrant_collection_with_qwen3(
        source_collection: str,
        target_collection: str,
        source_qdrant_host: str,
        source_qdrant_port: int,
        target_qdrant_host: str,
        target_qdrant_port: int,
        force_recreate: bool = False,
        batch_size: int = 100,
        qwen3_embed_url: str = "http://*************:8088/qwen3_4b_embed",
        sparse_embed_url: str = None,
        vector_size: int = 2560,
        store_domains: List[str] = None
):
    """复制qdrant collection数据到新的collection，使用qwen3重新生成向量"""
    logger.info(f"开始复制collection: {source_collection} -> {target_collection}")

    copier = OptimizedQdrantCollectionCopier(
        source_collection=source_collection,
        target_collection=target_collection,
        source_qdrant_host=source_qdrant_host,
        source_qdrant_port=source_qdrant_port,
        target_qdrant_host=target_qdrant_host,
        target_qdrant_port=target_qdrant_port,
        qwen3_embed_url=qwen3_embed_url,
        sparse_embed_url=sparse_embed_url,
        vector_size=vector_size,
        store_domains=store_domains
    )

    await copier.copy_collection(
        force_recreate=force_recreate,
        batch_size=batch_size
    )

    logger.info(f"collection复制完成: {source_collection} -> {target_collection}")
    return f"成功复制 {source_collection} 到 {target_collection}"


async def copy_all_knowledge_collections_with_qwen3(
        source_qdrant_host: str,
        source_qdrant_port: int,
        target_qdrant_host: str,
        target_qdrant_port: int,
        force_recreate: bool = False,
        batch_size: int = 100,
        qwen3_embed_url: str = "http://*************:8088/qwen3_4b_embed",
        sparse_embed_url: str = None,
        vector_size: int = 2560,
        store_domains: List[str] = None
):
    """批量复制所有knowledge collections，使用qwen3重新生成向量"""
    logger.info("开始批量复制所有knowledge collections")

    collections_to_copy = [
        {
            "source": "shopify_product_knowledge_0613",
            "target": "shopify_product_knowledge_0613_qwen3"
        },
        {
            "source": "seller_knowledge_extra_questions_sparse_vectors",
            "target": "seller_knowledge_extra_questions_sparse_vectors_qwen3"
        },
        {
            "source": "shopify_collection_knowledge",
            "target": "shopify_collection_knowledge_qwen3"
        }
    ]

    results = []

    for collection_info in collections_to_copy:
        source_collection = collection_info["source"]
        target_collection = collection_info["target"]

        logger.info(f"开始复制: {source_collection} -> {target_collection}")

        try:
            copier = OptimizedQdrantCollectionCopier(
                source_collection=source_collection,
                target_collection=target_collection,
                source_qdrant_host=source_qdrant_host,
                source_qdrant_port=source_qdrant_port,
                target_qdrant_host=target_qdrant_host,
                target_qdrant_port=target_qdrant_port,
                qwen3_embed_url=qwen3_embed_url,
                sparse_embed_url=sparse_embed_url,
                vector_size=vector_size,
                store_domains=store_domains
            )

            await copier.copy_collection(
                force_recreate=force_recreate,
                batch_size=batch_size
            )

            results.append(f"成功复制 {source_collection} 到 {target_collection}")
            logger.info(f"完成复制: {source_collection} -> {target_collection}")

        except Exception as e:
            error_msg = f"复制 {source_collection} 失败: {e}"
            results.append(error_msg)
            logger.error(error_msg)

    logger.info("批量复制完成")
    return results


def main():
    """主函数 - 直接调用copy_all_knowledge_collections_with_qwen3"""
    # 直接设置参数
    force_recreate = False
    batch_size = 100
    source_qdrant_host = "*************"
    source_qdrant_port = 6333
    target_qdrant_host = "0.0.0.0"
    target_qdrant_port = 6333
    qwen3_embed_url = "http://*************:8088/qwen3_4b_embed"
    sparse_embed_url = "http://*************:8002/sparse_embed"
    vector_size = 2560
    # store_domains = ["ororo-ca.myshopify.com", "zgrills.myshopify.com",
    # "cyxus.myshopify.com", "p0e11s-ng.myshopify.com"]
    store_domains = ["p0e11s-ng.myshopify.com"]

    logger.info("开始复制knowledge collections")
    logger.info(f"源qdrant: {source_qdrant_host}:{source_qdrant_port}")
    logger.info(f"目标qdrant: {target_qdrant_host}:{target_qdrant_port}")
    logger.info(f"指定store domains: {store_domains}")

    try:
        asyncio.run(copy_all_knowledge_collections_with_qwen3(
            source_qdrant_host=source_qdrant_host,
            source_qdrant_port=source_qdrant_port,
            target_qdrant_host=target_qdrant_host,
            target_qdrant_port=target_qdrant_port,
            force_recreate=force_recreate,
            batch_size=batch_size,
            qwen3_embed_url=qwen3_embed_url,
            sparse_embed_url=sparse_embed_url,
            vector_size=vector_size,
            store_domains=store_domains
        ))
        logger.info("所有knowledge collections复制完成！")
    except Exception as e:
        logger.error(f"复制过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
