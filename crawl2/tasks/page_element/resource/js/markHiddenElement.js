(function () {
    const MIN_DIMENSION = 2;
    const MARK_ATTRIBUTE = 'data-shop-genius-visibility-status';
    const MARK_VALUE = 'hidden_or_tiny';

    /**
     * Checks if an element is truly visible and has a meaningful size.
     * @param {Element} element The DOM element to check.
     * @returns {boolean} True if the element is visible and large enough, false otherwise.
     */
    function isElementVisibleAndSized(element) {
        if (!element.parentElement) {
            // Document or detached elements, or elements that are part of the root
            // console.log(`Skipping detached or root element: ${element.tagName}`);
            return false;
        }

        // Get computed styles for accurate visibility checks
        const computedStyle = window.getComputedStyle(element);

        // Check for common visibility properties
        if (computedStyle.display === 'none' ||
            computedStyle.visibility === 'hidden' ||
            parseFloat(computedStyle.opacity) === 0) {
            return false;
        }

        // Check for dimensions using getBoundingClientRect
        // This gives the size and position relative to the viewport
        const rect = element.getBoundingClientRect();

        // If width or height is less than MIN_DIMENSION, consider it tiny
        if (rect.width < MIN_DIMENSION || rect.height < MIN_DIMENSION) {
            return false;
        }

        // Check if the element is outside the viewport
        // This is a basic check; elements can be off-screen and still "visible" in some contexts.
        // For strict visibility, you might also check if it's within scrollable areas.
        if (rect.right < 0 || rect.bottom < 0 ||
            rect.left > window.innerWidth || rect.top > window.innerHeight) {
            // Exclude elements that are clearly off-screen, unless they have non-zero dimensions
            // and might become visible via scrolling. For this script, we're focusing on
            // actively hidden or tiny elements, so off-screen elements without active dimensions
            // are considered not effectively visible.
            if (rect.width === 0 && rect.height === 0) {
                return false;
            }
        }

        // Check if the element has no actual rendered content and is just a container with no size
        // This can happen with empty divs or spans without explicit dimensions
        if (element.children.length === 0 && element.textContent.trim() === '' && rect.width === 0 && rect.height === 0) {
            return false;
        }

        return true;
    }

    // Get all elements in the document
    const allElements = document.querySelectorAll('*');

    // Iterate through elements and mark them
    allElements.forEach(element => {
        // Skip script and style tags as they are not visual DOM elements
        if (element.tagName === 'SCRIPT' || element.tagName === 'STYLE' || element.tagName === 'NOSCRIPT' || element.tagName === 'META' || element.tagName === 'LINK' || element.tagName === 'TITLE') {
            return;
        }

        // Exclude the HTML and BODY elements themselves, as they are document containers
        if (element === document.documentElement || element === document.body) {
            return;
        }

        // Check if the element or any of its ancestors are already marked as hidden/tiny
        // This helps avoid redundant checks and marking descendants of already hidden elements
        let isAncestorMarked = false;
        let currentParent = element.parentElement;
        while (currentParent) {
            if (currentParent.hasAttribute(MARK_ATTRIBUTE) && currentParent.getAttribute(MARK_ATTRIBUTE) === MARK_VALUE) {
                isAncestorMarked = true;
                break;
            }
            currentParent = currentParent.parentElement;
        }

        if (isAncestorMarked) {
            return; // Skip if a parent is already marked as hidden
        }

        if (!isElementVisibleAndSized(element)) {
            element.setAttribute(MARK_ATTRIBUTE, MARK_VALUE);
            // Optional: console.log(`Marked element: ${element.tagName} ${element.id ? '#' + element.id : ''} ${element.className ? '.' + element.className : ''}`);
        }
    });

    // Optional: Log a message when the script completes
    // console.log('Invisible/tiny element marking script completed.');
})();