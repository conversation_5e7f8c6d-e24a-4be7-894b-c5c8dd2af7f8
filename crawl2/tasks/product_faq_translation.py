import json

from jinja2 import Template
from loguru import logger
from tortoise.exceptions import DoesNotExist

from crawl2.clients import llm
from crawl2.db import models
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync

llm_config = llm.LLMConfig("usq", "qwen2.5-32b-chat-fp8-offline", 0, 2000, enable_thinking=False)


prompt_template = Template("""
following is a list of english frequently asked questions (FAQs), with a format of json objects,
please translate it to Chinese, and return the result in the same format json array of objects, leave the keys `question` and `answer` unchanged,
but translate the values of 'question' and 'answer' to Chinese.

the input is:

```json
{{ faqs }}
```
""")


@celery_task
async def translate_single_product_faq(product_url: str) -> None:
    """翻译单个产品的所有FAQ"""
    try:
        faqs_record = await models.ProductFaqs.filter(product__url=product_url).get_or_none()
        if not faqs_record or not faqs_record.faqs:
            logger.info(f"No FAQs found for product {product_url}")
            return
        if all(faq.get('question_chn') and faq.get('answer_chn') for faq in faqs_record.faqs):
            logger.info(f"All FAQs for product {product_url} are already translated")
            return

        faqs_for_translation = [{'question': f['question'], 'answer': f['answer']} for f in faqs_record.faqs]
        prompt = prompt_template.render(faqs=json.dumps(faqs_for_translation, ensure_ascii=False, indent=2))
        result = await llm.call_llm(llm_config, prompt)
        logger.info(f"Translating FAQs of product {product_url} with result: {result}")

        if not result or not isinstance(result, list):
            logger.warning(f"No translation result or unexpected result type for FAQs of product {product_url}")
            return

        if len(result) != len(faqs_record.faqs):
            logger.warning(f"Mismatch in length of translated FAQs for {product_url}")
            return

        for i, faq in enumerate(faqs_record.faqs):
            translated_faq = result[i]
            if isinstance(translated_faq, dict):
                faq['question_chn'] = translated_faq.get('question', '')
                faq['answer_chn'] = translated_faq.get('answer', '')

        await faqs_record.save()
        logger.info(f"Successfully translated FAQs for product {product_url}")

    except DoesNotExist:
        logger.warning(f"Product with URL {product_url} not found for FAQ translation")
    except Exception as e:
        logger.error(f"Error translating FAQs for {product_url}: {str(e)}")


@celery_task
async def translate_all_product_faqs(domain: str) -> None:
    """翻译指定站点的所有产品FAQ"""
    try:
        site = await models.ShopifySite.get(domain=domain)
        products = await models.ProductMetadata.filter(site=site)
        translation_tasks = [
            translate_single_product_faq(product.url) for product in products
        ]
        await CeleryTaskTqdmAsync.gather(*translation_tasks, desc=f"Translating FAQs for {domain}")
    except DoesNotExist:
        logger.warning(f"Site with domain {domain} not found")
    except Exception as e:
        logger.error(f"Error translating FAQs for domain {domain}: {str(e)}")
