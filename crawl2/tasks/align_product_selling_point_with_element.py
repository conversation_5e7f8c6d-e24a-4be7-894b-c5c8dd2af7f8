import asyncio
from concurrent.futures import ThreadPoolExecutor, wait

from lxml import etree
from tqdm.asyncio import tqdm as tqdm_async
from tqdm import tqdm
from loguru import logger

from crawl2 import utils
from crawl2.clients import cos, llm
from crawl2.db import operations
import html2text
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync


ALIGN_PROMPT = """
你是一位经验丰富的电商运营专家，需要判断给定的商品卖点是否直接来源于商品详情页的特定区域内容。

判断标准：
1. 该区域内容必须与卖点完全相关
2. 该区域内容必须是卖点的直接来源或核心信息
3. 如果区域内容只是泛泛提及相关概念，而不是具体描述卖点，则不算相关

只输出 True 或者 False, True 表示该区域内容是该卖点的直接来源，False 表示不是。
不要输出额外的解释。

## 商品详情页特定区域内容
{element_content}

## 场景化卖点
{scenario_selling_point}

## 判断：该区域内容是否是该卖点的直接来源
"""


llm_conf = llm.LLMConfig("usq", "qwen2.5-32b-chat-fp8-offline", 0.6, 10)


async def get_html(url: str):
    page_url = "".join(utils.split_domain_and_path(url))
    cos_client = cos.CosClient.get_client()
    logger.info(f"get page {page_url} html from cos")
    return cos_client.read_page_html(page_url)


def get_page_element_with_content(page_html: str, selectors: list[str]):
    parser = etree.HTMLParser()
    tree = etree.fromstring(page_html, parser)
    h = html2text.HTML2Text()
    h.ignore_links = True
    h.ignore_images = True
    h.body_width = 0  # Disable line wrapping
    matched_contents = []
    for selector in selectors:
        elements = tree.xpath(selector)
        for element in elements:
            # Convert element to string and then to markdown
            element_html = etree.tostring(element, encoding="unicode", method="html")
            element_markdown = h.handle(element_html)
            matched_contents.append(element_markdown.strip())
    content = "\n".join(matched_contents)
    content = content.strip()
    return content


async def call_with_payload(prompt, payload):
    llm_response = await llm.call_llm(llm_conf, prompt, parse_json=False)
    return llm_response, payload


async def align_one_product_selling_points(
    point, product_url, product_elements, cached_element_content
):
    tasks = []
    for element in product_elements:
        try:
            target = element.target
            selectors = element.selectors
            if target not in cached_element_content:
                continue
            element_content = cached_element_content[target]
            if not element_content:
                logger.info(f"element {selectors} content is empty, skip.")
                continue

            aspect = point.name
            description = point.description
            prompt = ALIGN_PROMPT.format(
                element_content=element_content,
                scenario_selling_point={
                    "aspect": aspect,
                    "description": description,
                },
            )
            tasks.append(
                call_with_payload(
                    prompt,
                    {
                        "element": element,
                        "prompt": prompt,
                        "element_content": element_content,
                    },
                )
            )
        except Exception as e:
            logger.error(f"error when align one scenario selling points: {e}")
            continue
    related_elements = []
    related_element_contents = []
    for task in tqdm_async(
        asyncio.as_completed(tasks),
        total=len(tasks),
        desc="Generate Scenario Selling Point Align",
    ):
        content, payload = await task
        target = payload["element"].target
        selectors = payload["element"].selectors
        element_content = payload["element_content"]
        logger.info(
            "get content: {} product_url: {} element: {} prompt: {}",
            content,
            product_url,
            f"{target}:{selectors}",
            payload["prompt"],
        )
        if content and "True" in content:
            related_elements.append(payload["element"])
            related_element_contents.append(element_content)
    if related_elements:
        logger.info(f"related elements: {related_elements}")
        await point.elements.clear()
        await point.elements.add(*related_elements)
        await point.save()
    return related_elements, related_element_contents


def process_element(element, page_html, cached_element_content):
    """Function to process a single element and store its content."""
    element_content = get_page_element_with_content(page_html, element.selectors)
    cached_element_content[element.target] = element_content


async def get_cached_element_content(product):
    product_url = "".join(utils.split_domain_and_path(product.url))
    product_elements = await operations.list_raw_page_elements(product_url)
    logger.info("product_url: {} product_elements: {}", product_url, product_elements)
    page_html = await get_html(product.url)
    cached_element_content = {}
    if not page_html:
        logger.warning(f"Page html {product.url} not found in OSS, skip.")
        return cached_element_content, product_elements
    logger.info("get page {} html: {}", product.url, page_html[:100] + "...")
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [
            executor.submit(process_element, element, page_html, cached_element_content)
            for element in product_elements
        ]
        wait(futures)

    return cached_element_content, product_elements


@celery_task
async def align_product_selling_points(domain: str):
    """
    将站点所有挖掘到的店铺卖点营销话术和页面的element进行对齐.
    """
    all_products = await operations.list_product_metadata(domain)
    tasks = []
    for product in tqdm(all_products):
        cached_element_content, product_elements = await get_cached_element_content(
            product
        )
        logger.info("cached_element_content: {}", cached_element_content)
        points = await product.selling_points.all()
        for point in points:
            tasks.append(
                align_one_product_selling_points(
                    point, product.url, product_elements, cached_element_content
                )
            )

    await CeleryTaskTqdmAsync.gather(*tasks, desc="Align Product Selling Points")


@celery_task
async def align_single_product_selling_point(product_url: str):
    """
    将某个商品挖掘到的店铺卖点营销话术和页面的element进行对齐.
    """
    product = await operations.get_product_metadata(product_url)
    if not product:
        logger.error(f"Product {product_url} not found in db, skip.")
        return
    cached_element_content, product_elements = await get_cached_element_content(product)
    points = await product.selling_points.all()
    tasks = []
    for point in points:
        tasks.append(
            align_one_product_selling_points(
                point, product.url, product_elements, cached_element_content
            )
        )

    await CeleryTaskTqdmAsync.gather(
        *tasks, desc="Align Single Product Selling Points"
    )
