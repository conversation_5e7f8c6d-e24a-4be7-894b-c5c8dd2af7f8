<template>
  <div class="w-full">
    <div v-if="loading" class="text-center p-4 text-gray-600">
      Loading FAQs...
    </div>
    <div v-else-if="error" class="text-center p-4 text-red-500">
      Error: {{ error }}
    </div>
    <div v-else-if="faqs && faqs.faqs && faqs.faqs.length > 0" class="space-y-4">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">Product FAQs({{ faqs.faqs.length }})</h3>
        <div class="flex items-center gap-2">
          <div>
            <span
                :class="{
                'bg-blue-100 text-blue-800': faqs.status === 'fresh',
                'bg-yellow-100 text-yellow-800': faqs.status === 'editing',
                'bg-green-100 text-green-800': faqs.status === 'ready',
                'bg-gray-100 text-gray-800': faqs.status === 'disabled',
                'bg-red-100 text-red-800': faqs.status === 'archived'
              }"
                class="px-2 py-1 text-xs font-medium rounded-full capitalize"
            >
              {{ faqs.status }}
            </span>
            <div v-if="faqs.last_edited_at" class="text-xs text-gray-500 text-right">
              Last edited: {{ new Date(faqs.last_edited_at).toLocaleString() }}
            </div>
          </div>
        </div>
      </div>
      
      <div class="space-y-3">
        <div 
          v-for="(faq, index) in faqs.faqs" 
          :key="index"
          class="bg-gray-50 rounded-lg p-4 border border-gray-200"
        >
          <div class="space-y-2">
            <div>
              <h4 class="font-medium text-gray-900">Question:</h4>
              <p class="text-gray-700">{{ faq.question || 'No question provided' }}</p>
              <p v-if="faq.question_chn" class="text-gray-600 text-sm mt-1">{{ faq.question_chn }}</p>
            </div>
            <div>
              <h4 class="font-medium text-gray-900">Answer:</h4>
              <p class="text-gray-700">{{ faq.answer || 'No answer provided' }}</p>
              <p v-if="faq.answer_chn" class="text-gray-600 text-sm mt-1">{{ faq.answer_chn }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="text-center p-8 text-gray-500">
      No FAQs available for this product.
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useProduct } from '@/composables/useProduct.js'

const props = defineProps({
  domain: {
    type: String,
    required: true
  },
  productId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['dataCountChange'])

const { getProductFaqs } = useProduct()
const faqs = ref(null)
const loading = ref(false)
const error = ref(null)

const fetchFaqs = async () => {
  try {
    loading.value = true
    error.value = null
    faqs.value = await getProductFaqs(props.domain, props.productId)
    
    // 发送数据量变化事件
    if (faqs.value && faqs.value.faqs) {
      emit('dataCountChange', faqs.value.faqs.length)
    } else {
      emit('dataCountChange', 0)
    }
  } catch (err) {
    error.value = err.message
    console.error('Error fetching product FAQs:', err)
    emit('dataCountChange', 0)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchFaqs()
})

// 暴露刷新方法给父组件
defineExpose({
  refresh: fetchFaqs
})
</script>
