# Shop Pupil

为 **Shopify Genius** 和 **LeChat** 两个电商 SaaS 提供离线的数据爬取、挖掘、标注、更新等管理服务。

## 项目概述

Shop Pupil 是一个专门为电商 SaaS 平台设计的数据管理服务，主要功能包括：

- **数据爬取**: 从 Shopify 和 LeChat 站点自动提取产品信息、页面内容等
- **数据挖掘**: 使用 AI 技术挖掘产品卖点、用户关注点、FAQ 等
- **数据标注**: 对爬取的数据进行智能标注和分类
- **数据更新**: 定期更新和维护数据，确保数据时效性
- **RAG 支持**: 为检索增强生成（RAG）提供高质量的数据集

## 部署环境

| 环境       | 地址 | 用途 | 部署方式 |
|----------|------|------|----------|
| office环境 | http://************:8000 | 日常开发调试 | 在MR上手动触发 ci job 发布（不包括ddl）或者合并到 master 分支后自动发布（包括 ddl） |
| 预发环境     | https://shop-pupil-stq.leyanbot.com | 功能测试和验证 | 通过 [create](https://create.infra.leyantech.com/#/backend-app/shop-pupil/runtime) 构建镜像和发布 |
| 生产环境     | https://shop-pupil.leyanbot.com | 正式服务 | 通过 [create](https://create.infra.leyantech.com/#/backend-app/shop-pupil/runtime) 构建镜像和发布 |

## 开发环境初始化
本项目的运行时前后端分离，但代码仓库统一。支持在本地同时启动前后端进行开发调试。

### 后端环境

1. **创建虚拟环境**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   ```

2. **安装依赖**
   ```bash
   pip install -r dev-requirements.txt
   ```

3. **启动后端服务**
   ```bash
   cd crawl2
   uvicorn web.main:app --reload --host 0.0.0.0 --port 8181
   ```

### 前端环境

1. **进入前端目录**
   ```bash
   cd ui/
   ```

2. **安装依赖**
   ```bash
   pnpm install
   ```

3. **启动开发服务器**
   ```bash
   pnpm dev
   ```

**注意**: 如果没有启动后端服务，vite 将自动使用 office 环境，也就是 http://************:8000 提供的 api。

## 数据库 DDL 指南

### 开发阶段

1. **修改数据模型**
   - 在 `crawl2/db/models.py` 中修改表结构定义

2. **生成迁移脚本**
   ```bash
   aerich migrate
   ```

3. **提交代码**
   - 将修改的 `models.py` 和生成的迁移脚本一起提交
   - 发起 Merge Request 进行代码审查

### 部署阶段

1. **代码审查通过后**
   - 合并到 `master` 分支
   - 等待 ci pipeline 自动执行 office 环境的 DDL 和 发布 office 环境
   - 如果需要执行 stq/prq 环境的DDL，请手动触发 master 分支的 CI Job

## Task 开发指南

### 基本规范

- **位置**: 在 `crawl2/tasks/` 包下创建新的 task 文件
- **函数类型**: 必须是 `async` 异步函数
- **装饰器**: 使用 `@celery_task` 装饰器
- **参数类型**: 只支持基础类型（str, int, float, bool, None）或基础类型的列表
- **幂等性**: 确保 task 可以重复执行而不产生副作用
- **返回值**: 尽量不要返回业务数据, 而是将业务数据存入数据库

### 开发示例

参考 `crawl2/tasks/demo.py` 的实现：

```python
from loguru import logger
from crawl2.tasks.celery_wrap import celery_task
from crawl2.clients import http

@celery_task
async def simple_demo(domain: str):
    """
    用于展示挖掘任务定义与使用的简单 demo.
    """
    logger.info(f'hello there: {domain}')
    meta_url = f"https://{domain}/meta.json"
    response = await http.http_get(meta_url)
    response.raise_for_status()
    logger.info(f'meta.json response: {response.text}')
```

### 测试和调试

1. **浏览已有 Tasks**
   - 访问前端页面 `/task-definitions`
   - 查看所有可用的 task 定义和参数

2. **触发 Task 执行**
   - 在 Task Definitions 页面点击 "Trigger" 按钮
   - 填写必要参数后执行测试

3. **查看执行历史**
   - 访问 `/task-runs` 页面查看任务执行历史
   - 查看执行状态、日志和结果

## 项目结构

```
shop-pupil/
├── crawl2/                    # 后端核心代码
│   ├── tasks/                 # 任务定义
│   ├── db/                    # 数据库模型和操作
│   ├── clients/               # 各种客户端
│   ├── web/                   # Web API
│   └── config.py              # 配置管理
├── ui/                        # 前端 Vue.js 应用
├── migrations/                # 数据库迁移文件
├── tests/                     # 测试用例
└── requirements.txt           # Python 依赖
```

## 技术栈

- **后端**: FastAPI + Celery + Tortoise ORM + PostgreSQL
- **前端**: Vue 3 + Vite + Tailwind CSS + AG Grid
- **任务队列**: Redis + Celery
- **向量数据库**: Qdrant
- **AI 服务**: 自研 LLM 服务

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request
