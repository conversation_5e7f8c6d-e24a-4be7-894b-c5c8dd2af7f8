(function() {
    /**
     * 为给定的 DOM 元素生成一组健壮的 XPath 定位符。
     * 列表按从最稳定到最不稳定的顺序排列。
     *
     * @param {Element} element 需要为其生成 XPath 的 DOM 元素。
     * @returns {string[]} 一个包含 XPath 字符串的数组。
     */
    function generateXPaths(element) {
        // --- 1. 输入验证 ---
        if (!(element instanceof Element)) {
            console.error("输入无效：请提供一个有效的 DOM 元素。");
            return [];
        }

        // --- 2. 内部辅助函数的声明 ---

        /**
         * 替换原有的 Step class，使用普通对象。
         * @param {string} value - XPath 步骤的值。
         * @param {boolean} optimized - 是否为优化过的步骤。
         * @returns {{value: string, optimized: boolean}}
         */
        const createStep = (value, optimized) => {
            return {
                value: value, optimized: optimized || false,
            };
        };

        /**
         * 比较两个节点是否在 XPath 中被视为相似（例如，具有相同的标签名）。
         */
        const areNodesSimilar = (left, right) => {
            if (left === right) {
                return true;
            }

            if (left.nodeType === Node.ELEMENT_NODE && right.nodeType === Node.ELEMENT_NODE) {
                return left.localName === right.localName;
            }

            if (left.nodeType === right.nodeType) {
                return true;
            }

            // XPath 将 CDATA 视为文本节点。
            const leftType = left.nodeType === Node.CDATA_SECTION_NODE ? Node.TEXT_NODE : left.nodeType;
            const rightType = right.nodeType === Node.CDATA_SECTION_NODE ? Node.TEXT_NODE : right.nodeType;
            return leftType === rightType;
        };

        /**
         * 计算节点在其同级中的 XPath 索引。
         * 返回 -1 表示错误，0 表示没有相似的同级节点，否则返回 XPath 索引（从1开始）。
         */
        const xPathIndex = (node) => {
            const siblings = node.parentNode ? node.parentNode.children : null;
            if (!siblings) {
                return 0; // 根节点没有同级。
            }

            let hasSameNamedElements = false;
            for (let i = 0; i < siblings.length; ++i) {
                if (areNodesSimilar(node, siblings[i]) && siblings[i] !== node) {
                    hasSameNamedElements = true;
                    break;
                }
            }
            if (!hasSameNamedElements) {
                return 0;
            }

            let ownIndex = 1; // XPath 索引从 1 开始。
            for (let i = 0; i < siblings.length; ++i) {
                if (areNodesSimilar(node, siblings[i])) {
                    if (siblings[i] === node) {
                        return ownIndex;
                    }
                    ++ownIndex;
                }
            }
            return -1; // 错误：在父节点的子节点中未找到当前节点。
        };

        /**
         * 为单个节点计算 XPath 值（一个步骤）。
         */
        const xPathValue = (node, optimized) => {
            let ownValue;
            const ownIndex = xPathIndex(node);
            if (ownIndex === -1) {
                return null; // 错误。
            }

            switch (node.nodeType) {
                case Node.ELEMENT_NODE:
                    if (optimized && node.getAttribute("id")) {
                        return createStep('//*[@id="' + node.getAttribute("id") + '"]', true);
                    }
                    ownValue = node.localName;
                    break;
                case Node.ATTRIBUTE_NODE:
                    ownValue = "@" + node.nodeName;
                    break;
                case Node.TEXT_NODE:
                case Node.CDATA_SECTION_NODE:
                    ownValue = "text()";
                    break;
                case Node.PROCESSING_INSTRUCTION_NODE:
                    ownValue = "processing-instruction()";
                    break;
                case Node.COMMENT_NODE:
                    ownValue = "comment()";
                    break;
                case Node.DOCUMENT_NODE:
                    ownValue = "";
                    break;
                default:
                    ownValue = "";
                    break;
            }

            if (ownIndex > 0) {
                ownValue += "[" + ownIndex + "]";
            }

            return createStep(ownValue, node.nodeType === Node.DOCUMENT_NODE);
        };

        /**
         * 生成 Chrome DevTools 风格的 XPath。
         */
        const chromeGetXPath = (node, optimized) => {
            if (node.nodeType === Node.DOCUMENT_NODE) {
                return "/";
            }

            const steps = [];
            let contextNode = node;
            while (contextNode) {
                const step = xPathValue(contextNode, optimized);
                if (!step) {
                    break; // 发生错误，提前退出。
                }
                steps.push(step);
                if (step.optimized) {
                    break;
                }
                contextNode = contextNode.parentNode;
            }

            steps.reverse();
            // 使用 .map(s => s.value) 是因为我们现在用的是普通对象而非 class 实例
            const path = steps.map(s => s.value).join("/");
            return (steps.length && steps[0].optimized ? "" : "/") + path;
        };

        /**
         * 检查 XPath 是否唯一（只匹配一个元素）。
         */
        const isXPathUnique = (xpath) => {
            try {
                return (document.evaluate(`count(${xpath})`, document, null, XPathResult.NUMBER_TYPE, null).numberValue === 1);
            } catch (e) {
                return false; // 无效的 XPath。
            }
        };

        // 使用 Set 避免重复的 XPath
        const xpaths = new Set();
        const TAG_NAME = element.tagName.toLowerCase();

        /**
         * 如果候选 XPath 是唯一的，则添加它。
         */
        const addUniqueXPath = (xpath) => {
            if (isXPathUnique(xpath)) {
                xpaths.add(xpath);
            }
        };

        // --- 3. XPath 生成策略 ---

        // 策略 1: ID 和测试专用属性 (最高稳定性)
        if (element.id) {
            addUniqueXPath(`//${TAG_NAME}[@id='${element.id}']`);
        }
        // const testAttributes = ['data-testid', 'data-cy', 'data-test'];
        // for (const attr of testAttributes) {
        //   const value = element.getAttribute(attr);
        //   if (value) {
        //     addUniqueXPath(`//${TAG_NAME}[@${attr}='${value}']`);
        //   }
        // }

        // 策略 2: 基于锚点的 XPath 轴
        // a) 查找一个稳定的父锚点 (带ID)
        let parent = element.parentElement;
        while (parent && parent !== document.body) {
            if (parent.id) {
                const parentXPath = `//${parent.tagName.toLowerCase()}[@id='${parent.id}']`;
                const nameAttr = element.getAttribute("name");
                if (nameAttr) {
                    addUniqueXPath(`${parentXPath}//${TAG_NAME}[@name='${nameAttr}']`);
                }
                const classAttr = element.className;
                if (classAttr && typeof classAttr === 'string') { // 确保 className 是字符串
                    addUniqueXPath(`${parentXPath}//${TAG_NAME}[@class='${classAttr}']`);
                }
                break; // 找到第一个带锚点的父元素即可。
            }
            parent = parent.parentElement;
        }

        // b) 查找一个稳定的同级锚点 (如 <label>)
        const precedingLabel = element.previousElementSibling;
        if (precedingLabel && precedingLabel.tagName.toLowerCase() === "label" && precedingLabel.textContent) {
            const labelText = precedingLabel.textContent.trim();
            if (labelText) {
                addUniqueXPath(`//label[normalize-space()='${labelText}']/following-sibling::${TAG_NAME}`);
            }
        }

        // 策略 3: 其他特定的、有用的属性
        const specificAttributes = ["name", "placeholder", "title", "alt", "aria-label"];
        for (const attr of specificAttributes) {
            const value = element.getAttribute(attr);
            if (value) {
                addUniqueXPath(`//${TAG_NAME}[@${attr}='${value}']`);
            }
        }

        // 策略 4: 文本内容 (适用于按钮、链接等)
        const text = (element.textContent ?? "").trim();
        if (text && !text.includes("\n")) { // 避免过长或多行的文本
            addUniqueXPath(`//${TAG_NAME}[normalize-space()='${text}']`);
            addUniqueXPath(`//${TAG_NAME}[contains(normalize-space(), '${text}')]`);
        }

        // 策略 5: Class 和属性组合
        const className = element.className;
        const type = element.getAttribute("type");
        if (className && typeof className === 'string' && type) {
            const singleClass = className.split(" ")[0];
            if (singleClass) {
                addUniqueXPath(`//${TAG_NAME}[contains(@class, '${singleClass}') and @type='${type}']`);
            }
        }

        // 策略 6: Chrome 优化版 XPath
        try {
            addUniqueXPath(chromeGetXPath(element, true));
        } catch (e) { /* 忽略错误 */
        }

        // 策略 7: Chrome 普通版 (带索引) XPath
        try {
            addUniqueXPath(chromeGetXPath(element));
        } catch (e) { /* 忽略错误 */
        }

        // 策略 8: 位置索引 (最不稳定，作为最后的备选)
        if (xpaths.size === 0) {
            try {
                const allSameTagElements = Array.from(document.getElementsByTagName(TAG_NAME));
                const indexInAll = allSameTagElements.indexOf(element);
                if (indexInAll !== -1) {
                    const xpathIndex = indexInAll + 1; // XPath 索引是1-based
                    const positionalXPath = `(//${TAG_NAME})[${xpathIndex}]`;
                    xpaths.add(positionalXPath); // 即使不唯一也添加，作为最后手段。
                }
            } catch (e) { /* 忽略错误 */
            }
        }

        return Array.from(xpaths);
    }

    function getXPath(element) {
        if (!element || element.nodeType !== Node.ELEMENT_NODE) {
            return "";
        }
        const segments = [];
        let currentElement = element;
        while (currentElement && currentElement.nodeType === Node.ELEMENT_NODE && currentElement.nodeName.toLowerCase() !== 'html') {
            let segment = currentElement.nodeName.toLowerCase();
            if (currentElement.id) {
                segment += `[@id='${currentElement.id}']`;
            } else {
                let sameTagSiblings = 0;
                let sibling = currentElement.previousElementSibling;
                while (sibling) {
                    if (sibling.nodeName.toLowerCase() === currentElement.nodeName.toLowerCase()) {
                        sameTagSiblings += 1;
                    }
                    sibling = sibling.previousElementSibling;
                }

                // XPath 索引是从 1 开始的
                const index = sameTagSiblings + 1;
                segment += `[${index}]`;
            }
            segments.unshift(segment);
            currentElement = currentElement.parentElement;
        }
        if (segments.length === 0) return "";
        return "/" + segments.join("/");
    }

    const allElements = Array.from(document.querySelectorAll("*"));
    let shopGeniusId = 1;
    for (const el of allElements) {
        const rect = el.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(el);
        const isVisible = (rect.width > 0 && rect.height > 0 && computedStyle.display !== "none" && computedStyle.visibility !== "hidden" && computedStyle.opacity !== "0");
        if (!isVisible) continue;
        const xpathList = Array.from(new Set([getXPath(el), ...generateXPaths(el)].filter(Boolean)));
        if (xpathList.length > 0) {
            el.setAttribute("data-shop-genius-xpath", JSON.stringify(xpathList));
        }
        el.setAttribute("data-shop-genius-bounding-box", JSON.stringify({
            x: rect.x + window.scrollX,
            y: rect.y + window.scrollY,
            width: rect.width,
            height: rect.height
        }));
        el.setAttribute("data-shop-genius-id", shopGeniusId);
        shopGeniusId = shopGeniusId + 1;
    }
})();