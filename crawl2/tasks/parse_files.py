"""
这个 module 用于定义从文件（用户上传的 txt,word,pdf 等文件）中提取 markdown 文本的任务.
"""
from loguru import logger

from crawl2 import schema
from crawl2.clients import docling, store_center
from crawl2.db import operations, models
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdm


async def process_one_file_parse_task(task: schema.ParseFileTaskRequest):
    try:
        store_center_client = store_center.StoreCenterClient.get_client()
        file_key = task.file_key
        url = await store_center_client.get_file_download_url(file_key)
        logger.info(f'Processing file: {file_key}, download URL: {url}')
        markdown_content = await docling.get_file_markdown_content(url)
    except Exception as e:
        logger.opt(exception=e).error(f'Error processing file: {task.file_key} - {e}')
        await operations.update_parse_file_task_status(task.task_id,
                                                       models.FileProcessStatus.MARKDOWN_EXTRACT_FAILED,
                                                       '')
        return
    await operations.update_parse_file_task_status(task.task_id,
                                                   models.FileProcessStatus.MARKDOWN_EXTRACTED,
                                                   markdown_content)


@celery_task
async def parse_all_files(domain: str):
    """
    将处理指定域名下所有尚未完成的文件解析任务，为每个任务中的文件提取 markdown 内容.
    """
    logger.info(f"Starting file parsing task for domain: {domain}")
    pending_tasks = await operations.list_parse_file_tasks_for_markdown_extracting(domain)
    for task in CeleryTaskTqdm(pending_tasks, desc="Processing file parsing tasks"):
        await process_one_file_parse_task(task)


@celery_task
async def parse_file_for_task(task_id: str):
    """
    解析指定(shopify-knowledge)任务的文件并提取 markdown 内容, 其中 task_id 是 shopify-knowledge 的任务 id，不是 shop-pupil 的 celery task id.
    """
    task = await operations.get_parse_file_task(task_id)
    if not task:
        logger.error(f"Task with ID {task_id} not found.")
        return
    await process_one_file_parse_task(task)
