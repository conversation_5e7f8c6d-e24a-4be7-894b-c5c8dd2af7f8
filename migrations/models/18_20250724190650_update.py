from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "page_elements" ADD "data" JSONB NOT NULL;
        ALTER TABLE "shopify_page" ADD "data" JSONB NOT NULL;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "page_elements" DROP COLUMN "data";
        ALTER TABLE "shopify_page" DROP COLUMN "data";"""
