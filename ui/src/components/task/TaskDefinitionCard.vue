<template>
  <Card class="hover:shadow-lg transition-shadow duration-200">
    <!-- 卡片头部 -->
    <CardHeader class="pb-3">
      <div class="flex flex-col space-y-3">
        <!-- 任务名称和描述 -->
        <div class="space-y-2">
          <CardTitle class="text-lg break-words">{{ task.name }}</CardTitle>
          <p class="text-gray-600 text-sm leading-relaxed break-words">{{ task.description }}</p>
        </div>
        
        <!-- 执行按钮和作者信息 -->
        <div class="flex items-center justify-between">
          <div v-if="task.authors && task.authors.length" class="flex items-center text-sm text-gray-500">
            <span class="mr-2">👥</span>
            <span class="break-words">{{ task.authors.join(', ') }}</span>
          </div>
          <div class="flex-shrink-0 ml-3">
            <CrawlTaskTrigger 
              :taskName="task.name"
              :display-name="'执行'"
            />
          </div>
        </div>
      </div>
    </CardHeader>
    
    <!-- 参数信息 -->
    <CardContent v-if="task.params && task.params.properties" class="pt-0 pb-3 border-b border-gray-200 bg-gray-50/50">
      <h4 class="text-sm font-medium text-gray-700 mb-2">参数</h4>
      <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
        <div v-for="(schema, paramName) in task.params.properties" :key="paramName" class="text-sm">
          <span class="font-medium text-gray-600">{{ paramName }}:</span>
          <span class="text-gray-500 ml-1">{{ getParamType(schema) }}</span>
          <span v-if="schema.required !== undefined" class="text-red-500 ml-1">
            {{ schema.required ? '*' : '' }}
          </span>
        </div>
      </div>
    </CardContent>
    
    <!-- 执行统计 -->
    <CardContent class="pt-3">
      <div class="flex items-center justify-between mb-2">
        <h4 class="text-sm font-medium text-gray-700">执行统计</h4>
        <div class="text-xs text-gray-500">点击统计项查看详细历史</div>
      </div>
      <TaskRunStats 
        :stats="task.stats" 
        @view-history="handleViewHistory"
      />
    </CardContent>
  </Card>
</template>

<script setup>
import { useRouter } from 'vue-router'
import CrawlTaskTrigger from './CrawlTaskTrigger.vue'
import TaskRunStats from './TaskRunStats.vue'
import Card from '../ui/card/Card.vue'
import CardHeader from '../ui/card/CardHeader.vue'
import CardTitle from '../ui/card/CardTitle.vue'
import CardContent from '../ui/card/CardContent.vue'

const router = useRouter()

const props = defineProps({
  task: {
    type: Object,
    required: true
  }
})

const getParamType = (schema) => {
  if (schema.type === 'array') {
    return `array of ${schema.items?.type || 'any'}`
  }
  if (schema.enum) {
    return `enum: ${schema.enum.join(', ')}`
  }
  if (schema.format) {
    return schema.format
  }
  return schema.type || 'any'
}

// 处理查看历史事件
const handleViewHistory = (historyData) => {
  const { type, label } = historyData
  console.log(`查看 ${label} 历史`, historyData)
  
  // 构建查询参数
  const query = {
    task_name: props.task.name
  }
  
  // 根据点击的统计类型添加额外的过滤条件
  if (type === 'failed_count') {
    query.status = 'FAILED'
  } else if (type === 'success_count') {
    query.status = 'SUCCESS'
  } else if (type === 'running_count') {
    query.status = 'STARTED'
  } else if (type === 'pending_count') {
    query.status = 'PENDING'
  }
  // total_runs 和 last_run 不需要额外状态过滤
  console.log(`查看 ${label} 历史`, query)

  // 跳转到任务运行历史页面
  router.push({
    path: '/task-runs',
    query
  })
}
</script>
