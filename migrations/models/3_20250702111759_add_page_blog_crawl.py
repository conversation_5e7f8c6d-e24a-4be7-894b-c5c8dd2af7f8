from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "blog_metadata" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "url" VARCHAR(255) NOT NULL UNIQUE,
    "store_domain" VARCHAR(255) NOT NULL,
    "raw_content" TEXT NOT NULL,
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_blog_metada_url_784ef9" ON "blog_metadata" ("url", "store_domain", "site_id");
COMMENT ON COLUMN "blog_metadata"."created_at" IS '创建时间';
COMMENT ON COLUMN "blog_metadata"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "blog_metadata"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "blog_metadata"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "blog_metadata"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "blog_metadata"."url" IS '博客的 URL';
COMMENT ON COLUMN "blog_metadata"."store_domain" IS '店铺域名';
COMMENT ON COLUMN "blog_metadata"."raw_content" IS '博客原始 Markdown 内容';
        CREATE TABLE IF NOT EXISTS "page_metadata" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "url" VARCHAR(255) NOT NULL UNIQUE,
    "store_domain" VARCHAR(255) NOT NULL,
    "raw_content" TEXT NOT NULL,
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_page_metada_url_d888dd" ON "page_metadata" ("url", "store_domain", "site_id");
COMMENT ON COLUMN "page_metadata"."created_at" IS '创建时间';
COMMENT ON COLUMN "page_metadata"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "page_metadata"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "page_metadata"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "page_metadata"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "page_metadata"."url" IS '页面的 URL';
COMMENT ON COLUMN "page_metadata"."store_domain" IS '店铺域名';
COMMENT ON COLUMN "page_metadata"."raw_content" IS '页面原始 Markdown 内容';
        ALTER TABLE "shopify_site" ADD "pages_urls" JSONB;
        ALTER TABLE "shopify_site" ADD "blogs_urls" JSONB;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "shopify_site" DROP COLUMN "pages_urls";
        ALTER TABLE "shopify_site" DROP COLUMN "blogs_urls";
        DROP TABLE IF EXISTS "page_metadata";
        DROP TABLE IF EXISTS "blog_metadata";"""
