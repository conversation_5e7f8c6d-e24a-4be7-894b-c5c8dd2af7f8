from crawl2.clients.llm import call_llm, LLMConfig
from crawl2.web.schema.aigc import (
    AigcGenRequest,
    MarketingCopyGenResponse,
    FAQGenResponse,
    QuestionGenResponse,
)

MARKETING_COPY_GEN_PROMPT_TEMPLATE = """
## Task
You are a seasoned e-commerce expert specializing in simulating authentic shopping guide sales copy for different shopping scenarios.
Based on the given knowledge, generate concise, friendly, and reassuring English sales copy that a real in-store shopping assistant might say to customers browsing the website.

## Generation Rules
1. Language Requirements:
   - Tone should be warm, natural, and proactive, as if you are standing next to the customer, helping them make decisions and addressing possible concerns
   - Avoid technical jargon and use simple, everyday language
2. Content Control:
   - The sales copy must directly reflect the topic, sub-topic, and name.
   - Strictly prohibit inventing or assuming any information not present in the knowledge
   - Each sentence should highlight a real benefit or address a possible concern
3. Diversity Requirements:
   - Generate 1-3 distinct sentences, each with a different focus or selling point
   - Avoid repetition and ensure each sentence is engaging
4. Length Requirement:
   - Each sentence should be under 100 characters (including spaces)
5. Language Consistency:
   - Output only in English

## Knowledge:
{knowledge}

## Output
Return a JSON array of 1-3 sales copy sentences. Do not include any explanations or formatting outside the JSON.

[
    "sentence1",
    "sentence2",
    "sentence3"
]
"""
FAQ_GEN_PROMPT_TEMPLATE = """
## Task
You are a seasoned e-commerce expert specializing in simulating authentic buyer questions and answers across different shopping stages.
Based on given knowledge, generate consumer questions and answers that are relevant to the knowledge topic.

## Generation Rules
1. Language Requirements:
   - Colloquial expressions (e.g.: "Does this have...", "Is free shipping available?")
   - Incorporate typical interrogative patterns (how, whether, which, why, what models)
   - Avoid technical terms (use "charger" instead of "power adapter")
2. Content Control:
   - Each question must contain explicit information points covered in the answer
   - Strictly prohibit introducing new information not covered in the original answer (e.g., fabricated specifications)
   - Preserve key qualifiers (e.g., "wireless charging", "Type-C port")
3. Diversity Requirements: Ensure varied phrasing without repetition while maintaining focus
4. Natural Language: Use conversational expressions and avoid jargon
5. Quantity Requirement: Generate 5 distinct questions and answers
6. Language Consistency: Generated questions and answers must match the knowledge's language

## Knowledge:
{knowledge}

## Output
Return a JSON array of objects, each with a \"q\" and \"a\" field. Do not include any explanations or formatting outside the JSON.

[
    {{"q": "Question 1", "a": "Answer 1"}},
    {{"q": "Question 2", "a": "Answer 2"}}
]
"""

QUESTION_GEN_PROMPT_TEMPLATE = """
## Task
You are a seasoned e-commerce expert specializing in simulating authentic buyer questions across different shopping stages.
Based on given knowledge, generate consumer questions that are relevant to the knowledge topic.

## Generation Rules
1. Language Requirements:
   - Colloquial expressions (e.g.: "Does this have...", "Is free shipping available?")
   - Incorporate typical interrogative patterns (how, whether, which, why, what models)
   - Avoid technical terms (use "charger" instead of "power adapter")
2. Content Control:
   - Each question must contain explicit information points covered in the answer
   - Strictly prohibit introducing new information not covered in the original answer (e.g., fabricated specifications)
   - Preserve key qualifiers (e.g., "wireless charging", "Type-C port")
3. Diversity Requirements: Ensure varied phrasing without repetition while maintaining focus
4. Natural Language: Use conversational expressions and avoid jargon
5. Quantity Requirement: Generate 5 distinct questions
6. Language Consistency: Generated questions must match the knowledge's language

## Knowledge:
{knowledge}

## Output
Return a JSON array of questions. Do not include any explanations or formatting outside the JSON.

[
    "Question 1",
    "Question 2",
    "Question 3"
]
"""


class AIGCService:
    def __init__(self):
        # TODO 需要从配置中获取
        self.llm_config = LLMConfig("usq", "qwen2.5-32b-chat-fp8-offline", 0.7, 2000)

    def format_prompt(self, prompt_template: str, request: AigcGenRequest) -> str:
        return prompt_template.format(knowledge=request.model_dump_json())

    async def gen_marketing_copy(
        self, request: AigcGenRequest
    ) -> MarketingCopyGenResponse:
        prompt_template = MARKETING_COPY_GEN_PROMPT_TEMPLATE
        prompt = self.format_prompt(prompt_template, request)
        print(prompt)
        result = await call_llm(self.llm_config, prompt, parse_json=True)
        print(result)
        return MarketingCopyGenResponse(copy_list=result)

    async def gen_faq(self, request: AigcGenRequest) -> FAQGenResponse:
        prompt_template = FAQ_GEN_PROMPT_TEMPLATE
        prompt = self.format_prompt(prompt_template, request)
        result = await call_llm(self.llm_config, prompt, parse_json=True)
        return FAQGenResponse(faq_list=result)

    async def gen_question(self, request: AigcGenRequest) -> QuestionGenResponse:
        prompt_template = QUESTION_GEN_PROMPT_TEMPLATE
        prompt = self.format_prompt(prompt_template, request)
        result = await call_llm(self.llm_config, prompt, parse_json=True)
        return QuestionGenResponse(question_list=result)


def get_aigc_service() -> AIGCService:
    return AIGCService()


aigc_service = AIGCService()
