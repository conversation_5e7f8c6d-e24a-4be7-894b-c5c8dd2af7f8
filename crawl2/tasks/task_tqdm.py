"""将进度保存到数据库，并且和当前 task 关联的 tqdm 进度条"""
import asyncio
import contextvars
from typing import ClassVar, Union

from loguru import logger
from tqdm import tqdm
from tqdm.asyncio import tqdm_asyncio

from crawl2.db import operations


class _TqdmHolder:
    """用于存储当前 celery task id 和 tqdm 实例"""
    def __init__(self, task_id: int):
        self.task_id: int = task_id
        self.tqdm: Union[None, 'CeleryTaskTqdm', 'CeleryTaskTqdmAsync'] = None


class CeleryTaskBoundTqdmMixin:
    _tqdm_holder: ClassVar[contextvars.ContextVar[_TqdmHolder | None]] =\
        contextvars.ContextVar('_tqdm_holder', default=None)

    @classmethod
    def mark_task_started(cls, task_id: int):
        """标记当前任务开始，创建一个 tqdm_holder"""
        holder = _TqdmHolder(task_id)
        token = cls._tqdm_holder.set(holder)
        return token

    @classmethod
    async def mark_task_finished(cls):
        """标记当前任务结束，清除 tqdm_holder"""
        holder = cls._tqdm_holder.get()
        if holder and holder.tqdm is not None:
            await holder.tqdm._db_write_queue.put(None)  # 发送结束信号到数据库写入队列
            await holder.tqdm._db_write_task  # 等待数据库写入任务完成
        cls._tqdm_holder.set(None)

    def __init__(self, *args, **kwargs):
        tqdm_holder = self._tqdm_holder.get()
        # 仅将第一个 tqdm 实例同步到数据库
        if tqdm_holder and tqdm_holder.tqdm is None:
            self._task_id = tqdm_holder.task_id
            tqdm_holder.tqdm = self
            self._db_write_queue = asyncio.Queue()
            self._db_write_task = asyncio.create_task(self._db_write_loop())
        else:
            self._task_id = None
            self._db_write_queue = None
            self._db_write_task = None
        self._last_updated_n = 0
        self._last_updated_total = 0
        super().__init__(*args, **kwargs)

    async def _db_write_loop(self):
        """异步循环，将进度写入数据库"""
        while self._db_write_queue:
            progress = await self._db_write_queue.get()
            if progress is None:
                break
            n, total, desc = progress
            await operations.update_celery_task_progress(self._task_id, n, total, desc)

    def display(self, msg=None, pos=None):
        super().display(msg, pos)
        logger.info(f"progress - {self.desc}: {self.n}/{self.total}")
        if self._task_id is None or self._db_write_queue is None:
            return
        self._db_write_queue.put_nowait((self.n, self.total, self.desc))


class CeleryTaskTqdm(CeleryTaskBoundTqdmMixin, tqdm):
    pass


class CeleryTaskTqdmAsync(CeleryTaskBoundTqdmMixin, tqdm_asyncio):
    pass
