"""
该模块用于将 tasks 中的基础能力封装成工作流.
"""
import celery
import celery.canvas
import fastapi
from celery import chain
from loguru import logger

from crawl2 import schema
from crawl2.db import operations
from crawl2.tasks import (
    site_metadata,
    product_type_aspects,
    product_type_concerns,
    product_type_pv_schema,
    page_blog_metadata,
    collection_metadata,
    product_metadata,
    product_raw_pages,
    product_knowledge,
    selling_point,
    product_concern,
    product_selling_point_translation,
    product_faq_translation,
    product_faq,
    scenario_selling_point,
    product_type_fix,
    product_pv_extraction,
    product_title_keywords,
    product_summary,
    parse_files,
    shopify_knowledge_callbacks,
    page_element_detection,
    align_scenario_selling_point_with_element,
    align_product_selling_point_with_element,
)
from crawl2.tasks.store_knowledge_extraction import (
    store_knowledge,
    collection_knowledge,
    store_knowledge_value,
)
from crawl2.utils import extract_pydantic_model_for_function_params

workflow_factories = []


def register_workflow_factory(fn):
    """
    注册工作流工厂函数.
    """
    workflow_factories.append(fn)
    return fn


@register_workflow_factory
def create_crawl_site_workflow(domain: str, begin_with: str | None = None,
                               shopify_knowledge_task_id: str | None = None):
    """创建整站数据挖掘的 Celery 任务链"""
    tasks_for_domain = [
        site_metadata.crawl_site_metadata,
        product_metadata.crawl_all_products_metadata,
        product_type_fix.fix_product_type_for_all_products,
        product_raw_pages.crawl_all_product_page_markdown,
        product_knowledge.extract_all_product_knowledge,
        product_type_pv_schema.extract_all_product_type_pv_schema,
        product_pv_extraction.extract_product_pv_for_all_products,
        product_pv_extraction.sync_product_pv_to_knowledge_base,
        product_title_keywords.extract_product_title_keywords_for_all_products,
        product_title_keywords.sync_product_title_keywords_to_knowledge_base,
        product_summary.extract_product_summary_for_all_products,
        product_knowledge.sync_all_product_knowledge_to_qdrant,
        product_knowledge.sync_all_product_knowledge_to_knowledge_base,
        product_type_aspects.extract_all_product_type_aspects,
        product_type_concerns.extract_all_product_type_concerns,
        product_type_aspects.expand_batch_product_type_aspects,
        product_type_concerns.expand_all_product_type_concerns,
        selling_point.extract_all_selling_points,
        product_selling_point_translation.translate_all_product_selling_points,
        product_concern.extract_all_product_concern_points,
        product_concern.translate_all_product_concern_points,
        product_faq.extract_and_save_all_product_faqs,
        product_faq_translation.translate_all_product_faqs,
        # 卖点和FAQ需要人审核，这边不自动同步给 shopify-knowledge服务

        # 场景卖点相关任务
        scenario_selling_point.generate_product_type_scenarios,
        scenario_selling_point.generate_all_product_scenarios,
        scenario_selling_point.sync_all_product_type_scenarios,

        page_blog_metadata.crawl_all_pages_metadata,
        page_blog_metadata.crawl_all_blogs_metadata,
        collection_metadata.crawl_all_collections_metadata,
        store_knowledge.extract_all_store_knowledge,
        store_knowledge.sync_all_store_knowledge_to_qdrant,
        store_knowledge.sync_store_knowledge_to_knowledge_base,
        collection_knowledge.extract_all_collection_knowledge,
        collection_knowledge.sync_all_collection_knowledge_to_qdrant,
        store_knowledge_value.extract_store_knowledge_value,
        store_knowledge_value.sync_store_knowledge_value_to_shopify_knowledge,
        # 对 shopify 页面进行商品实体类型标注
        page_element_detection.crawl_domain_raw_web_pages,
        page_element_detection.crawl_domain_page_elements,
        align_product_selling_point_with_element.align_product_selling_points,
        align_scenario_selling_point_with_element.align_scenario_selling_points,
        selling_point.sync_product_selling_points_to_knowledge_base,
        scenario_selling_point.sync_all_scenario_selling_points,
    ]
    if begin_with is not None:
        # 如果指定了开始任务，则跳过该任务之前的所有任务
        start_index = None
        for i, task in enumerate(tasks_for_domain):
            if task.name == begin_with:
                start_index = i
                break
        if start_index is not None:
            tasks_for_domain = tasks_for_domain[start_index:]
            logger.info(f'Starting tasks from {begin_with} for domain {domain}.')
        else:
            logger.warning(f"Task {begin_with} not found in tasks for domain {domain}, starting from the beginning.")
    tasks = [task.si(domain=domain) for task in tasks_for_domain]
    celery_chain = chain(*tasks)
    if shopify_knowledge_task_id:
        celery_chain.link(shopify_knowledge_callbacks.notify_shopify_knowledge_task_succeed.s(
            task_id=shopify_knowledge_task_id))
        celery_chain.link_error(shopify_knowledge_callbacks.notify_shopify_knowledge_task_failed.s(
            task_id=shopify_knowledge_task_id))
        logger.bind(domain=domain).info(f"已为 {domain} 创建数据挖掘流程, 并关联到 Shopify knowledge 任务 {shopify_knowledge_task_id}")
    else:
        logger.bind(domain=domain).info(f"已为 {domain} 创建数据挖掘流程")
    return celery_chain


@register_workflow_factory
def create_scenario_selling_point_workflow(domain: str):
    """
    创建一个为指定站点生成和同步场景卖点的工作流.
    """
    tasks_for_domain = [
        # 前置依赖任务
        site_metadata.crawl_site_metadata,
        product_metadata.crawl_all_products_metadata,
        # 对 shopify 页面进行商品实体类型标注
        page_element_detection.crawl_domain_raw_web_pages,
        page_element_detection.crawl_domain_page_elements,

        product_type_fix.fix_product_type_for_all_products,
        product_raw_pages.crawl_all_product_page_markdown,
        product_knowledge.extract_all_product_knowledge,
        # 抽取场景卖点任务
        scenario_selling_point.generate_product_type_scenarios,
        scenario_selling_point.generate_all_product_scenarios,
        align_scenario_selling_point_with_element.align_scenario_selling_points,
        scenario_selling_point.sync_all_product_type_scenarios,
        scenario_selling_point.sync_all_scenario_selling_points,
    ]
    tasks = [task.si(domain=domain) for task in tasks_for_domain]
    celery_chain = chain(*tasks)
    logger.info(f"Created scenario selling point workflow for domain {domain}: {celery_chain}")
    return celery_chain


@register_workflow_factory
def create_crawl_single_product_workflow(product_url: str):
    """创建一个挖掘指定单个商品所有可能知识的工作流."""
    tasks_for_product = [
        product_metadata.crawl_single_product_metadata,
        product_type_fix.fix_product_type_for_single_product,
        product_raw_pages.crawl_single_product_page_markdown,
        product_knowledge.extract_single_product_knowledge,
        product_pv_extraction.extract_product_pv_for_single_product,
        product_title_keywords.extract_product_title_keywords_for_single_product,
        product_summary.extract_product_summary_for_single_product,
        product_knowledge.sync_single_product_knowledge_to_qdrant,
        product_knowledge.sync_single_product_knowledge_to_knowledge_base,
        selling_point.extract_and_save_single_product_selling_points,
        align_product_selling_point_with_element.align_single_product_selling_point,
        product_selling_point_translation.translate_single_product_selling_point,
        product_concern.extract_and_save_single_product_concern_points,
        product_concern.translate_single_product_concern_points,
        product_faq.extract_and_save_single_product_faqs,
        product_faq_translation.translate_single_product_faq,
        scenario_selling_point.generate_product_scenarios,
        align_scenario_selling_point_with_element.align_single_product_scenario_selling_point,
        scenario_selling_point.sync_single_product_scenario_selling_points,
    ]
    celery_sigs = []
    for task in tasks_for_product:
        if task.has_param("clear_existing"):
            celery_sigs.append(
                task.si(product_url=product_url, clear_existing=True)
            )
        else:
            celery_sigs.append(task.si(product_url=product_url))
    return chain(*celery_sigs)


@register_workflow_factory
def create_file_parse_workflow(task_id: str, knowledge_type: str):
    """
    为指定的文件解析任务创建 workflow.

    :param task_id: 来自 shopify-knowledge 的 task_id
    :param knowledge_type: 知识类型，支持 "STORE" 或 "PRODUCT"
    :return: celery chain
    """
    tasks = [
        parse_files.parse_file_for_task.si(task_id=task_id),
    ]
    if knowledge_type == "STORE":
        tasks.append(store_knowledge.extract_store_knowledge_from_uploaded_file.si(task_id=task_id))
    elif knowledge_type == "PRODUCT":
        tasks.append(product_knowledge.extract_product_knowledge_from_uploaded_file.si(task_id=task_id))
    else:
        raise ValueError(f"Unsupported knowledge_type: {knowledge_type}")

    task_chain = chain(*tasks)
    task_chain.link(shopify_knowledge_callbacks.notify_shopify_knowledge_task_succeed.s(task_id=task_id))
    task_chain.link_error(shopify_knowledge_callbacks.notify_shopify_knowledge_task_failed.s(task_id=task_id))
    logger.info(f"created celery task chain {task_chain} for shopify knowledge task {task_id}")
    return task_chain


@register_workflow_factory
def create_knowledge_sync_workflow(domain: str):
    """
    创建一个将已挖掘数据同步到 shopify-knowledge/qdrant 的工作流.
    """
    tasks = [
        product_pv_extraction.sync_product_pv_to_knowledge_base,
        product_title_keywords.sync_product_title_keywords_to_knowledge_base,
        product_knowledge.sync_all_product_knowledge_to_qdrant,
        product_knowledge.sync_all_product_knowledge_to_knowledge_base,
        selling_point.sync_product_selling_points_to_knowledge_base,
        product_faq.sync_product_faqs_to_knowledge_base,
        product_concern.sync_product_concerns_to_shopify_knowledge,
        scenario_selling_point.sync_all_product_type_scenarios,
        scenario_selling_point.sync_all_scenario_selling_points,
        collection_knowledge.sync_all_collection_knowledge_to_qdrant,
        store_knowledge.sync_all_store_knowledge_to_qdrant,
        store_knowledge.sync_store_knowledge_to_knowledge_base,
        store_knowledge_value.sync_store_knowledge_value_to_shopify_knowledge
    ]
    celery_sigs = [task.si(domain=domain) for task in tasks]
    task_chain = chain(*celery_sigs)
    logger.info(f"Created knowledge sync workflow for domain {domain}: {task_chain}")
    return task_chain


@register_workflow_factory
def create_crawl_page_element_workflow(domain: str, clear_existing: bool = False, sync_to_knowledge: bool = False):
    """ 创建一个爬取页面元素的工作流."""
    celery_sigs = [
        # 前置依赖任务
        site_metadata.crawl_site_metadata.si(domain=domain),
        # 数据任务
        page_element_detection.crawl_domain_raw_web_pages.si(domain=domain, clear_existing=clear_existing),
        page_element_detection.crawl_domain_page_elements.si(domain=domain, clear_existing=clear_existing),
    ]
    if sync_to_knowledge:
        celery_sigs.append(page_element_detection.sync_page_elements_to_knowledge.si(domain=domain))
    task_chain = chain(*celery_sigs)
    logger.info(f"Created page element detection workflow for site {domain}: {task_chain}")
    return task_chain


@register_workflow_factory
def create_refresh_shopify_page_workflow(domain: str):
    tasks = [
        page_element_detection.crawl_domain_raw_web_pages
    ]
    celery_sigs = [task.si(domain=domain, clear_existing=True) for task in tasks]
    task_chain = chain(*celery_sigs)
    logger.info(f"Created refresh shopify page workflow for site {domain}: {task_chain}")
    return task_chain


@register_workflow_factory
def create_sync_page_element_workflow(domain: str):
    """创建一个将同步好的页面元素同步到 shopify-knowledge 的工作流."""
    tasks = [
        page_element_detection.sync_page_elements_to_knowledge
    ]
    celery_sigs = [task.si(domain=domain) for task in tasks]
    task_chain = chain(*celery_sigs)
    logger.info(f"Created page element sync workflow for site {domain}: {task_chain}")
    return task_chain


@register_workflow_factory
def create_dump_page_element_workflow(domain: str, version: str):
    tasks = [
        page_element_detection.dump_page_elements
    ]
    celery_sigs = [task.si(domain=domain, version=version) for task in tasks]
    task_chain = chain(*celery_sigs)
    logger.info(f"Created page element dump workflow for site {domain}: {task_chain}")
    return task_chain


@register_workflow_factory
def create_load_page_element_workflow(domain: str, version: str):
    tasks = [
        page_element_detection.load_page_elements,
    ]
    celery_sigs = [task.si(domain=domain, version=version) for task in tasks]
    task_chain = chain(*celery_sigs)
    logger.info(f"Created page element load workflow for site {domain}: {task_chain}")
    return task_chain


async def trigger_workflow(name: str, celery_chain: celery.chain):
    workflow_id = await operations.create_workflow(name, celery_chain)
    tasks: list[celery.canvas.Signature] = celery_chain.tasks
    for task in tasks:
        headers = task.options.setdefault('headers', {})
        headers['workflow_id'] = workflow_id
        headers['workflow_name'] = name
    celery_chain.apply_async()
    logger.bind(workflow_id=str(workflow_id)).info(f"Triggered workflow {name} with ID {workflow_id}: {celery_chain}")
    return await operations.get_workflow(workflow_id)


async def retry_workflow(workflow_id: int, begin_task_name: str):
    workflow = await operations.get_workflow(workflow_id)
    if not workflow:
        raise ValueError(f"Workflow {workflow_id} not found")
    task_index = None
    for i, task_sig in enumerate(workflow.signature.tasks):
        if task_sig.name == begin_task_name:
            task_index = i
            break
    if task_index is None:
        raise ValueError(f"Task {begin_task_name} not found in workflow {workflow_id}")

    # Create truncated workflow signature (from the retried task to the end)
    truncated_tasks = workflow.signature.tasks[task_index:]
    if not truncated_tasks:
        raise ValueError(f"No tasks remaining after {begin_task_name} in workflow")

    # Convert to celery chain format
    tasks = []
    for t in truncated_tasks:
        tasks.append({
            'task': t.name,
            'args': (),
            'kwargs': t.kwargs or {},
            'options': {},
            'immutable': True
        })
    # Build celery chain from signature dict
    celery_chain = celery.chain.from_dict({'kwargs': {'tasks': tasks}, 'options': workflow.signature.options})

    # IMPORTANT: do NOT create a new workflow record.
    # Reuse the existing workflow by attaching headers so newly created celery tasks
    # are associated with the original workflow.
    for sig in celery_chain.tasks:
        headers = sig.options.setdefault('headers', {})
        headers['workflow_id'] = workflow_id
        headers['workflow_name'] = workflow.name

    logger.info(f"Retrying workflow {workflow_id} ({workflow.name}) from task {begin_task_name} without creating a new workflow record")  # NOQA
    celery_chain.apply_async()

    # Return the current workflow detail so caller can observe live updates
    return await operations.get_workflow(workflow_id)


def _create_endpoint_to_trigger_workflow_via_factory(factory):
    """
    使用指定的 workflow 工厂函数(workflow 模板) 来触发一个工作流.
    """
    param_model = extract_pydantic_model_for_function_params(factory)

    async def endpoint(params: param_model) -> schema.CeleryWorkflowDetail:
        params_dict = params.model_dump()
        chain = factory(**params_dict)
        params_str = ', '.join(f'{k}={v}' for k, v in params_dict.items())
        workflow_name = f'{factory.__name__}({params_str})'
        if len(workflow_name) > 512:
            workflow_name = workflow_name[:509] + "..."
        resp = await trigger_workflow(workflow_name, chain)
        return resp

    endpoint.__name__ = factory.__name__
    endpoint.__doc__ = factory.__doc__
    return endpoint


def _create_endpoint_to_create_workflow_via_factory(factory):
    """
    使用指定的 workflow 工厂函数(workflow 模板) 来生成一个工作流定义.
    """
    param_model = extract_pydantic_model_for_function_params(factory)

    async def endpoint(params: param_model) -> schema.CeleryWorkflowDefinition:
        params_dict = params.model_dump()
        chain = factory(**params_dict)
        params_str = ', '.join(f'{k}={v}' for k, v in params_dict.items())
        workflow_name = f'{factory.__name__}({params_str})'
        if len(workflow_name) > 512:
            workflow_name = workflow_name[:509] + "..."
        res = schema.CeleryWorkflowDefinition(
            name=workflow_name,
            tasks=[schema.CeleryTaskSignature(name=task.name, kwargs=task.kwargs) for task in chain.tasks],
        )
        return res

    endpoint.__name__ = factory.__name__
    endpoint.__doc__ = factory.__doc__
    return endpoint


def create_fast_api_router() -> fastapi.APIRouter:
    router = fastapi.APIRouter(prefix="/workflows", tags=["workflows"])
    factories = []
    for factory in workflow_factories:
        param_model = extract_pydantic_model_for_function_params(factory)
        factories.append(schema.CeleryWorkflowFactory(
            name=factory.__name__,
            description=factory.__doc__,
            params=param_model.model_json_schema(),
        ))
        router.add_api_route(
            path=f"/factory/{factory.__name__}",
            endpoint=_create_endpoint_to_trigger_workflow_via_factory(factory),
            methods=["POST"],
        )
        router.add_api_route(
            path=f"/factory/{factory.__name__}",
            endpoint=_create_endpoint_to_create_workflow_via_factory(factory),
            methods=["PUT"],
        )

    @router.get("/factories")
    def get_workflow_factories() -> list[schema.CeleryWorkflowFactory]:
        """
        获取所有已注册的工作流工厂函数名称和对应的参数 schema.
        """
        return factories

    @router.post("")
    async def create_workflow(req: schema.CeleryWorkflowDefinition) -> schema.CeleryWorkflowDetail:
        """
        使用指定的工作流名称和任务列表创建并触发一个工作流.
        """
        req.name = req.name or "Unnamed Workflow"
        tasks = []
        for t in req.tasks:
            tasks.append({'task': t.name, 'args': (), 'kwargs': t.kwargs or {}, 'options': {}, 'immutable': True})
        celery_chain = celery.chain.from_dict({'kwargs': {'tasks': tasks}, 'options': {}})
        return await trigger_workflow(req.name, celery_chain)

    @router.get("/runs")
    async def list_workflows(page: int = 1, size: int = 20, search: str | None = None) -> schema.PaginatedResponse[schema.CeleryWorkflowDetail]:
        """
        获取所有工作流列表，支持分页和搜索.
        """
        return await operations.list_workflows(page=page, size=size, search=search)

    @router.get("/runs/{workflow_id}")
    async def get_workflow(workflow_id: int) -> schema.CeleryWorkflowDetail:
        """
        获取指定 ID 的工作流详情.
        """
        workflow = await operations.get_workflow(workflow_id)
        if not workflow:
            raise fastapi.HTTPException(status_code=404, detail=f"Workflow with ID {workflow_id} not found.")
        return workflow

    return router
