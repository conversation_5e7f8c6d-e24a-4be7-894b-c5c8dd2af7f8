<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-2xl mx-4 max-h-[80vh] overflow-y-auto">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ title }}
        </h3>
        <button
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X class="h-5 w-5" />
        </button>
      </div>
      
      <div class="mb-6">
        <p class="text-gray-600 mb-4">
          {{ description }}
        </p>
        
        <!-- 进度显示 -->
        <div v-if="isProcessing || progress.current > 0" class="mb-4">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">取消进度</span>
            <span class="text-sm text-gray-500">{{ progress.current }} / {{ progress.total }}</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
            <div 
              class="bg-red-500 h-2 rounded-full transition-all duration-300" 
              :style="{ width: `${(progress.current / progress.total) * 100}%` }"
            ></div>
          </div>
          
          <!-- 当前处理的任务 -->
          <div v-if="isProcessing && progress.current > 0" class="text-sm text-gray-600">
            <span class="font-medium">正在处理：</span>
            <span class="text-gray-800">{{ getCurrentProcessingInfo() }}</span>
          </div>
        </div>
        
        <!-- 任务状态列表 -->
        <div v-if="tasks.length > 0" class="max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-3">
          <h4 class="text-sm font-medium text-gray-700 mb-2">
            {{ isProcessing || progress.current > 0 ? '任务状态：' : '待取消任务：' }}
          </h4>
          <div class="space-y-3">
            <div 
              v-for="task in tasks" 
              :key="task.id"
              class="flex items-start gap-3 text-sm"
            >
              <div class="flex-1 min-w-0">
                <div class="text-gray-600 break-words leading-relaxed">
                  {{ task.displayInfo }}
                </div>
              </div>
              <span 
                :class="getTaskStatusClass(task.id)"
                class="font-medium flex-shrink-0 whitespace-nowrap"
              >
                {{ getTaskStatusText(task.id) }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="flex justify-end gap-3">
        <button
          v-if="!isProcessing && progress.current === 0"
          @click="handleClose"
          class="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
        >
          取消
        </button>
        <button
          v-if="!isProcessing && progress.current === 0"
          @click="handleConfirm"
          class="px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors"
        >
          确认取消
        </button>
        <button
          v-if="!isProcessing && progress.current > 0"
          @click="handleClose"
          class="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
        >
          完成
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { X } from 'lucide-vue-next'
import { api } from '@/api/api.js'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '取消确认'
  },
  description: {
    type: String,
    default: '确定要取消选中的任务吗？此操作不可撤销。'
  },
  tasks: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['close', 'cancel-completed'])

// 内部状态管理
const isProcessing = ref(false)
const progress = ref({ current: 0, total: 0 })
const results = ref([])

// 计算属性
const progressComputed = computed(() => progress.value)

// 处理关闭对话框
const handleClose = () => {
  emit('close')
}

// 处理确认取消
const handleConfirm = async () => {
  if (isProcessing.value || props.tasks.length === 0) return
  
  isProcessing.value = true
  progress.value = { current: 0, total: props.tasks.length }
  results.value = []
  
  try {
    // 逐个取消任务
    for (let i = 0; i < props.tasks.length; i++) {
      const task = props.tasks[i]
      progress.value.current = i + 1
      
      try {
        // 调用取消API
        await api.post(`/api/v1/tasks/runs/${task.id}/cancel`)
        
        results.value.push({
          taskId: task.id,
          taskInfo: task.displayInfo,
          success: true,
          message: '取消成功'
        })
      } catch (error) {
        const errorMessage = error.response?.data?.detail || error.message || '未知错误'
        results.value.push({
          taskId: task.id,
          taskInfo: task.displayInfo,
          success: false,
          message: `取消失败: ${errorMessage}`
        })
      }
    }
  } finally {
    isProcessing.value = false
  }
  
  // 通知父组件取消完成
  emit('cancel-completed', {
    results: results.value,
    progress: progress.value
  })
}

// 获取当前处理的任务信息
const getCurrentProcessingInfo = () => {
  if (progress.value.current === 0) return ''
  
  const currentTask = props.tasks[progress.value.current - 1]
  return currentTask ? currentTask.displayInfo : ''
}

// 获取任务状态样式类
const getTaskStatusClass = (taskId) => {
  const result = results.value.find(r => r.taskId === taskId)
  if (!result) {
    // 还未处理
    if (progress.value.current === 0) {
      return 'text-gray-400' // 等待取消
    } else {
      const taskIndex = props.tasks.findIndex(t => t.id === taskId)
      if (taskIndex < progress.value.current) {
        return 'text-blue-600' // 正在处理中
      } else {
        return 'text-gray-400' // 等待处理
      }
    }
  }
  
  // 已处理完成
  return result.success ? 'text-green-600' : 'text-red-600'
}

// 获取任务状态文本
const getTaskStatusText = (taskId) => {
  const result = results.value.find(r => r.taskId === taskId)
  if (!result) {
    // 还未处理
    if (progress.value.current === 0) {
      return '等待取消'
    } else {
      const taskIndex = props.tasks.findIndex(t => t.id === taskId)
      if (taskIndex < progress.value.current) {
        return '处理中...' // 正在处理
      } else {
        return '等待处理'
      }
    }
  }
  
  // 已处理完成
  return result.success ? '取消成功' : '取消失败'
}
</script>
