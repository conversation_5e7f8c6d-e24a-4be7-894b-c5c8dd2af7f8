from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "tqdm_record" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "relate_id" INT NOT NULL,
    "relate_type" VARCHAR(50) NOT NULL,
    "total" INT NOT NULL,
    "n" INT NOT NULL DEFAULT 0,
    "desc" VARCHAR(255),
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS "idx_tqdm_record_relate__32fb90" ON "tqdm_record" ("relate_id", "relate_type");
COMMENT ON COLUMN "tqdm_record"."relate_id" IS '关联的任务 ID, 如 Celery 任务 ID 或工作流 ID';
COMMENT ON COLUMN "tqdm_record"."relate_type" IS '关联类型, 如 ''celery_task'', ''celery_workflow'' 等';
COMMENT ON COLUMN "tqdm_record"."total" IS '总数';
COMMENT ON COLUMN "tqdm_record"."n" IS '当前进度';
COMMENT ON COLUMN "tqdm_record"."desc" IS '描述';
COMMENT ON COLUMN "tqdm_record"."created_at" IS '创建时间';
COMMENT ON COLUMN "tqdm_record"."updated_at" IS '最后更新时间';
COMMENT ON TABLE "tqdm_record" IS 'Tqdm 进度条记录.';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "tqdm_record";"""
