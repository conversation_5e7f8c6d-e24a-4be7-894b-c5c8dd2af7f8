def create_page_element_detection_flow():
    """创建页面元素检测的主流程。

    params:
        html: 原始页面
        product_id: 商品页 product id
        tag: 需要进行识别的 BeautifySoup 节点
        llm_conf: 大模型参数
    shared:
        page_elements: 识别出的商品实体列表
    """
    # 1. 定义处理单个节点的子流程
    from typing import cast
    from bs4 import Tag
    from pocketflow import AsyncParallelBatchFlow
    from crawl2.tasks.page_element.utils import tag_need_ignore
    from crawl2.tasks.page_element.flow.ner import create_ner_flow
    from crawl2.tasks.page_element import nodes

    class PageElementDetectionFlow(AsyncParallelBatchFlow):
        async def prep_async(self, shared):
            parent = cast(Tag, self.params["tag"])

            # 过滤掉不需要处理的子节点
            return [
                {"tag": child}
                for child in parent.children if not tag_need_ignore(child)
            ]

    ner_flow = create_ner_flow()
    end = nodes.End()
    # 2. 如果 NER 判断需要递归，则进入 BatchFlow
    children_flow = PageElementDetectionFlow(start=ner_flow)
    ner_flow - "continue" >> children_flow
    ner_flow - "end" >> end
    return children_flow
