<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { api } from '@/api/api.js'
import Navigation from '../components/common/Navigation.vue'
import TaskItem from '../components/task/TaskItem.vue'
import { Search } from 'lucide-vue-next'

const router = useRouter()
const workflows = ref([])
const loading = ref(true)
const error = ref(null)
const searchKeyword = ref('')

// 分页相关
const page = ref(1)
const pageSize = ref(20)
const total = ref(0)
const totalPages = ref(1)

const statusColors = {
  'PENDING': '#ffc107',
  'STARTED': '#17a2b8',
  'SUCCESS': '#28a745',
  'FAILURE': '#dc3545',
  'RETRY': '#fd7e14'
}

const formatStatus = (status) => {
  const statusMap = {
    'PENDING': '等待中',
    'STARTED': '进行中',
    'SUCCESS': '已完成',
    'FAILURE': '失败',
    'FAILED': '失败'
  }
  return statusMap[status] || status
}

// 计算工作流持续时间
const getWorkflowDuration = (workflow) => {
  if (!workflow.created_at) return ''
  
  const startTime = new Date(workflow.created_at)
  const endTime = workflow.finished_at ? new Date(workflow.finished_at) : new Date()
  const duration = endTime.getTime() - startTime.getTime()
  
  const hours = Math.floor(duration / (1000 * 60 * 60))
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟`
  } else {
    return '小于1分钟'
  }
}

// 获取最近的任务
const getLatestTask = (workflow) => {
  if (!workflow.tasks || workflow.tasks.length === 0) return null
  
  // 按创建时间排序，返回最新的任务
  return workflow.tasks.sort((a, b) => 
    new Date(b.created_at) - new Date(a.created_at)
  )[0]
}

// 获取已完成或正在执行的任务数量
const getCompletedOrRunningTaskCount = (workflow) => {
  if (!workflow.tasks || workflow.tasks.length === 0) return 0
  
  return workflow.tasks.filter(task => 
    ['STARTED', 'SUCCESS', 'FAILURE', 'FAILED'].includes(task.status)
  ).length
}

const fetchWorkflows = async () => {
  try {
    loading.value = true
    const params = {
      page: page.value,
      size: pageSize.value,
    }
    if (searchKeyword.value.trim()) {
      params.search = searchKeyword.value.trim()
    }
    const response = await api.get('/api/v1/workflows/runs', { params })
    const data = response.data
    workflows.value = data.items
    total.value = data.total
    totalPages.value = data.total_pages
  } catch (err) {
    error.value = err.message
    console.error('Error fetching workflows:', err)
  } finally {
    loading.value = false
  }
}

// 分页切换
const handlePageChange = (newPage) => {
  if (newPage !== page.value) {
    page.value = newPage
    fetchWorkflows()
    // 自动滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
}

const handlePageSizeChange = (newSize) => {
  if (newSize !== pageSize.value) {
    pageSize.value = newSize
    page.value = 1
    fetchWorkflows()
    // 自动滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
}

// 快速跳转到指定页
const jumpToPage = () => {
  const input = document.getElementById('pageInput')
  const targetPage = parseInt(input.value)
  if (targetPage && targetPage >= 1 && targetPage <= totalPages.value) {
    handlePageChange(targetPage)
  }
}

// 搜索处理
const handleSearch = () => {
  page.value = 1
  fetchWorkflows()
}

const viewWorkflowDetails = (workflowId) => {
  router.push(`/workflows/${workflowId}`)
}

// 处理任务取消成功事件
const handleTaskCanceled = (taskId) => {
  // 刷新工作流列表以更新任务状态
  fetchWorkflows()
}

onMounted(() => {
  fetchWorkflows()
})
</script>

<template>
  <div class="flex flex-col h-screen w-full pt-0">
    <Navigation
        :breadcrumbs="[
        { text: 'Sites', path: '/' },
        { text: 'Workflow History' }
      ]"
    />

    <div class="sticky flex justify-center items-center p-4 px-8 w-full bg-white shadow top-16 z-10">
      <div class="relative w-[400px]">
        <input
          v-model="searchKeyword"
          type="text"
          placeholder="Search by workflow name or ID..."
          class="w-full p-3 px-10 border border-gray-300 rounded-lg text-sm bg-gray-50 transition-all duration-200 focus:outline-none focus:border-blue-500 focus:bg-white focus:shadow-md"
          @keyup.enter="handleSearch"
        />
        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none">
          <Search size="16" />
        </div>
      </div>
      <button 
        @click="handleSearch"
        class="ml-4 px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
      >
        搜索
      </button>
    </div>

    <div class="flex-1 p-4 px-8 flex flex-col w-full bg-gray-50 pb-24">
      <div v-if="loading" class="flex justify-center items-center h-full text-gray-600 bg-white rounded-lg shadow">
        Loading workflow history...
      </div>
      <div v-else-if="error" class="flex justify-center items-center h-full text-red-500 bg-white rounded-lg shadow">
        Error: {{ error }}
      </div>
      <div v-else-if="workflows.length" class="flex flex-col gap-4">
        <div 
          v-for="workflow in workflows" 
          :key="workflow.id" 
          class="bg-white rounded-lg border-2 border-gray-200 shadow-sm cursor-pointer transition-all duration-200 hover:border-blue-300 hover:shadow-md hover:-translate-y-0.5"
          @click="viewWorkflowDetails(workflow.id)"
        >
          <!-- 第一行：工作流摘要信息 -->
          <div class="p-4 border-b border-gray-200">
            <div class="flex items-center justify-between gap-4">
              <!-- 左侧：状态、时间和任务数 -->
              <div class="flex items-center gap-6 flex-1 min-w-0">
                <div class="flex-shrink-0">
                  <span 
                    class="inline-block px-3 py-1 rounded-full text-white text-sm font-medium uppercase"
                    :style="{ backgroundColor: statusColors[workflow.status] || '#6c757d' }"
                  >
                    {{ formatStatus(workflow.status) }}
                  </span>
                </div>
                
                <div class="flex flex-col gap-1 text-xs text-gray-500">
                  <span>创建: {{ new Date(workflow.created_at).toLocaleString() }}</span>
                  <span v-if="workflow.finished_at" class="text-green-600">
                    完成: {{ new Date(workflow.finished_at).toLocaleString() }}
                  </span>
                  <span v-if="getWorkflowDuration(workflow)" class="text-blue-600 font-medium">
                    耗时: {{ getWorkflowDuration(workflow) }}
                  </span>
                </div>
                
                <div class="flex-shrink-0">
                  <span class="text-sm text-gray-500">任务:</span>
                  <span class="ml-1 text-sm text-gray-700">
                    {{ getCompletedOrRunningTaskCount(workflow) }}/{{ workflow.tasks?.length || 0 }}
                  </span>
                </div>
              </div>
              
              <!-- 右侧：ID和名称 -->
              <div class="flex items-center gap-6 flex-shrink-0">
                <div class="flex-shrink-0 min-w-0">
                  <span class="text-sm text-gray-500">名称:</span>
                  <span class="ml-1 text-sm font-semibold text-gray-800 truncate max-w-48" :title="workflow.name">
                    {{ workflow.name }}
                  </span>
                </div>
                
                <div class="flex-shrink-0">
                  <span class="text-sm text-gray-500">ID:</span>
                  <span class="ml-1 text-sm font-mono text-gray-700">{{ workflow.id }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 第二行：最近的任务信息 -->
          <div v-if="getLatestTask(workflow)" class="p-0">
            <div class="text-xs text-gray-500 px-4 py-2 bg-gray-50 border-b border-gray-200 font-medium">
              📋 最近任务
            </div>
            <TaskItem 
              :task="getLatestTask(workflow)" 
              :can-select="false"
              :can-search="false"
              @task-canceled="handleTaskCanceled"
              @toggle-selection="() => {}"
              @search-task-name="() => {}"
            />
          </div>
          
          <!-- 如果没有任务，显示提示 -->
          <div v-else class="p-4 text-center text-gray-500 text-sm">
            暂无任务信息
          </div>
        </div>
      </div>
      <div v-else class="flex justify-center items-center h-full text-gray-600 bg-white rounded-lg shadow">
        No workflow history found.
      </div>
    </div>

    <!-- 固定分页控件 -->
    <div v-if="total > 0" class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
      <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="flex justify-center items-center gap-4 text-sm text-gray-600">
          <span>共 {{ total }} 个工作流，页码：</span>
          <button :disabled="page === 1" @click="handlePageChange(page - 1)" class="px-3 py-2 rounded border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50" >上一页</button>
          <span class="font-medium">{{ page }} / {{ totalPages }}</span>
          <button :disabled="page === totalPages" @click="handlePageChange(page + 1)" class="px-3 py-2 rounded border disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">下一页</button>
          <span>跳转到</span>
          <input 
            id="pageInput"
            type="number" 
            min="1" 
            :max="totalPages" 
            class="w-16 px-2 py-2 border rounded text-center"
            @keyup.enter="jumpToPage"
          />
          <button @click="jumpToPage" class="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">跳转</button>
          <span>每页</span>
          <select v-model.number="pageSize" @change="handlePageSizeChange($event.target.value)" class="border rounded px-2 py-2">
            <option :value="10">10</option>
            <option :value="20">20</option>
            <option :value="50">50</option>
            <option :value="100">100</option>
          </select>
          <span>条</span>
        </div>
      </div>
    </div>
  </div>
</template> 