import asyncio

from sentry_sdk.integrations.celery import CeleryIntegration
import sentry_sdk; sentry_sdk.init(disabled_integrations=[CeleryIntegration])  # NOQA

from celery import Celery
from celery.signals import worker_process_init

from crawl2.config import settings


@worker_process_init.connect
def setup_asyncio_ev_loop_and_tortoise(*args, **kwargs):
    from tortoise import Tortoise
    from crawl2.logger import setup
    from leyan_grpc.consul_client import consul_client

    print(consul_client.http.__dict__)
    setup()
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError as e:
        if "no current event loop in thread" in str(e):
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        else:
            raise e
    if not getattr(loop, '_is_tortoise_inited', False):
        loop.run_until_complete(Tortoise.init(db_url=settings.DATABASE_URL, modules={"models": ['crawl2.db.models']}))


app = Celery('shop_pupil', broker=settings.REDIS_URL, backend=settings.REDIS_URL)
app.autodiscover_tasks(['crawl2'])
from crawl2.tasks.celery_wrap import celery_tasks

task_routes = {}

for task in celery_tasks.values():
    if task.func.__module__.startswith('crawl2.tasks.chat_plus_ai'):
        task_routes[task.name] = {'queue': 'lechat'}

# 配置 task routing，将指定的任务路由到 lechat queue
app.conf.task_routes = task_routes
