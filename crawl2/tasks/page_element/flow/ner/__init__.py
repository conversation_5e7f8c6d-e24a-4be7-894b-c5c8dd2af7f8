def create_ner_flow():
    """创建处理单个DOM节点的流程

    params:
        html: 原始页面
        product_id: 商品页 product id
        tag: 需要进行识别的 BeautifySoup 节点
        llm_conf: 大模型参数

    intermediate:
        element_tag: (optional) 命中的商品实体类型
        selling_point_summary: (optional) 商品卖点类型专用

    shared:
        page_elements: 识别出的商品实体列表
    """
    from pocketflow import AsyncFlow
    from crawl2.tasks.page_element import nodes

    ner = nodes.NER()
    to_main_image = nodes.ToMainImagePageElement()
    selling_point_indicator = nodes.SellingPointIndicator()
    selling_point_summary = nodes.SellingPointSummary()
    to_page_element = nodes.ToPageElement()
    need_recursive = nodes.NeedRecursive()
    end = nodes.End()

    # 从 NER 节点根据其输出字符串进行分支
    ner - "selling_point_indicator" >> selling_point_indicator
    ner - "main_image" >> to_main_image
    ner - "default" >> to_page_element
    ner - "continue" >> need_recursive

    # 商品卖点分支
    selling_point_indicator - "product_features" >> to_page_element
    selling_point_indicator - "selling_point" >> selling_point_summary
    selling_point_summary - "default" >> to_page_element
    selling_point_summary - "end" >> end

    to_page_element >> end

    # 单个节点的流程从 NER 开始
    class NER(AsyncFlow): pass

    flow = NER(start=ner)
    return flow
