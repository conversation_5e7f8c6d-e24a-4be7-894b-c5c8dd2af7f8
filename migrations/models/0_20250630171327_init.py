from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "celery_task" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "task_id" VARCHAR(255) NOT NULL UNIQUE,
    "name" VARCHAR(255) NOT NULL,
    "status" VARCHAR(255) NOT NULL,
    "params" JSONB NOT NULL,
    "result" TEXT,
    "error" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "finished_at" TIMESTAMPTZ
);
CREATE INDEX IF NOT EXISTS "idx_celery_task_task_id_8ecd07" ON "celery_task" ("task_id", "name", "status");
COMMENT ON COLUMN "celery_task"."task_id" IS 'Celery 任务 ID';
COMMENT ON COLUMN "celery_task"."name" IS 'Celery 任务名称';
COMMENT ON COLUMN "celery_task"."status" IS '任务状态';
COMMENT ON COLUMN "celery_task"."params" IS '任务参数';
COMMENT ON COLUMN "celery_task"."result" IS '任务结果';
COMMENT ON COLUMN "celery_task"."error" IS '错误信息';
COMMENT ON COLUMN "celery_task"."created_at" IS '创建时间';
COMMENT ON COLUMN "celery_task"."finished_at" IS '完成时间';
COMMENT ON TABLE "celery_task" IS 'Celery 任务记录.';
CREATE TABLE IF NOT EXISTS "data_version" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "prev_id" INT,
    "value" JSONB NOT NULL,
    "data_type" VARCHAR(255) NOT NULL,
    "data_id" INT NOT NULL,
    "comment" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS "idx_data_versio_data_ty_07f9ce" ON "data_version" ("data_type", "data_id", "created_at");
COMMENT ON COLUMN "data_version"."prev_id" IS '上一个版本的ID';
COMMENT ON COLUMN "data_version"."value" IS '当前版本的值';
COMMENT ON COLUMN "data_version"."data_type" IS '数据类型，如 product_selling_point';
COMMENT ON COLUMN "data_version"."data_id" IS '对应数据的ID';
COMMENT ON COLUMN "data_version"."comment" IS '用户输入的标注原因';
COMMENT ON COLUMN "data_version"."created_at" IS '版本创建时间';
COMMENT ON TABLE "data_version" IS '数据版本表，用于追踪标注数据的版本历史';
CREATE TABLE IF NOT EXISTS "llm_record" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "prompt" TEXT NOT NULL,
    "model" VARCHAR(255) NOT NULL,
    "response" TEXT NOT NULL,
    "error" TEXT
);
COMMENT ON COLUMN "llm_record"."created_at" IS '创建时间';
COMMENT ON COLUMN "llm_record"."prompt" IS 'LLM 提示';
COMMENT ON COLUMN "llm_record"."model" IS 'LLM 模型';
COMMENT ON COLUMN "llm_record"."response" IS 'LLM 响应';
COMMENT ON COLUMN "llm_record"."error" IS 'LLM 错误信息';
COMMENT ON TABLE "llm_record" IS 'LLM 调用记录.';
CREATE TABLE IF NOT EXISTS "shopify_site" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "domain" VARCHAR(255) NOT NULL UNIQUE,
    "shopify_domain" VARCHAR(255) NOT NULL DEFAULT '',
    "product_urls" JSONB NOT NULL,
    "collection_urls" JSONB NOT NULL
);
CREATE INDEX IF NOT EXISTS "idx_shopify_sit_domain_a65139" ON "shopify_site" ("domain");
COMMENT ON COLUMN "shopify_site"."created_at" IS '创建时间';
COMMENT ON COLUMN "shopify_site"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "shopify_site"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "shopify_site"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "shopify_site"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "shopify_site"."domain" IS 'shopify 站点 domain, 仅包含域名中不含 www 的部分';
COMMENT ON COLUMN "shopify_site"."shopify_domain" IS '以 myshopify.com 结尾的完整 shopify 站点域名';
COMMENT ON COLUMN "shopify_site"."product_urls" IS '产品页面的 URL 列表';
COMMENT ON COLUMN "shopify_site"."collection_urls" IS '产品collection页面的 URL 列表';
COMMENT ON TABLE "shopify_site" IS '和一个 Shopify 站点相关, 但和某个具体商品直接关联的元数据.';
CREATE TABLE IF NOT EXISTS "collection_metadata" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "url" VARCHAR(255) NOT NULL UNIQUE,
    "collection_id" VARCHAR(255) NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "product_ids" JSONB NOT NULL,
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_collection__url_6761df" ON "collection_metadata" ("url", "collection_id", "site_id");
COMMENT ON COLUMN "collection_metadata"."created_at" IS '创建时间';
COMMENT ON COLUMN "collection_metadata"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "collection_metadata"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "collection_metadata"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "collection_metadata"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "collection_metadata"."url" IS '产品collection页面的 URL';
COMMENT ON COLUMN "collection_metadata"."collection_id" IS '产品collection ID, 与 Shopify 站点的产品collection ID 一致, 与表中的 id 字段无关';
COMMENT ON COLUMN "collection_metadata"."title" IS '产品collection标题';
COMMENT ON COLUMN "collection_metadata"."description" IS '产品collection描述';
COMMENT ON COLUMN "collection_metadata"."product_ids" IS '产品collection中的产品 ID 列表';
COMMENT ON TABLE "collection_metadata" IS '产品collection页面的原始内容，用于后续的知识抽取和数据挖掘, 注意不包含用 AI 抽取的二级信息.';
CREATE TABLE IF NOT EXISTS "product_metadata" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "url" VARCHAR(255) NOT NULL UNIQUE,
    "product_id" VARCHAR(255) NOT NULL,
    "product_type" VARCHAR(255) NOT NULL,
    "title" TEXT NOT NULL,
    "price" VARCHAR(255) NOT NULL,
    "tags" TEXT NOT NULL,
    "vendor" VARCHAR(255) NOT NULL DEFAULT '',
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_met_url_a987ed" ON "product_metadata" ("url", "product_id", "site_id");
COMMENT ON COLUMN "product_metadata"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_metadata"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_metadata"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_metadata"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_metadata"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_metadata"."url" IS '产品页面的 URL';
COMMENT ON COLUMN "product_metadata"."product_id" IS '产品 ID, 与 Shopify 站点的产品 ID 一致, 与表中的 id 字段无关';
COMMENT ON COLUMN "product_metadata"."product_type" IS '产品类型, 由 Shopify 站点提供';
COMMENT ON COLUMN "product_metadata"."title" IS '产品标题';
COMMENT ON COLUMN "product_metadata"."price" IS '产品价格, 我们对具体金额暂时不关心，所以用字符串表示';
COMMENT ON COLUMN "product_metadata"."tags" IS '产品标签, 用逗号分隔';
COMMENT ON COLUMN "product_metadata"."vendor" IS '产品供应商';
COMMENT ON TABLE "product_metadata" IS '产品页面的原始内容，用于后续的知识抽取和数据挖掘, 注意不包含用 AI 抽取的二级信息.';
CREATE TABLE IF NOT EXISTS "product_faqs" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "faqs" JSONB NOT NULL,
    "status" VARCHAR(8) NOT NULL DEFAULT 'fresh',
    "last_edited_at" TIMESTAMPTZ,
    "llm_record_id" INT NOT NULL REFERENCES "llm_record" ("id") ON DELETE CASCADE,
    "product_id" INT NOT NULL REFERENCES "product_metadata" ("id") ON DELETE CASCADE,
    "version_id" INT REFERENCES "data_version" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_faq_product_78cea2" ON "product_faqs" ("product_id", "llm_record_id", "status");
COMMENT ON COLUMN "product_faqs"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_faqs"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_faqs"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_faqs"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_faqs"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_faqs"."faqs" IS '常见问题和解答';
COMMENT ON COLUMN "product_faqs"."status" IS '标注状态';
COMMENT ON COLUMN "product_faqs"."last_edited_at" IS '上次标注时间';
COMMENT ON COLUMN "product_faqs"."version_id" IS '当前版本';
COMMENT ON TABLE "product_faqs" IS '产品FAQ';
CREATE TABLE IF NOT EXISTS "product_knowledge" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "markdown_content" TEXT NOT NULL,
    "documents" JSONB NOT NULL,
    "metadata_id" INT NOT NULL REFERENCES "product_metadata" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_kno_metadat_939d2d" ON "product_knowledge" ("metadata_id");
COMMENT ON COLUMN "product_knowledge"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_knowledge"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_knowledge"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_knowledge"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_knowledge"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_knowledge"."markdown_content" IS '产品页面的知识抽取结果';
COMMENT ON COLUMN "product_knowledge"."documents" IS '产品页面的知识抽取结果的 JSON 对象';
COMMENT ON TABLE "product_knowledge" IS '产品页面的知识抽取结果';
CREATE TABLE IF NOT EXISTS "product_raw_page" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "html_content" TEXT NOT NULL,
    "markdown_content" TEXT NOT NULL,
    "extracted_links" JSONB NOT NULL,
    "json_objects" JSONB NOT NULL,
    "metadata_id" INT NOT NULL REFERENCES "product_metadata" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_raw_metadat_9b9106" ON "product_raw_page" ("metadata_id");
COMMENT ON COLUMN "product_raw_page"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_raw_page"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_raw_page"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_raw_page"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_raw_page"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_raw_page"."html_content" IS '产品页面的原始 HTML 内容';
COMMENT ON COLUMN "product_raw_page"."markdown_content" IS '产品页面的原始 Markdown 描述';
COMMENT ON COLUMN "product_raw_page"."extracted_links" IS '产品页面上提取的链接';
COMMENT ON COLUMN "product_raw_page"."json_objects" IS '产品页面上提取的对象';
COMMENT ON TABLE "product_raw_page" IS '产品页面的原始内容，用于后续的知识抽取和数据挖掘, 注意不包含用 AI 抽取的二级信息.';
CREATE TABLE IF NOT EXISTS "product_search_enhancement" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "new_title" VARCHAR(512) NOT NULL,
    "new_description" TEXT NOT NULL,
    "new_product_type" VARCHAR(255) NOT NULL,
    "filterable_attributes" JSONB NOT NULL,
    "llm_record_id" INT NOT NULL REFERENCES "llm_record" ("id") ON DELETE CASCADE,
    "product_id" INT NOT NULL REFERENCES "product_metadata" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_sea_product_de7ec6" ON "product_search_enhancement" ("product_id", "llm_record_id");
COMMENT ON COLUMN "product_search_enhancement"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_search_enhancement"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_search_enhancement"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_search_enhancement"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_search_enhancement"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_search_enhancement"."new_title" IS '增强后的标题';
COMMENT ON COLUMN "product_search_enhancement"."new_description" IS '增强后的描述';
COMMENT ON COLUMN "product_search_enhancement"."new_product_type" IS '增强后的产品类型';
COMMENT ON COLUMN "product_search_enhancement"."filterable_attributes" IS '增强后的可过滤属性';
COMMENT ON TABLE "product_search_enhancement" IS '产品搜索增强';
CREATE TABLE IF NOT EXISTS "product_selling_point" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT NOT NULL,
    "marketing_copies" JSONB NOT NULL,
    "name_chn" VARCHAR(255),
    "description_chn" TEXT,
    "marketing_copies_chn" JSONB NOT NULL,
    "status" VARCHAR(8) NOT NULL DEFAULT 'fresh',
    "last_edited_at" TIMESTAMPTZ,
    "llm_record_id" INT NOT NULL REFERENCES "llm_record" ("id") ON DELETE CASCADE,
    "product_id" INT NOT NULL REFERENCES "product_metadata" ("id") ON DELETE CASCADE,
    "version_id" INT REFERENCES "data_version" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_sel_product_39e0b7" ON "product_selling_point" ("product_id", "llm_record_id", "status");
COMMENT ON COLUMN "product_selling_point"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_selling_point"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_selling_point"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_selling_point"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_selling_point"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_selling_point"."name" IS '卖点名称';
COMMENT ON COLUMN "product_selling_point"."description" IS '卖点描述';
COMMENT ON COLUMN "product_selling_point"."marketing_copies" IS '营销文案';
COMMENT ON COLUMN "product_selling_point"."name_chn" IS '卖点名称中文翻译';
COMMENT ON COLUMN "product_selling_point"."description_chn" IS '卖点描述中文翻译';
COMMENT ON COLUMN "product_selling_point"."marketing_copies_chn" IS '营销文案中文翻译';
COMMENT ON COLUMN "product_selling_point"."status" IS '标注状态';
COMMENT ON COLUMN "product_selling_point"."last_edited_at" IS '上次标注时间';
COMMENT ON COLUMN "product_selling_point"."version_id" IS '当前版本';
CREATE TABLE IF NOT EXISTS "product_type_aspects" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "product_type" VARCHAR(255) NOT NULL,
    "llm_determined_product_type" VARCHAR(255) NOT NULL,
    "aspects" JSONB NOT NULL,
    "llm_record_id" INT NOT NULL REFERENCES "llm_record" ("id") ON DELETE CASCADE,
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_9bebf8" ON "product_type_aspects" ("site_id", "llm_record_id");
COMMENT ON COLUMN "product_type_aspects"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_type_aspects"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_type_aspects"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_type_aspects"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_type_aspects"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_type_aspects"."product_type" IS '网站展示的字面产品类型';
COMMENT ON COLUMN "product_type_aspects"."llm_determined_product_type" IS 'LLM 推断的产品类型';
COMMENT ON COLUMN "product_type_aspects"."aspects" IS '产品类型的属性点';
COMMENT ON TABLE "product_type_aspects" IS '产品类型的属性点';
CREATE TABLE IF NOT EXISTS "product_type_concerns" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "product_type" VARCHAR(255) NOT NULL,
    "llm_determined_product_type" VARCHAR(255) NOT NULL,
    "concerns" JSONB NOT NULL,
    "llm_record_id" INT NOT NULL REFERENCES "llm_record" ("id") ON DELETE CASCADE,
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_dcb6cd" ON "product_type_concerns" ("site_id", "llm_record_id");
COMMENT ON COLUMN "product_type_concerns"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_type_concerns"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_type_concerns"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_type_concerns"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_type_concerns"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_type_concerns"."product_type" IS '网站展示的字面产品类型';
COMMENT ON COLUMN "product_type_concerns"."llm_determined_product_type" IS 'LLM 推断的产品类型';
COMMENT ON COLUMN "product_type_concerns"."concerns" IS '产品类型的顾虑点';
COMMENT ON TABLE "product_type_concerns" IS '产品类型的顾虑点';
CREATE TABLE IF NOT EXISTS "product_type_expanded_aspects" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "product_type" VARCHAR(255) NOT NULL,
    "llm_determined_product_type" VARCHAR(255) NOT NULL,
    "aspects" JSONB NOT NULL,
    "llm_record_id" INT NOT NULL REFERENCES "llm_record" ("id") ON DELETE CASCADE,
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_aefba7" ON "product_type_expanded_aspects" ("site_id", "llm_record_id");
COMMENT ON COLUMN "product_type_expanded_aspects"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_type_expanded_aspects"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_type_expanded_aspects"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_type_expanded_aspects"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_type_expanded_aspects"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_type_expanded_aspects"."product_type" IS '网站展示的字面产品类型';
COMMENT ON COLUMN "product_type_expanded_aspects"."llm_determined_product_type" IS 'LLM 推断的产品类型';
COMMENT ON COLUMN "product_type_expanded_aspects"."aspects" IS '产品类型的属性点';
COMMENT ON TABLE "product_type_expanded_aspects" IS '产品类型的扩展后属性点';
CREATE TABLE IF NOT EXISTS "product_type_expanded_concerns" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "product_type" VARCHAR(255) NOT NULL,
    "llm_determined_product_type" VARCHAR(255) NOT NULL,
    "concerns" JSONB NOT NULL,
    "llm_record_id" INT NOT NULL REFERENCES "llm_record" ("id") ON DELETE CASCADE,
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_product_typ_site_id_4bcefb" ON "product_type_expanded_concerns" ("site_id", "llm_record_id");
COMMENT ON COLUMN "product_type_expanded_concerns"."created_at" IS '创建时间';
COMMENT ON COLUMN "product_type_expanded_concerns"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "product_type_expanded_concerns"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "product_type_expanded_concerns"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "product_type_expanded_concerns"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "product_type_expanded_concerns"."product_type" IS '网站展示的字面产品类型';
COMMENT ON COLUMN "product_type_expanded_concerns"."llm_determined_product_type" IS 'LLM 推断的产品类型';
COMMENT ON COLUMN "product_type_expanded_concerns"."concerns" IS '产品类型的顾虑点';
COMMENT ON TABLE "product_type_expanded_concerns" IS '产品类型的扩展后顾虑点';
CREATE TABLE IF NOT EXISTS "aerich" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSONB NOT NULL
);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
