import xmltodict
from loguru import logger

from crawl2.clients import http
from crawl2.db import models, operations
from crawl2.tasks.celery_wrap import celery_task


async def _get_shopify_domain(domain: str) -> str:
    """
    Get the shop name from the meta.json file of the Shopify store.

    :return:  The shop name (myshopify_domain) from the meta.json file.
    """
    meta_url = f"https://{domain}/meta.json"
    response = await http.http_get(meta_url)
    meta_data = response.json()
    return meta_data["myshopify_domain"]

async def get_urls_from_sitemap(domain: str, sitemap_name: str) -> list[str]:
    """
    Get all URLs from the Shopify store's sitemap.
    """
    site_map_url = f"https://{domain}/sitemap.xml"
    r = await http.http_get(site_map_url)
    sitemap_data = xmltodict.parse(r.text)
    sitemapindex = sitemap_data["sitemapindex"]["sitemap"]
    target_sitemap_url = ""
    for sitemap in sitemapindex:
        sitemap_url = sitemap["loc"]
        if sitemap_name in sitemap_url:
            target_sitemap_url = sitemap_url
            break
    if target_sitemap_url == "":
        logger.error(f"Target sitemap not found for {domain}")
        return []

    logger.info(f"Target sitemap found: {target_sitemap_url}")
    r = await http.http_get(target_sitemap_url)
    sitemap_data = xmltodict.parse(r.text)
    urls_data = sitemap_data["urlset"]["url"]
    # Extract just the URLs from the sitemap data
    if type(urls_data) == list:
        urls = [item["loc"] for item in urls_data]
    else:
        urls = [urls_data["loc"]]
    return urls

async def _get_all_product_urls(domain: str) -> list[str]:
    """
    Get all product URLs from the Shopify store's sitemap.

    :return: a list of product URLs.
    """
    urls = await get_urls_from_sitemap(domain, "products_1.xml")
    urls = [url for url in urls if "products" in url]
    logger.info(f"Total product URLs found for {domain}: {len(urls)}")
    return urls


async def _get_all_collection_urls(domain: str) -> list[str]:
    """
    Get all collection URLs from the Shopify store's sitemap.
    """
    urls = await get_urls_from_sitemap(domain, "collections_1")
    logger.info(f"Total collection URLs found for {domain}: {len(urls)}")
    return urls


async def _get_all_pages_urls(domain: str) -> list[str]:
    """
    Get all pages URLs from the Shopify store's sitemap.
    """
    urls = await get_urls_from_sitemap(domain, "pages_1")
    logger.info(f"Total pages URLs found for {domain}: {len(urls)}")
    return urls


async def _get_all_blogs_urls(domain: str) -> list[str]:
    """
    Get all blogs URLs from the Shopify store's sitemap.
    """
    urls = await get_urls_from_sitemap(domain, "blogs_1")
    logger.info(f"Total blogs URLs found for {domain}: {len(urls)}")
    return urls


@celery_task
async def crawl_site_metadata(domain: str) -> models.ShopifySite:
    """
    挖掘指定站点的元数据，包括商店名称、产品URL、集合URL、页面URL和博客URL等
    """
    await operations.mark_status_processing(models.ShopifySite, domain=domain)
    shop_name = await _get_shopify_domain(domain)
    product_urls = await _get_all_product_urls(domain)
    collection_urls = await _get_all_collection_urls(domain)
    pages_urls = await _get_all_pages_urls(domain)
    blogs_urls = await _get_all_blogs_urls(domain)
    return await operations.save_site_metadata(domain, shop_name, product_urls, collection_urls, pages_urls, blogs_urls)
