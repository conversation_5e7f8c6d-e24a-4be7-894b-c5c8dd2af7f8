<template>
  <div style="display: inline-block">
    <button class="bg-green-500 text-white border-none rounded px-4 py-2 cursor-pointer text-sm mx-2 hover:bg-green-600" @click="openDialog">{{ displayName }}</button>
    <teleport to="body">
      <div v-if="dialogVisible" class="fixed inset-0 bg-black bg-opacity-25 z-[2000] flex items-center justify-center">
        <div class="bg-white rounded-lg min-w-[340px] max-w-[90vw] shadow-lg p-0 overflow-hidden">
          <div class="flex justify-between items-center bg-gray-50 p-4 px-6 font-semibold text-lg">
            <span>触发工作流：{{ workflowName}}</span>
            <button class="bg-transparent border-none text-2xl cursor-pointer text-gray-500" @click="closeDialog">×</button>
          </div>
          <div class="p-2 px-6 text-gray-600 text-sm border-b border-gray-100 bg-gray-50" v-if="description">
            {{ description }}
          </div>
          <div class="p-6">
            <form v-if="paramFields.length" @submit.prevent="submitWorkflow">
              <div v-for="field in paramFields" :key="field.name" class="mb-5">
                <!-- boolean: label 包住 checkbox 和文字 -->
                <div v-if="field.type === 'boolean'" class="flex items-center gap-2">
                  <label :for="field.name" class="flex items-center gap-2">
                    {{ field.name }}
                    <input
                      type="checkbox"
                      v-model="formData[field.name]"
                      :id="field.name"
                      class="mr-2"
                    />
                  </label>
                </div>
                <!-- 其他类型 label -->
                <div v-else class="flex items-start gap-3">
                  <label :for="field.name" class="text-sm mb-0 min-w-[120px] pt-2">{{ field.name }}</label>
                  <div class="flex-1">
                    <!-- select (enum) -->
                    <select
                      v-if="field.enum"
                      v-model="formData[field.name]"
                      :id="field.name"
                      :required="field.required"
                      class="p-2 border border-gray-300 rounded text-base w-full"
                    >
                      <option v-for="option in field.enum" :key="option" :value="option">{{ option }}</option>
                    </select>
                    <!-- textarea -->
                    <textarea
                      v-else-if="field.format === 'textarea'"
                      v-model="formData[field.name]"
                      :id="field.name"
                      :placeholder="field.name"
                      :required="field.required"
                      class="p-2 border border-gray-300 rounded text-base w-full"
                    />
                    <!-- array of strings -->
                    <div v-else-if="field.type === 'array' && field.items && field.items.type === 'string'" class="w-full">
                      <div class="space-y-2">
                        <div v-for="(item, index) in formData[field.name]" :key="index" class="flex items-center gap-2">
                          <input
                            v-model="formData[field.name][index]"
                            type="text"
                            :placeholder="`项目 ${index + 1}`"
                            :required="field.required && index === 0"
                            class="p-2 border border-gray-300 rounded text-base flex-1"
                          />
                          <button
                            type="button"
                            @click="removeArrayItem(field.name, index)"
                            class="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
                          >
                            删除
                          </button>
                        </div>
                        <button
                          type="button"
                          @click="addArrayItem(field.name)"
                          class="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
                        >
                          添加项目
                        </button>
                      </div>
                    </div>
                    <!-- number/integer -->
                    <input
                      v-else-if="field.type === 'number' || field.type === 'integer'"
                      v-model.number="formData[field.name]"
                      type="number"
                      :id="field.name"
                      :placeholder="field.name"
                      :required="field.required"
                      class="p-2 border border-gray-300 rounded text-base w-full"
                    />
                    <!-- default: string -->
                    <input
                      v-else-if="field.type !== 'boolean'"
                      v-model="formData[field.name]"
                      type="text"
                      :id="field.name"
                      :placeholder="field.name"
                      :required="field.required"
                      class="p-2 border border-gray-300 rounded text-base w-full"
                    />
                  </div>
                </div>
              </div>
              <div class="flex gap-4 mt-6">
                <button type="submit" class="px-5 py-2 border-none rounded text-base cursor-pointer bg-green-500 text-white hover:bg-green-600 disabled:bg-green-300 disabled:cursor-not-allowed" :disabled="loading">提交</button>
                <button type="button" @click="editWorkflow" class="px-5 py-2 border-none rounded text-base cursor-pointer bg-blue-500 text-white hover:bg-blue-600" :disabled="loading">编辑</button>
                <button type="button" @click="closeDialog" class="px-5 py-2 border-none rounded text-base cursor-pointer bg-gray-200 text-gray-800 hover:bg-gray-300">取消</button>
              </div>
            </form>
            <div v-else-if="loading" class="text-gray-600">加载参数...</div>
            <div v-else class="text-red-500">未找到参数定义</div>
            <div v-if="error" class="text-red-500 mt-4">{{ error }}</div>
            <div v-if="success" class="text-green-600 mt-4">
              工作流已触发！
              <div class="flex gap-4 mt-2 flex-wrap">
                <button 
                  v-if="workflowResponse?.id" 
                  @click="navigateToWorkflow"
                  class="text-blue-600 no-underline font-medium px-2 py-1 rounded bg-gray-50 border border-gray-200 transition-all duration-200 text-sm cursor-pointer inline-block hover:bg-blue-50 hover:border-blue-600"
                >
                  查看工作流详情
                </button>
                <a 
                  v-if="workflowResponse?.id && workflowResponse?.log_url" 
                  :href="workflowResponse.log_url"
                  target="_blank"
                  class="text-blue-600 no-underline font-medium px-2 py-1 rounded bg-gray-50 border border-gray-200 transition-all duration-200 text-sm cursor-pointer inline-block hover:bg-blue-50 hover:border-blue-600"
                >
                  点击查看日志
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </teleport>

    <!-- 工作流编辑器对话框 -->
    <teleport to="body">
      <div v-if="editorVisible" class="fixed inset-0 bg-black bg-opacity-25 z-[2000] flex items-center justify-center">
        <div class="bg-white rounded-lg min-w-[800px] max-w-[95vw] max-h-[95vh] shadow-lg p-0 overflow-hidden">
          <div class="flex justify-between items-center bg-gray-50 p-4 px-6 font-semibold text-lg">
            <span>编辑工作流：{{ workflowName }}</span>
            <button class="bg-transparent border-none text-2xl cursor-pointer text-gray-500" @click="closeEditor">×</button>
          </div>
          <div class="p-6">
            <WorkflowEditor 
              ref="workflowEditor"
              :initial-workflow="workflowDefinition"
              :initial-name="workflowName"
              @workflow-created="onWorkflowCreated"
            />
          </div>
        </div>
      </div>
    </teleport>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { useRouter } from 'vue-router'
import { api } from '@/api/api.js'
import WorkflowEditor from './WorkflowEditor.vue'

const router = useRouter()

const props = defineProps({
  workflowName: { type: String, required: true },
  displayName: { type: String, required: true }
})

const dialogVisible = ref(false)
const editorVisible = ref(false)
const paramFields = ref([])
const formData = ref({})
const loading = ref(false)
const error = ref('')
const success = ref(false)
const description = ref('')
const workflowResponse = ref(null) // 统一保存工作流响应对象
const workflowDefinition = ref(null)
const workflowEditor = ref(null)

const openDialog = async () => {
  dialogVisible.value = true
  error.value = ''
  success.value = false
  loading.value = true
  formData.value = {}
  paramFields.value = []
  description.value = ''
  workflowResponse.value = null // 重置工作流响应对象
  try {
    const { data } = await api.get('/api/v1/workflows/factories')
    const def = data.find(d => d.name === props.workflowName)
    if (def) {
      description.value = def.description || ''
    }
    if (def && def.params && def.params.properties) {
      paramFields.value = Object.entries(def.params.properties).map(([name, schema]) => ({
        name,
        type: schema.type || 'string',
        required: (def.params.required || []).includes(name),
        enum: schema.enum,
        format: schema.format,
        items: schema.items,
        default: schema.default
      }))
      // 初始化表单数据，使用服务端默认值
      paramFields.value.forEach(f => { 
        if (f.default !== undefined) {
          formData.value[f.name] = f.default
        } else {
          // 如果服务端没有提供默认值，根据类型设置合适的初始值
          if (f.type === 'array') {
            formData.value[f.name] = []
          } else if (f.type === 'boolean') {
            formData.value[f.name] = false
          } else {
            formData.value[f.name] = ''
          }
        }
      })
    } else {
      paramFields.value = []
    }
  } catch (e) {
    error.value = '参数定义加载失败'
  } finally {
    loading.value = false
  }
}

const addArrayItem = (fieldName) => {
  if (!formData.value[fieldName]) {
    formData.value[fieldName] = []
  }
  formData.value[fieldName].push('')
}

const removeArrayItem = (fieldName, index) => {
  formData.value[fieldName].splice(index, 1)
}

const closeDialog = () => {
  dialogVisible.value = false
  error.value = ''
  success.value = false
  workflowResponse.value = null // 重置工作流响应对象
}

const closeEditor = () => {
  editorVisible.value = false
  workflowDefinition.value = null
}

const navigateToWorkflow = () => {
  const id = workflowResponse.value?.id
  closeDialog()
  router.push(`/workflows/${id}`)
}

const submitWorkflow = async () => {
  loading.value = true
  error.value = ''
  success.value = false
  try {
    // 清理提交数据：只移除 undefined，保留所有用户输入的值（包括空字符串）
    const cleanedData = {}
    Object.entries(formData.value).forEach(([key, value]) => {
      if (value !== undefined) {
        cleanedData[key] = value
      }
    })
    
    const response = await api.post(`/api/v1/workflows/factory/${props.workflowName}`, cleanedData)
    success.value = true
    // 保存工作流响应对象
    workflowResponse.value = response.data
  } catch (e) {
    error.value = e?.response?.data?.detail || '工作流触发失败'
  } finally {
    loading.value = false
  }
}

const editWorkflow = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 清理提交数据：只移除 undefined，保留所有用户输入的值（包括空字符串）
    const cleanedData = {}
    Object.entries(formData.value).forEach(([key, value]) => {
      if (value !== undefined) {
        cleanedData[key] = value
      }
    })
    
    // 调用PUT API获取工作流定义
    const response = await api.put(`/api/v1/workflows/factory/${props.workflowName}`, cleanedData)
    workflowDefinition.value = response.data
    
    // 关闭参数对话框，打开编辑器对话框
    dialogVisible.value = false
    editorVisible.value = true
  } catch (e) {
    error.value = e?.response?.data?.detail || '获取工作流定义失败'
  } finally {
    loading.value = false
  }
}

const onWorkflowCreated = (workflowId) => {
  closeEditor()
  // 可以在这里添加成功提示或导航到工作流详情
  router.push(`/workflows/${workflowId}`)
}
</script>

<style scoped>
</style> 