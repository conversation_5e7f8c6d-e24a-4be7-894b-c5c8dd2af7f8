<template>
  <div class="fixed inset-0 bg-transparent flex justify-center items-center z-[1000]" @click.self="$emit('close')">
    <div class="bg-white p-8 rounded-lg shadow-lg w-[95%] max-w-[1200px] max-h-[95vh] flex flex-col">
      <h3 class="mt-0 mb-5 text-lg font-bold flex-shrink-0">Edit FAQs</h3>
      <form class="flex flex-col max-h-[85vh]">
        <!-- 上面部分：元信息 + FAQ列表 -->
        <div class="flex gap-6 flex-1 min-h-0 mb-6">
          <!-- 左侧：元信息 -->
          <div class="w-[10%] flex flex-col gap-3">
            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="font-medium text-sm mb-1">Product</div>
              <a :href="editableData.product_url" target="_blank" class="text-[0.9rem] text-blue-600 hover:underline leading-relaxed">
                {{ editableData.product_title }}
              </a>
            </div>
            
            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="font-medium text-sm mb-1">Product Type</div>
              <div class="text-[0.9rem] text-[#222] leading-relaxed">{{ editableData.product_type }}</div>
            </div>
            
            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="font-medium text-sm mb-1">Product Price</div>
              <div class="text-[0.9rem] text-[#222] leading-relaxed">{{ editableData.product_price }}</div>
            </div>
            
            <!-- 时间信息 -->
            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="font-medium text-sm mb-1">时间信息</div>
              <div class="text-xs text-gray-600 space-y-1">
                <div v-if="editableData.created_at">
                  <span class="font-medium">生成时间:</span> 
                  {{ formatDateTime(editableData.created_at) }}
                </div>
                <div v-if="editableData.last_edited_at">
                  <span class="font-medium">最近标注:</span> 
                  {{ formatDateTime(editableData.last_edited_at) }}
                </div>
                <div v-else class="text-gray-500">
                  <span class="font-medium">最近标注:</span> 尚未标注
                </div>
              </div>
            </div>
          </div>
          
          <!-- 右侧：FAQ列表区域 -->
          <div class="flex-1 flex flex-col min-h-0">
            <div class="flex items-center justify-between mb-2">
              <label class="font-bold text-base">FAQs</label>
              <div class="text-sm text-gray-600">
                已选择: <span class="font-bold text-green-600">{{ selectedCount }}</span> / 
                总计: <span class="font-bold">{{ totalCount }}</span>
              </div>
            </div>
            
            <div class="flex-1 border border-gray-200 rounded-lg overflow-hidden">
              <div class="h-full overflow-y-auto p-3 bg-gray-50">
                <div v-for="(faq, index) in editableData.faqs" :key="index" 
                     class="mb-1 p-3 bg-white rounded-lg border border-gray-200 hover:border-blue-300 transition-colors">
                  <div class="flex items-start gap-3">
                    <input 
                      type="checkbox" 
                      v-model="faqEnabled[index]" 
                      class="mt-1 flex-shrink-0"
                      :id="`faq-${index}`"
                    />
                    <div class="flex-1 min-w-0">
                      <label :for="`faq-${index}`" class="cursor-pointer block">
                        <div v-if="faq.question_chn || faq.answer_chn" class="text-[0.97rem] text-[#222] mb-0.5 leading-tight">
                          <div v-if="faq.question_chn"><strong>Q:</strong> {{ faq.question_chn }}</div>
                          <div v-if="faq.answer_chn"><strong>A:</strong> {{ faq.answer_chn }}</div>
                        </div>
                        <div class="text-[0.9rem] text-[#222] mb-0.5 leading-tight"><strong>Q:</strong> {{ faq.question }}</div>
                        <div class="text-[0.9rem] text-[#222] leading-tight"><strong>A:</strong> {{ faq.answer }}</div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 下面部分：状态选择 + 操作按钮 -->
        <div class="flex gap-4 pt-3 border-t border-gray-200 flex-shrink-0">
          <!-- 左侧：状态选择 -->
          <div class="flex-1 p-3 bg-gray-50 rounded-lg flex justify-end">
            <div class="flex gap-4">
              <label v-for="option in statusOptions" :key="option.value" class="flex items-center gap-2 text-[0.97rem] cursor-pointer">
                <input type="radio" v-model="editableData.status" :value="option.value" />
                <div>
                  <div class="font-medium" :class="option.color">{{ option.label }}</div>
                  <div class="text-xs text-gray-500">{{ option.label_chn }}</div>
                </div>
              </label>
            </div>
          </div>
          
          <!-- 右侧：操作按钮 -->
          <div class="flex items-center gap-4">
            <button type="button" @click="$emit('close')" class="px-6 py-2 rounded font-bold bg-gray-200 text-gray-800 hover:bg-gray-300">Cancel</button>
            <button type="button" @click="saveAndNext" class="px-6 py-2 rounded font-bold bg-green-500 text-white hover:bg-green-600">Save&Next</button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'

const props = defineProps({
  rowData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close', 'save', 'saveAndNext'])

const statusOptions = [
  { value: 'ready', label: 'Ready', label_chn: '已就绪', color: 'text-green-600' },
  { value: 'editing', label: 'Editing', label_chn: '编辑中', color: 'text-blue-600' },
  { value: 'disabled', label: 'Disabled', label_chn: '已禁用', color: 'text-red-600' },
  { value: 'archived', label: 'Archived', label_chn: '已归档', color: 'text-gray-600' },
]

const editableData = ref({})
const faqEnabled = ref([])

// 计算属性：统计选中的FAQ数量
const selectedCount = computed(() => {
  return faqEnabled.value.filter(enabled => enabled).length
})

// 计算属性：总FAQ数量
const totalCount = computed(() => {
  return editableData.value.faqs?.length || 0
})

watch(() => props.rowData, (newData) => {
  const data = { ...newData };
  data.status = data.status && data.status !== 'fresh' ? data.status : 'ready';
  // 确保faqs是数组
  data.faqs = Array.isArray(data.faqs) ? data.faqs : [];
  editableData.value = data;
  // 默认所有FAQ项都启用
  faqEnabled.value = data.faqs.map(() => true);
}, { immediate: true, deep: true })

const saveAndNext = () => {
  // 只保留被勾选的FAQ项
  const enabledIndexes = faqEnabled.value
    .map((v, i) => v ? i : -1)
    .filter(i => i !== -1);
  
  if (enabledIndexes.length === 0) {
    alert('At least one FAQ is required.');
    return;
  }
  
  const newData = {
    ...editableData.value,
    faqs: enabledIndexes.map(i => editableData.value.faqs[i])
  };
  
  emit('saveAndNext', newData);
}

// 时间格式化函数
const formatDateTime = (dateTime) => {
  if (!dateTime) return 'N/A'
  
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    return 'Invalid Date'
  }
}
</script>
