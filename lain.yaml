appname: shop-pupil

build:
  context: ./
  dockerfile: Dockerfile


web:
  cmd: uvicorn --host 0.0.0.0 crawl2.web.main:app
  healthcheck: http://localhost:8000/api/v1/health


worker:
  cmd: celery --app crawl2.celery_app:app worker --loglevel info --concurrency 4 --queues celery

# 专门的 lechat worker，处理路由到 lechat queue 的任务
proc.lechat-worker:
  cmd: celery --app crawl2.celery_app:app worker --loglevel info --concurrency 16 --queues lechat

web.feature:
  cmd: uvicorn --host 0.0.0.0 crawl2.web.main:app
