import os

import pytest
from fastapi.testclient import TestClient
from tortoise import Tortoise
from loguru import logger

from crawl2.web.main import app

pytest.skip("跳过测试，等待修复 sqlite 的schema", allow_module_level=True)


@pytest.fixture(scope="module", autouse=True)
async def setup_database():
    """设置测试数据库"""
    cur_dir = os.path.dirname(os.path.abspath(__file__))
    # test_db.sqlite3 来自于 https://git.leyantech.com/oversea/shopify/shop-pupil/-/jobs/4749706
    db_path = os.path.join(cur_dir, "test_db.sqlite3")
    await Tortoise.init(
        db_url=f"sqlite://{db_path}", modules={"models": ["crawl2.db.models"]}
    )
    await Tortoise.generate_schemas()
    yield
    await Tortoise.close_connections()


@pytest.fixture
def client():
    """创建测试客户端"""
    return TestClient(app)


async def test_health_check(client):
    """测试健康检查端点"""
    response = client.get("/health")
    assert response.status_code == 200


async def test_list_sites(client):
    """测试站点列表"""
    response = client.get("/api/v1/sites")
    assert response.status_code == 200
    sites = response.json()
    assert len(sites) == 1
    assert sites[0]["domain"] == "songmics.com"


async def test_get_site_details(client):
    """测试获取站点详情"""
    response = client.get("/api/v1/sites/songmics.com")
    assert response.status_code == 200
    data = response.json()
    assert data["shopify_domain"] == "zielhome-us.myshopify.com"
    assert len(data["products"]) == 2
    assert len(data["product_type_aspects"]) > 0
    assert "product_type_concerns" in data
    # print(data)


async def test_get_product_details(client):
    """测试获取产品详情"""
    response = client.get("/api/v1/sites/songmics.com/products/7894540845305")
    assert response.status_code == 200
    product = response.json()
    assert product["product_id"] == "7894540845305"
    assert "selling_points" in product
    assert "page_markdown" in product
    assert "knowledge_markdown" in product


async def test_gen_marketing_copy(client):
    """测试生成营销话术"""
    response = client.post(
        "/api/v1/inner/aigc/gen_marketing_copy",
        json={
            "topic": "Brand Reputation",
            "sub_topic": "Brand Introduction",
            "name": "Brand History",
            "value": "ororo was founded in 2015 in Kansas City with the mission to provide sustainable warmth through functional and fashionable apparel. The brand originated from the Midwest during one of the coldest winters, aiming to create heated apparel that could provide warmth even on the coldest days. ororo is recognized as a pioneer in heated apparel in North America and is known for its trustworthiness, boasting a 4.7 rating on Trustpilot. The company uses eco-friendly materials such as REPREVE® recycled fleece made from plastic bottles to reduce carbon emissions.", # NOQA
        },
    )
    assert response.status_code == 200
    result = response.json()
    logger.info(result)


async def test_gen_faq(client):
    """测试生成FAQ"""
    response = client.post(
        "/api/v1/inner/aigc/gen_faq",
        json={
            "topic": "Brand Reputation",
            "sub_topic": "Brand Introduction",
            "name": "Brand History",
            "value": "ororo was founded in 2015 in Kansas City with the mission to provide sustainable warmth through functional and fashionable apparel. The brand originated from the Midwest during one of the coldest winters, aiming to create heated apparel that could provide warmth even on the coldest days. ororo is recognized as a pioneer in heated apparel in North America and is known for its trustworthiness, boasting a 4.7 rating on Trustpilot. The company uses eco-friendly materials such as REPREVE® recycled fleece made from plastic bottles to reduce carbon emissions.", # NOQA
        },
    )
    assert response.status_code == 200
    result = response.json()
    logger.info(result)


async def test_gen_question(client):
    """测试生成问题"""
    response = client.post(
        "/api/v1/inner/aigc/gen_question",
        json={
            "topic": "Brand Reputation",
            "sub_topic": "Brand Introduction",
            "name": "Brand History",
            "value": "ororo was founded in 2015 in Kansas City with the mission to provide sustainable warmth through functional and fashionable apparel. The brand originated from the Midwest during one of the coldest winters, aiming to create heated apparel that could provide warmth even on the coldest days. ororo is recognized as a pioneer in heated apparel in North America and is known for its trustworthiness, boasting a 4.7 rating on Trustpilot. The company uses eco-friendly materials such as REPREVE® recycled fleece made from plastic bottles to reduce carbon emissions.", # NOQA
        },
    )
    assert response.status_code == 200
    result = response.json()
    logger.info(result)


async def test_store_knowledge_point_upsert(client):
    """测试店铺知识点插入"""
    response = client.post(
        "/api/v1/inner/store_knowledge_point/upsert",
        json={
            "point_id": "186bfa36-7b2e-4e93-82c1-47fb955eba50",
            "topic": "测试 topic",
            "question": "测试 question",
            "answer": "测试 answer",
            "source": "测试 source",
            "source_detail": "测试 source_detail",
            "quality": 1,
            "label": "测试 label",
            "detailed_label": "测试 detailed_label",
            "extra_questions": ["测试 extra_question1", "测试 extra_question2"],
            "store_domain": "测试 store_domain",
        },
    )
    assert response.status_code == 200
    result = response.json()
    logger.info(result)
