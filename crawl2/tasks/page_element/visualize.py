import textwrap

from loguru import logger
from pocketflow import Flow


def check_flow(start: Flow):
    """静态检查 Flow 定义的正确性，不生成 mermaid 图。
    检查点：
      1. successor=None
      2. action 重复定义（覆盖）
    """
    errors = []
    visited = set()
    seen_edges = {}

    def walk(node):
        if node in visited:
            return
        visited.add(node)

        if isinstance(node, Flow):
            if node.start_node:
                walk(node.start_node)

            for act, nxt in node.successors.items():
                if nxt is None:
                    errors.append(f"❌ Flow {node.__class__.__name__} has successor '{act}' but it's None")
                else:
                    seen_edges.setdefault(node, set())
                    if act in seen_edges[node]:
                        errors.append(f"⚠️ Flow {node.__class__.__name__} has duplicate action '{act}', "
                                      f"earlier successor will be overwritten")
                    seen_edges[node].add(act)
                    walk(nxt)

        else:
            for act, nxt in node.successors.items():
                if nxt is None:
                    errors.append(f"❌ Node {node.__class__.__name__} has successor '{act}' but it's None")
                else:
                    seen_edges.setdefault(node, set())
                    if act in seen_edges[node]:
                        errors.append(f"⚠️ Node {node.__class__.__name__} has duplicate action '{act}', "
                                      f"earlier successor will be overwritten")
                    seen_edges[node].add(act)
                    walk(nxt)

    walk(start)
    if errors:
        raise ValueError("\n".join(errors))


def build_mermaid(start):
    ids, visited, lines = {}, set(), ["graph LR"]
    seen_edges = {}

    def get_id(node):
        """全局唯一 ID = 类名 + 内存地址（保证唯一）"""
        if node in ids:
            return ids[node]
        nid = f"{node.__class__.__name__}_{id(node)}"
        ids[node] = nid
        return nid

    def link(a, b, action=None):
        if action:
            lines.append(f"    {a} -->|{action}| {b}")
        else:
            lines.append(f"    {a} --> {b}")

    def walk(node, parent=None, action=None):
        if node in visited:
            if parent:
                link(parent, get_id(node), action)
            return
        visited.add(node)

        if isinstance(node, Flow):
            start_node = node.start_node
            if parent and start_node:
                link(parent, get_id(start_node), action)

            lines.append(f"\n    subgraph sub_{get_id(node)}[{node.__class__.__name__}]\n")

            if start_node:
                walk(start_node)

            for act, nxt in node.successors.items():
                if nxt is None:
                    logger.warning(f"❌ Flow {node.__class__.__name__} has successor '{act}' but it's None")
                    continue

                seen_edges.setdefault(node, set())
                if act in seen_edges[node]:
                    logger.warning(f"⚠️ Flow {node.__class__.__name__} has duplicate action '{act}', "
                                   f"earlier successor will be overwritten")
                seen_edges[node].add(act)

                if start_node:
                    walk(nxt, get_id(start_node), act)
                elif parent:
                    link(parent, get_id(nxt), action)
                else:
                    walk(nxt, None, act)

            lines.append("    end\n")

        else:
            nid = get_id(node)
            lines.append(f"    {nid}['{node.__class__.__name__}']")
            if parent:
                link(parent, nid, action)

            seen_edges.setdefault(node, set())
            for act, nxt in node.successors.items():
                if nxt is None:
                    logger.warning(f"❌ Node {node.__class__.__name__} has successor '{act}' but it's None")
                    continue

                if act in seen_edges[node]:
                    logger.warning(f"⚠️ Node {node.__class__.__name__} has duplicate action '{act}', "
                                   f"earlier successor will be overwritten")
                seen_edges[node].add(act)

                walk(nxt, nid, act)

    walk(start)
    return "\n".join(lines)
