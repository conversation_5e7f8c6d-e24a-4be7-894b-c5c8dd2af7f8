from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "users" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "username" VARCHAR(255) NOT NULL,
    "email" VARCHAR(255),
    "full_name" VARCHAR(255),
    "phone_number" VARCHAR(20),
    "is_active" BOOL NOT NULL DEFAULT True,
    "is_superuser" BOOL NOT NULL DEFAULT False,
    "last_login" TIMESTAMPTZ,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "sso_metadata" JSONB NOT NULL
);
CREATE INDEX IF NOT EXISTS "idx_users_usernam_df2ee6" ON "users" ("username", "email");
COMMENT ON COLUMN "users"."username" IS '用户名，多为邮箱前缀';
COMMENT ON COLUMN "users"."email" IS '邮箱';
COMMENT ON COLUMN "users"."full_name" IS '全名，多为真实姓名';
COMMENT ON COLUMN "users"."phone_number" IS '手机号';
COMMENT ON COLUMN "users"."is_active" IS '是否激活';
COMMENT ON COLUMN "users"."is_superuser" IS '是否超级用户';
COMMENT ON COLUMN "users"."last_login" IS '最后登录时间';
COMMENT ON COLUMN "users"."created_at" IS '创建时间';
COMMENT ON COLUMN "users"."updated_at" IS '更新时间';
COMMENT ON COLUMN "users"."sso_metadata" IS 'SSO返回的元数据';
COMMENT ON TABLE "users" IS '用户表，存储从SSO获取的用户信息';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "users";"""
