import json
from collections import defaultdict

from loguru import logger

from crawl2.clients import llm
from crawl2.clients.shopify_knowledge import create_store_knowledge_value_item, ShopifyKnowledgeClient
from crawl2.db import operations
from crawl2.tasks.celery_wrap import celery_task
from .prompts import QAPAIR_VALUE_PROMPT
from ..task_tqdm import CeleryTaskTqdmAsync

llm_conf = llm.LLMConfig('usq', 'qwen2.5-32b-chat-fp8-offline', 0, 1024)


async def _process_single_label_group(
        domain: str,
        label: str,
        detailed_label: str,
        qa_list: list[str],
        clear_existing: bool = False,
):
    existed = await operations.get_store_knowledge_value(domain, label, detailed_label)
    if existed and not clear_existing:
        logger.info(f"Store knowledge value already exists for {label} - {detailed_label}, skipping extraction.")
        return
    elif existed and clear_existing:
        logger.info(f"Clearing existing store knowledge value for {label} - {detailed_label}.")
    prompt = QAPAIR_VALUE_PROMPT.format(f"{label} - {detailed_label}", f"{label} - {detailed_label}", '\n'.join(qa_list))
    try:
        response = await llm.call_llm(llm_conf, prompt, parse_json=False)
        value = response.strip() if response else ""
    except Exception as e:
        logger.error(f"Error processing label {label} - {detailed_label}: {e}")
        value = ""
    if value:
        await operations.save_store_knowledge_value(
            domain, label, detailed_label, value)
        logger.info(f"已保存 {label} - {detailed_label} 的知识归纳值")
    return {"label": label, "detailed_label": detailed_label, "value": value}


@celery_task
async def extract_store_knowledge_value(domain: str, clear_existing: bool = False):
    """
    为指定站点归纳所有店铺知识点（按label+detailed_label分组）
    """
    logger.info(f"开始extract_store_knowledge_value任务: {domain}")
    knowledge_points = await operations.list_store_knowledge(domain)
    store_label_answers = defaultdict(lambda: defaultdict(set))
    for kp in knowledge_points:
        label = kp.label
        detailed_label = kp.detailed_label
        is_deleted = kp.is_deleted
        quality = kp.quality
        if not label or not detailed_label or detailed_label == "其他" or is_deleted != 0 or quality != 1:
            continue
        qa_str = json.dumps({"question": kp.question, "answer": kp.answer}, ensure_ascii=False)
        store_label_answers[label][detailed_label].add(qa_str)

    sample_qa_num = 50
    tasks = []
    for label, detailed_labels in store_label_answers.items():
        for detailed_label, qa in detailed_labels.items():
            tasks.append(_process_single_label_group(domain, label, detailed_label, list(qa)[:sample_qa_num]))
    results = await CeleryTaskTqdmAsync.gather(*tasks, desc="知识归纳进度")
    logger.info(f"处理完成，已写入数据库，共{len([r for r in results if r and r['value']])}条")


@celery_task
async def sync_store_knowledge_value_to_shopify_knowledge(
    domain: str,
):
    """
    同步店铺知识点到 Shopify 知识库
    """
    site = await operations.get_site(domain)
    if not site:
        logger.error(f"No site found for {domain}")
        return
    store_domain = site.shopify_domain
    knowledge_points = await operations.list_store_knowledge_value(domain)
    if not knowledge_points:
        logger.warning(f"No store knowledge value found for {domain}")
        return
    items = []
    for kp in knowledge_points:
        detailed_label = kp.detailed_label
        value = kp.value
        item = create_store_knowledge_value_item(detailed_label, value)
        if item:
            items.append(item)
    if not items:
        logger.warning(f"No store knowledge value to sync for {domain}")
        return
    shopify_knowledge_client = ShopifyKnowledgeClient.get_client()
    tasks = [
        shopify_knowledge_client.sync_shopify_knowledge(store_domain, item)
        for item in items
    ]
    await CeleryTaskTqdmAsync.gather(*tasks, desc="同步店铺知识点到 Shopify 知识库")
