# Shopify 站点数据挖掘流程设计文档

## 概述

本文档介绍了如何通过 crawl-site-workflow 来挖掘一个 Shopify 站点的数据。整个流程是一个基于 Celery 的异步任务链，能够自动从 Shopify 站点中提取各种类型的数据，包括产品信息、卖点、FAQ、页面元素等。

## 1. Workflow 触发方式

Workflow 主要通过 `crawl2/web/api/sites.py` 中提供的 HTTP API 接口触发：

### 1.1 挖掘流程触发接口
```http
POST /sites
Content-Type: application/json

{
  "site_url": "https://example.myshopify.com",
  "task_id": "optional_shopify_knowledge_task_id"
  "crawl_strategy": {
    "product_selling_point": {
      "target": "all"
    },
    "product_concern_point": {
      "target": "all"
    },
    "product_faq": {
      "target": "specific",
      "product_ids": []
    }
  }
}
```

接口中各字段说明:
1. `site_url`: 目标站点的 http 链接， 指向的 shopify 站点是否存在，该接口会执行不同的操作：
   1. **站点不存在**：创建新站点并启动挖掘流程
   2. **站点已存在**：重新启动挖掘流程
2. `task_id`: 可选参数，用于关联到 Shopify 知识库的任务 ID, 当 shopify 完成整个挖掘流程时，会将流程结果通知到 Shopify 知识库。
3. `crawl_strategy`: 可选参数，用于指定挖掘策略。如果不指定 `crawl_strategy` 则按最近一次为该站点设置的挖掘策略启动流程，否则按此次设置的最新挖掘策略启动流程。目前支持以下知识类型的挖掘策略：
   - `product_selling_point`: 产品卖点挖掘策略
   - `product_concern_point`: 产品关注点挖掘策略
   - `product_faq`: 产品 FAQ 挖掘策略



### 1.2 工作流触发流程

```mermaid
sequenceDiagram
    participant Client
    participant SitesAPI
    participant Workflows
    participant Celery
    participant Database

    Client->>SitesAPI: POST /sites 或 PUT /{domain}/crawl
    SitesAPI->>Database: 检查站点是否存在
    SitesAPI->>Database: 更新挖掘策略（如果指定）
    SitesAPI->>Workflows: 调用 create_crawl_site_workflow()
    Workflows->>Celery: 创建并执行任务链
    Celery->>Database: 记录工作流状态
    SitesAPI->>Client: 返回站点信息
```

## 2. 站点挖掘策略

### 2.1 策略定制接口

每个站点都可以个性化定制挖掘策略，提供两个主要接口：

#### 接口1：触发挖掘时指定策略
**创建站点时**：`POST /sites` 接口的 `crawl_strategy` 参数

#### 接口2：单独修改策略（不立即触发流程）
```http
POST /sites/{domain}/crawl_strategy
Content-Type: application/json

{
  "strategy": "new_strategy",
  "comment": "策略更新说明"
}
```

### 2.2 策略管理接口

```http
# 获取当前策略
GET /sites/{domain}/crawl_strategy

# 删除策略（恢复默认）
DELETE /sites/{domain}/crawl_strategy

# 获取策略历史
GET /sites/{domain}/crawl_strategy/history
```

## 3. 典型挖掘流程任务组成

### 3.1 任务执行顺序

基于 `workflows.py` 中的 `create_crawl_site_workflow` 函数，一个完整的站点挖掘流程包含以下任务（按执行顺序）：

```mermaid
graph TD
    A[站点元数据爬取] --> B[产品元数据爬取]
    B --> C[产品类型修复]
    C --> D[产品页面原始内容爬取]
    D --> E[产品知识提取]
    E --> F[产品类型PV Schema提取]
    F --> G[产品PV提取]
    G --> H[产品PV同步到知识库]
    H --> I[产品标题关键词提取]
    I --> J[产品标题关键词同步]
    J --> K[产品摘要提取]
    K --> L[产品知识同步到Qdrant]
    L --> M[产品知识同步到知识库]
    M --> N[产品类型维度提取]
    N --> O[产品类型关注点提取]
    O --> P[产品类型维度扩展]
    P --> Q[产品类型关注点扩展]
    Q --> R[产品卖点提取]
    R --> S[产品卖点翻译]
    S --> T[产品关注点提取]
    T --> U[产品关注点翻译]
    U --> V[产品FAQ提取]
    V --> W[产品FAQ翻译]
    W --> X[产品类型场景生成]
    X --> Y[产品场景生成]
    Y --> Z[产品类型场景同步]
    Z --> AA[页面元数据爬取]
    AA --> BB[博客元数据爬取]
    BB --> CC[集合元数据爬取]
    CC --> DD[店铺知识提取]
    DD --> EE[店铺知识同步到Qdrant]
    EE --> FF[店铺知识同步到知识库]
    FF --> GG[集合知识提取]
    GG --> HH[集合知识同步到Qdrant]
    HH --> II[店铺知识价值提取]
    II --> JJ[店铺知识价值同步]
    JJ --> KK[页面元素检测]
    KK --> LL[页面元素爬取]
    LL --> MM[产品卖点与元素对齐]
    MM --> NN[场景卖点与元素对齐]
    NN --> OO[产品卖点同步到知识库]
    OO --> PP[场景卖点同步]
```

### 3.2 任务分组说明

- **基础数据爬取**：站点元数据、产品元数据、页面内容
- **知识提取**：产品知识、卖点、关注点、FAQ
- **数据同步**：同步到 Qdrant 向量数据库、Shopify 知识库
- **页面元素分析**：页面元素检测、卖点与元素对齐
- **场景化处理**：场景卖点生成、场景与元素对齐

## 4. 重复挖掘处理策略

### 4.1 处理原则

为了避免重复挖掘相同数据，系统采用以下策略：

**总原则**：尽量避免重复挖掘相同数据，通过数据库查询检查数据是否存在，如果存在且没有通过参数 `clear_existing` 指定强制更新，则直接跳过关联知识的挖掘，把开销控制在数个数据库查询内，避免繁重的爬虫和大模型 API 调用。

### 4.2 具体实现示例
**单个产品卖点挖掘**:

```python
@celery_task
async def extract_and_save_single_product_selling_points(product_url: str, clear_existing: bool = False):
    """
    挖掘单个商品的卖点
    """
    # 1. 检查是否已存在卖点数据
    existing_selling_points = await operations.get_product_selling_points(product_url)
    
    # 2. 如果存在且不强制更新，直接跳过
    if existing_selling_points and not clear_existing:
        logger.info(f"Product selling points already exist for {product_url}, skipping extraction.")
        return
    
    # 3. 如果存在且强制更新，先删除旧数据
    elif existing_selling_points and clear_existing:
        logger.info(f"Clearing existing selling points for {product_url}.")
        await operations.delete_product_selling_points(product_url)
    
    # 4. 执行实际的卖点提取和保存
    selling_points = await extract_single_product_selling_points(product_url)
    if not selling_points:
        return
    await operations.save_product_selling_points(product_url, selling_points)
```

### 4.2 性能优化效果

通过这种策略，系统能够：

1. **避免重复计算**：已存在的数据直接跳过，节省大模型 API 调用
2. **控制数据库开销**：仅需数个简单的 SELECT 查询即可判断是否需要处理
3. **支持强制更新**：通过 `clear_existing=True` 参数可以强制重新挖掘

### 4.3 适用场景

- **首次挖掘**：新站点或新产品，`clear_existing=False`（默认值）
- **增量更新**：已有数据，`clear_existing=False`，跳过已存在的数据
- **全量更新**：需要重新挖掘所有数据，`clear_existing=True`

## 5. 挖掘数据流向

### 5.1 数据类型及流向

```mermaid
graph LR
    A[原始页面内容] --> B[结构化数据]
    B --> C[本地数据库]
    B --> D[Qdrant向量数据库]
    B --> E[Shopify知识库]
    
    subgraph "本地存储"
        C --> F[产品元数据]
        C --> G[产品知识]
        C --> H[卖点数据]
        C --> I[FAQ数据]
        C --> J[页面元素]
    end
    
    subgraph "向量搜索"
        D --> K[语义搜索]
        D --> L[相似度匹配]
    end
    
    subgraph "线上服务"
        E --> M[Shopify应用]
        E --> N[客户支持]
        E --> O[营销自动化]
    end
```

### 5.2 数据同步策略

- **实时同步**：产品知识、页面元素等，挖掘完成后，在挖掘流程中自动同步
- **条件同步**：卖点、顾虑点、FAQ等需要人工审核的数据, 标注为 `READY` 状态后，由标注人员通过界面手动触发同步到知识库

## 6. 需要人工标注的数据

### 6.1 标注数据类型

根据代码分析，以下数据需要人工标注：

1. **产品卖点 (Selling Points)**
   - 状态：`READY`（已审核）才能同步到知识库
   - 标注内容：卖点名称、描述、营销文案

2. **顾虑点 (Concern Points)**
   - 状态：`READY`（已审核）才能同步到知识库
   - 标注内容：顾虑点名称、描述、营销文案

3. **产品FAQ**
   - 状态：`READY`（已审核）才能同步到知识库
   - 标注内容：问题、答案

### 6.2 标注流程

```mermaid
flowchart TD
    A[数据自动提取] --> B[进入待审核状态]
    B --> C[人工审核标注]
    C --> D{审核通过?}
    D -->|是| E[状态更新为READY]
    D -->|否| F[状态更新为DELETED或ARCHIVED]
    E --> G[自动同步到知识库]
    F --> H[返回重新处理]
    
    subgraph "标注界面"
        I[产品卖点编辑器]
        J[产品顾虑点编辑器]
        K[产品FAQ编辑器]
    end
    
    C --> I
    C --> J
    C --> K
```

## 7. 流程完成/失败通知

### 7.1 成功通知

当工作流成功完成，且关联了 shopify knowledge task， 将通过 HTTP 回调 shopify-knowledge 服务告知关联的 shopify knowledge task 已完成.

### 7.2 失败通知

当工作流执行失败时，且关联了 shopify knowledge task，将通过 HTTP 回调 shopify-knowledge 服务告知关联的 shopify knowledge task 执行失败。

## 8. 总结

Shopify 站点数据挖掘流程是一个完整的、可配置的自动化系统：

1. **触发灵活**：支持多种API接口触发，可指定挖掘策略
2. **流程完整**：从数据爬取到知识提取，再到数据同步的完整链路
3. **性能优化**：通过重复挖掘处理策略，避免重复计算，控制资源开销
4. **质量保证**：关键数据需要人工审核，确保数据质量
5. **监控完善**：完整的成功/失败通知机制，便于任务管理
6. **可扩展性**：基于 Celery 的异步架构，支持大规模并发处理
