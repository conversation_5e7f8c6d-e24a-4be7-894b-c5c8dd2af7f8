import asyncio
from typing import List, Dict, Optional, Any
from typing_extensions import override
from qdrant_client.models import (
    Distance,
    VectorParams,
    SparseVectorParams,
    MultiVectorConfig,
    MultiVectorComparator,
)
import uuid
from datetime import datetime
from pydantic import BaseModel, Field
from crawl2.qdrant_index.base_qdrant_indexer import BaseQdrantIndexer, IndexConfig
from qdrant_client.http.models import Filter, FieldCondition, MatchValue
from loguru import logger


class ProductDetailPoint(BaseModel):
    org_id: str = ""
    url: str = ""
    product_id: str = ""
    product_title: str = ""
    product_type: str = ""
    product_tags: str = ""
    product_page: str = ""
    variant_info: Optional[Dict[str, Any]] = None
    property_values: Optional[Dict[str, Any]] = None
    product_summary: Optional[str] = None
    store_domain: str = ""
    title: str = ""
    content: str = ""
    label: str = ""
    source: str = ""
    level: str = "SPU"
    sku_id_list: List[str] = Field(default_factory=list)
    spu_group_id: str = ""  # 新增字段，默认空字符串
    is_direct_answer: int = 0
    knowledge_id: Optional[str] = None
    image_urls: Optional[List[str]] = None  # 用来维护图片URL列表
    source_detail: Optional[str] = None  # 用来存储从哪一个图片链接抽取的知识
    extra_questions: List[str] = Field(default_factory=list)  # 新增字段，默认空列表
    created_at: int = Field(default_factory=lambda: int(datetime.now().timestamp()))


class ProductDetailPointIndex(BaseQdrantIndexer[ProductDetailPoint]):
    def __init__(self, config: IndexConfig):
        super().__init__(config)

    @override
    async def generate_vectors(self, docs: List[ProductDetailPoint]) -> List[Dict[str, Any]]:
        document_texts = [f"{doc.title} {doc.content}" for doc in docs]
        product_titles = [doc.product_title for doc in docs]
        product_title_embeddings = await self.to_dense_embedding(product_titles)
        product_title_sparse_embeddings = await self.to_sparse_embedding(product_titles)
        document_embeddings = await self.to_dense_embedding(document_texts)
        document_sparse_embeddings = await self.to_sparse_embedding(document_texts)

        # 为extra_questions生成dense向量
        extra_questions_vectors = await asyncio.gather(*[self.to_dense_embedding(doc.extra_questions) for doc in docs])

        res = []
        for doc_dense, doc_sparse, title_dense, title_sparse, extra_questions_vectors in zip(
                document_embeddings,
                document_sparse_embeddings,
                product_title_embeddings,
                product_title_sparse_embeddings,
                extra_questions_vectors
        ):
            if len(extra_questions_vectors) > 0:
                res.append(
                    {
                        "document_dense": doc_dense,
                        "document_sparse": doc_sparse,
                        "product_title_dense": title_dense,
                        "product_title_sparse": title_sparse,
                        "extra-questions-dense": extra_questions_vectors,
                    }
                )
            else:
                res.append(
                    {
                        "document_dense": doc_dense,
                        "document_sparse": doc_sparse,
                        "product_title_dense": title_dense,
                        "product_title_sparse": title_sparse,
                    }
                )
        return res

    @override
    def schema_to_payload(self, schema: ProductDetailPoint) -> Dict[str, Any]:
        meta_dict = schema.model_dump()
        if meta_dict.get("product_type"):
            meta_dict["product_type"] = meta_dict["product_type"].lower()
        return meta_dict

    @override
    def get_id(self, schema: ProductDetailPoint) -> str:
        return str(uuid.uuid4())

    @property
    @override
    def vectors_config(self) -> Dict[str, Any]:
        return {
            "document_dense": VectorParams(
                size=self.config.vector_size, distance=Distance.COSINE
            ),
            "product_title_dense": VectorParams(
                size=self.config.vector_size, distance=Distance.COSINE
            ),
            "extra-questions-dense": VectorParams(
                size=self.config.vector_size,
                distance=Distance.COSINE,
                multivector_config=MultiVectorConfig(
                    comparator=MultiVectorComparator.MAX_SIM
                ),
                on_disk=True,
            ),
        }

    @property
    @override
    def sparse_vectors_config(self) -> Dict[str, Any]:
        return {
            "document_sparse": SparseVectorParams(),
            "product_title_sparse": SparseVectorParams(),
        }

    @property
    @override
    def payload_indexes(self) -> Dict[str, str]:
        return {
            "org_id": "keyword",
            "store_domain": "keyword",
            "product_id": "keyword",
            "product_type": "keyword",
            "label": "keyword",
            "source": "keyword",
            "source_detail": "keyword",
            "knowledge_id": "keyword",
            "spu_group_id": "keyword",
            "created_at": "integer",  # 添加时间戳索引
        }

    async def delete_by_knowledge_id(self, knowledge_id: str, source: str):
        """根据knowledge_id删除所有相关数据"""
        try:
            # 检查collection是否存在
            # if not await self.collection_exists():
            #     logger.info(f"Collection {self.config.collection_name} 不存在，跳过删除操作")
            #     return
            await self.qdrant.delete(
                collection_name=self.config.collection_name,
                points_selector=Filter(
                    must=[
                        FieldCondition(
                            key="knowledge_id",
                            match=MatchValue(value=knowledge_id)
                        ),
                        FieldCondition(
                            key="source", match=MatchValue(value=source)
                        )
                    ]
                ),
            )
            logger.info(f"Deleted all data for knowledge_id: {knowledge_id} and source: {source}")
        except Exception as e:
            logger.error(f"Error deleting data for knowledge_id {knowledge_id} and source: {source}: {e}")
            raise

    async def delete_single_product_knowledge_with_source(self, store_domain: str, product_id: str,
                                                          source: str, spu_group_id: str = None):
        """删除指定store_domain的指定product_id的所有数据"""
        try:
            # 检查collection是否存在
            # if not await self.collection_exists():
            #     logger.info(f"Collection {self.config.collection_name} 不存在，跳过删除操作")
            #     return

            # 构建过滤条件
            filter_conditions = [
                FieldCondition(
                    key="store_domain",
                    match=MatchValue(value=store_domain),
                ),
                FieldCondition(
                    key="product_id", match=MatchValue(value=product_id)
                ),
                FieldCondition(
                    key="source", match=MatchValue(value=source)
                ),
            ]

            # 如果提供了spu_group_id，则添加SPU组过滤条件
            if spu_group_id:
                filter_conditions.append(
                    FieldCondition(
                        key="spu_group_id", match=MatchValue(value=spu_group_id)
                    )
                )

            await self.qdrant.delete(
                collection_name=self.config.collection_name,
                points_selector=Filter(must=filter_conditions),
            )

            if spu_group_id:
                logger.info(
                    f"Deleted all data for store_domain: {store_domain}, product_id: {product_id}, "
                    f"source: {source}, spu_group_id: {spu_group_id}"
                )
            else:
                logger.info(
                    f"Deleted all data for store_domain: {store_domain} and "
                    f"product_id: {product_id} and source: {source}"
                )
        except Exception as e:
            if spu_group_id:
                logger.error(
                    f"Error deleting data for store_domain {store_domain}, product_id {product_id}, "
                    f"source: {source}, spu_group_id: {spu_group_id}: {e}"
                )
            else:
                logger.error(
                    f"Error deleting data for store_domain {store_domain} and product_id {product_id} "
                    f"and source: {source}: {e}"
                )
            raise

    async def delete_single_product_knowledge_with_source_detail(self, store_domain: str, product_id: str,
                                                                 source: str, source_detail: str,
                                                                 spu_group_id: str = None):
        """删除指定store_domain、product_id、source和source_detail的数据"""
        try:
            # 检查collection是否存在
            # if not await self.collection_exists():
            #     logger.info(f"Collection {self.config.collection_name} 不存在，跳过删除操作")
            #     return

            # 构建过滤条件
            filter_conditions = [
                FieldCondition(
                    key="store_domain",
                    match=MatchValue(value=store_domain),
                ),
                FieldCondition(
                    key="product_id", match=MatchValue(value=product_id)
                ),
                FieldCondition(
                    key="source", match=MatchValue(value=source)
                ),
                FieldCondition(
                    key="source_detail", match=MatchValue(value=source_detail)
                ),
            ]

            # 如果提供了spu_group_id，则添加SPU组过滤条件
            if spu_group_id:
                filter_conditions.append(
                    FieldCondition(
                        key="spu_group_id", match=MatchValue(value=spu_group_id)
                    )
                )

            await self.qdrant.delete(
                collection_name=self.config.collection_name,
                points_selector=Filter(must=filter_conditions),
            )

            if spu_group_id:
                logger.info(
                    f"Deleted data for store_domain: {store_domain}, product_id: {product_id}, "
                    f"source: {source}, source_detail: {source_detail}, spu_group_id: {spu_group_id}"
                )
            else:
                logger.info(
                    f"Deleted data for store_domain: {store_domain}, product_id: {product_id}, "
                    f"source: {source}, source_detail: {source_detail}"
                )
        except Exception as e:
            if spu_group_id:
                logger.error(
                    f"Error deleting data for store_domain {store_domain}, product_id {product_id}, "
                    f"source: {source}, source_detail: {source_detail}, spu_group_id: {spu_group_id}: {e}"
                )
            else:
                logger.error(
                    f"Error deleting data for store_domain {store_domain}, product_id {product_id}, "
                    f"source: {source}, source_detail: {source_detail}: {e}"
                )
            raise

    async def delete_by_org_id_and_source(self, org_id: str, source: Optional[str] = None):
        """
        根据org_id和可选的source删除相关数据
        :param org_id: 组织ID
        :param source: 数据源标识，如果为None则只按org_id删除
        """
        try:
            # 检查collection是否存在
            # if not await self.collection_exists():
            #     logger.info(f"Collection {self.config.collection_name} 不存在，跳过删除操作")
            #     return

            # 构建过滤条件
            filter_conditions = [
                FieldCondition(
                    key="org_id",
                    match=MatchValue(value=org_id)
                )
            ]

            # 如果指定了source，则添加source条件
            if source is not None:
                filter_conditions.append(
                    FieldCondition(
                        key="source",
                        match=MatchValue(value=source)
                    )
                )
                logger.info(f"Deleting data for org_id: {org_id} and source: {source}")
            else:
                logger.info(f"Deleting all data for org_id: {org_id}")

            await self.qdrant.delete(
                collection_name=self.config.collection_name,
                points_selector=Filter(must=filter_conditions),
            )

            if source is not None:
                logger.info(f"Deleted all data for org_id: {org_id} and source: {source}")
            else:
                logger.info(f"Deleted all data for org_id: {org_id}")
        except Exception as e:
            if source is not None:
                logger.error(f"Error deleting data for org_id {org_id} and source: {source}: {e}")
            else:
                logger.error(f"Error deleting data for org_id {org_id}: {e}")
            raise
