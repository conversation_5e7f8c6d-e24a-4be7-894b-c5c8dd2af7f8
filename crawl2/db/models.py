"""
****  如果你需要修改 models *********
请按照以下顺序生成对应的数据库迁移文件:

1. 修改 models.py 文件。
2. 运行以下命令生成迁移文件: aerich migrate
3. 将你手动对 models.py 做的修改，以及 aerich migrate 生成的文件一起提交到代码仓库
"""
from enum import Enum

from tortoise import Model, fields
from tortoise.fields import CharEnumField


class CrawlStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"


class SellingPointStatus(str, Enum):
    FRESH = "fresh"
    EDITING = "editing"
    DISABLED = "disabled"
    READY = "ready"
    ARCHIVED = "archived"


class FileProcessStatus(str, Enum):
    PENDING = "pending"
    MARKDOWN_EXTRACTED = "markdown_extracted"
    MARKDOWN_EXTRACT_FAILED = "markdown_extract_failed"
    KNOWLEDGE_EXTRACTED = "knowledge_extracted"
    KNOWLEDGE_EXTRACT_FAILED = "knowledge_extract_failed"


class PageElementStatus(str, Enum):
    FRESH = "fresh"
    EDITED = "edited"


class CrawlStatusMixin(Model):
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    last_crawled_at = fields.DatetimeField(null=True, auto_now=True, description="最后一次尝试时间")
    crawl_status = CharEnumField(CrawlStatus, default=CrawlStatus.PENDING)
    crawl_attempts = fields.IntField(default=0, description="尝试次数")
    crawl_error = fields.CharField(max_length=1024, null=True, description="最后一次尝试的错误信息")

    class Meta:
        abstract = True
        indexes = ["created_at", "crawl_status"]


class DataVersion(Model):
    """数据版本表，用于追踪标注数据的版本历史"""
    id = fields.IntField(pk=True)
    prev_id = fields.IntField(null=True, description="上一个版本的ID")
    value = fields.JSONField(description="当前版本的值")
    data_type = fields.CharField(max_length=255, description="数据类型，如 product_selling_point")
    data_id = fields.IntField(description="对应数据的ID")
    comment = fields.TextField(null=True, description="用户输入的标注原因")
    created_at = fields.DatetimeField(auto_now_add=True, description="版本创建时间")

    class Meta:
        table = "data_version"
        indexes = ["data_type", "data_id", "created_at"]


class CeleryTaskStatus:
    PENDING = 'PENDING'
    STARTED = 'STARTED'
    SUCCESS = 'SUCCESS'
    FAILED = 'FAILED'
    CANCELED = 'CANCELED'
    RETRIED = 'RETRIED'


class CeleryTask(Model):
    """Celery 任务记录."""
    id = fields.IntField(pk=True)
    task_id = fields.CharField(max_length=255, unique=True, description="Celery 任务 ID")
    name = fields.CharField(max_length=255, description="Celery 任务名称")
    status = fields.CharField(max_length=255, default=CeleryTaskStatus.PENDING, description="任务状态")
    params = fields.JSONField(default=dict, description="任务参数")
    result = fields.TextField(null=True, description="任务结果")
    error = fields.TextField(null=True, description="错误信息")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    finished_at = fields.DatetimeField(null=True, description="完成时间")
    workflow = fields.ForeignKeyField('models.CeleryWorkflow', related_name='tasks', null=True, description="关联的工作流 ID")

    class Meta:
        table = "celery_task"
        indexes = ["task_id", "name", "status"]


class CeleryWorkflowStatus:
    PENDING = 'PENDING'
    RUNNING = 'RUNNING'
    SUCCESS = 'SUCCESS'
    FAILED = 'FAILED'
    CANCELED = 'CANCELED'


class CeleryWorkflow(Model):
    """Celery 工作流记录."""
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=1024, description="工作流名称")
    status = fields.CharField(max_length=255, default=CeleryWorkflowStatus.PENDING, description="工作流状态")
    signature = fields.JSONField(default=list, description="这个工作流的 schema，用于描述工作流的执行步骤")
    tasks = fields.ReverseRelation["CeleryTask"]
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="最后更新时间")
    finished_at = fields.DatetimeField(null=True, description="完成时间")

    class Meta:
        table = "celery_workflow"
        indexes = ["name", "status", "created_at"]


class TqdmRecord(Model):
    """Tqdm 进度条记录."""
    id = fields.IntField(pk=True)
    relate_id = fields.IntField(description="关联的任务 ID, 如 Celery 任务 ID 或工作流 ID")
    relate_type = fields.CharField(max_length=50, description="关联类型, 如 'celery_task', 'celery_workflow' 等")
    total = fields.IntField(description="总数")
    n = fields.IntField(default=0, description="当前进度")
    desc = fields.CharField(max_length=255, null=True, description="描述")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="最后更新时间")

    class Meta:
        table = "tqdm_record"
        indexes = ["relate_id", "relate_type"]


class ShopifySite(CrawlStatusMixin):
    """和一个 Shopify 站点相关, 但和某个具体商品直接关联的元数据."""
    id = fields.IntField(pk=True)
    domain = fields.CharField(max_length=255, unique=True, description="shopify 站点 domain, 仅包含域名中不含 www 的部分")
    shopify_domain = fields.CharField(max_length=255, default='', description="以 myshopify.com 结尾的完整 shopify 站点域名")
    product_urls = fields.JSONField(default=list, description="产品页面的 URL 列表")
    collection_urls = fields.JSONField(default=list, description="产品collection页面的 URL 列表")
    pages_urls = fields.JSONField(default=list, description="自定义页面的 URL 列表")
    blogs_urls = fields.JSONField(default=list, description="博客页面的 URL 列表")

    # 挖掘策略相关字段
    crawl_strategy = fields.JSONField(null=True, description="当前生效的挖掘策略，为 null 时使用默认策略")
    strategy_updated_at = fields.DatetimeField(null=True, description="策略更新时间")

    # Relationships
    products: fields.ReverseRelation["ProductMetadata"]
    product_type_aspects: fields.ReverseRelation["ProductTypeAspects"]
    product_type_expanded_aspects: fields.ReverseRelation["ProductTypeExpandedAspects"]
    product_type_concerns: fields.ReverseRelation["ProductTypeConcerns"]
    product_type_expanded_concerns: fields.ReverseRelation["ProductTypeExpandedConcerns"]
    shopify_pages: fields.ReverseRelation["ShopifyPage"]
    page_content_summaries: fields.ReverseRelation["PageContentSummary"]

    class Meta:
        table = "shopify_site"
        indexes = ["domain"]


class ProductMetadata(CrawlStatusMixin):
    """产品页面的原始内容，用于后续的知识抽取和数据挖掘, 注意不包含用 AI 抽取的二级信息."""
    id = fields.IntField(pk=True)
    url = fields.CharField(max_length=1024, unique=True, description="产品页面的 URL")
    product_id = fields.CharField(max_length=255, description="产品 ID, 与 Shopify 站点的产品 ID 一致, 与表中的 id 字段无关")
    product_type = fields.CharField(max_length=255, description="产品类型, 由 Shopify 站点提供,如果没有则通过大模型推断")
    title = fields.TextField(description="产品标题")
    price = fields.CharField(max_length=255, description="产品价格, 我们对具体金额暂时不关心，所以用字符串表示")
    tags = fields.TextField(default="", description="产品标签, 用逗号分隔")
    vendor = fields.CharField(max_length=255, default="", description="产品供应商")
    variant_info = fields.JSONField(default=dict, description="产品变体信息")
    site = fields.ForeignKeyField("models.ShopifySite", related_name="products")

    # Relationships
    product_raw_pages: fields.ReverseRelation["ProductRawPage"]
    product_knowledge: fields.ReverseRelation["ProductKnowledge"]
    selling_points: fields.ReverseRelation["ProductSellingPoint"]
    concern_points: fields.ReverseRelation["ProductConcernPoint"]
    search_enhancements: fields.ReverseRelation["ProductSearchEnhancement"]
    property_and_value: fields.ReverseRelation["ProductPropertyAndValue"]
    summary: fields.ReverseRelation["ProductSummary"]
    scenario_selling_points: fields.ReverseRelation["ProductScenarioSellingPoint"]

    class Meta:
        table = "product_metadata"
        indexes = ["url", "product_id", "site_id"]


class CollectionMetadata(CrawlStatusMixin):
    """产品collection页面的原始内容，用于后续的知识抽取和数据挖掘, 注意不包含用 AI 抽取的二级信息."""
    id = fields.IntField(pk=True)
    url = fields.CharField(max_length=1024, unique=True, description="产品collection页面的 URL")
    collection_id = fields.CharField(max_length=255, description="产品collection ID, 与 Shopify 站点的产品collection ID 一致, 与表中的 id 字段无关")
    title = fields.TextField(description="产品collection标题")
    description = fields.TextField(description="产品collection描述")
    product_ids = fields.JSONField(default=list, description="产品collection中的产品 ID 列表")
    raw_content = fields.TextField(description="collection页面原始 Markdown 内容")
    site = fields.ForeignKeyField("models.ShopifySite", related_name="collections")

    class Meta:
        table = "collection_metadata"
        indexes = ["url", "collection_id", "site_id"]


class ProductRawPage(CrawlStatusMixin):
    """产品页面的原始内容，用于后续的知识抽取和数据挖掘, 注意不包含用 AI 抽取的二级信息."""
    id = fields.IntField(pk=True)
    metadata = fields.ForeignKeyField("models.ProductMetadata", related_name="product_raw_pages")
    html_content = fields.TextField(default="", description="产品页面的原始 HTML 内容")
    markdown_content = fields.TextField(description="产品页面的原始 Markdown 描述")
    extracted_links = fields.JSONField(default=list, description="产品页面上提取的链接")  # List[Link]
    json_objects = fields.JSONField(default=list, description="产品页面上提取的对象")

    class Meta:
        table = "product_raw_page"
        indexes = ["metadata_id"]


class ProductKnowledge(CrawlStatusMixin):
    """产品页面的知识抽取结果"""
    id = fields.IntField(pk=True)
    metadata = fields.ForeignKeyField("models.ProductMetadata", related_name="product_knowledge")
    markdown_content = fields.TextField(description="产品页面的知识抽取结果")
    documents = fields.JSONField(default=list, description="产品页面的知识抽取结果的 JSON 对象")  # List[KnowledgeTopicContent]
    source = fields.CharField(max_length=50, default="product_page", description="来源类型")

    class Meta:
        table = "product_knowledge"
        indexes = ["metadata_id"]

class ProductPropertyAndValue(CrawlStatusMixin):
    """产品页面的属性值抽取结果"""
    id = fields.IntField(pk=True)
    metadata = fields.ForeignKeyField("models.ProductMetadata", related_name="product_property_and_value")
    property_values = fields.JSONField(default=dict, description="产品页面的属性值抽取结果")
    class Meta:
        table = "product_property_and_value"
        indexes = ["metadata_id"]


class ProductTypeAspects(CrawlStatusMixin):
    """产品类型的属性点"""
    id = fields.IntField(pk=True)
    site = fields.ForeignKeyField("models.ShopifySite", related_name="product_type_aspects")
    product_type = fields.CharField(max_length=255, description="网站展示的字面产品类型")
    llm_determined_product_type = fields.CharField(max_length=255, description="LLM 推断的产品类型")
    aspects = fields.JSONField(default=list, description="产品类型的属性点")  # List[NameAndDescription]

    class Meta:
        table = "product_type_aspects"
        indexes = ["site_id"]


class ProductTypeScenarios(CrawlStatusMixin):
    """产品类型的使用场景"""
    id = fields.IntField(pk=True)
    product_type = fields.CharField(max_length=255, description="网站展示的字面产品类型")
    scenarios = fields.JSONField(default=list, description="产品类型的使用场景")

    class Meta:
        table = "product_type_scenarios"
        indexes = ["product_type"]


class ProductScenarioSellingPoint(CrawlStatusMixin):
    id = fields.IntField(pk=True)
    product = fields.ForeignKeyField("models.ProductMetadata", related_name="scenario_selling_points")
    scenario = fields.CharField(max_length=255, description="场景")
    content = fields.TextField(description="场景卖点内容")
    elements = fields.ManyToManyField(
        "models.PageElement",
        related_name="scenario_selling_points",  # 反向关系名称
        through="page_element_scenario_selling_point"  # 中间表名称
    )

    class Meta:
        table = "product_scenario_selling_point"
        indexes = ["product_id"]


class ProductTypeExpandedAspects(CrawlStatusMixin):
    """产品类型的扩展后属性点"""
    id = fields.IntField(pk=True)
    site = fields.ForeignKeyField("models.ShopifySite", related_name="product_type_expanded_aspects")
    product_type = fields.CharField(max_length=255, description="网站展示的字面产品类型")
    llm_determined_product_type = fields.CharField(max_length=255, description="LLM 推断的产品类型")
    aspects = fields.JSONField(default=list, description="产品类型的属性点")  # List[NameAndDescription]

    class Meta:
        table = "product_type_expanded_aspects"
        indexes = ["site_id"]


class ProductTypeConcerns(CrawlStatusMixin):
    """产品类型的顾虑点"""
    id = fields.IntField(pk=True)
    site = fields.ForeignKeyField("models.ShopifySite", related_name="product_type_concerns")
    product_type = fields.CharField(max_length=255, description="网站展示的字面产品类型")
    llm_determined_product_type = fields.CharField(max_length=255, description="LLM 推断的产品类型")
    concerns = fields.JSONField(default=list, description="产品类型的顾虑点")  # List[NameAndDescription]

    class Meta:
        table = "product_type_concerns"
        indexes = ["site_id"]


class ProductTypeExpandedConcerns(CrawlStatusMixin):
    """产品类型的扩展后顾虑点"""
    id = fields.IntField(pk=True)
    site = fields.ForeignKeyField("models.ShopifySite", related_name="product_type_expanded_concerns")
    product_type = fields.CharField(max_length=255, description="网站展示的字面产品类型")
    llm_determined_product_type = fields.CharField(max_length=255, description="LLM 推断的产品类型")
    concerns = fields.JSONField(default=list, description="产品类型的顾虑点")  # List[NameAndDescription]

    class Meta:
        table = "product_type_expanded_concerns"
        indexes = ["site_id"]


class ProductSellingPoint(CrawlStatusMixin):
    id = fields.IntField(pk=True)
    product = fields.ForeignKeyField("models.ProductMetadata", related_name="selling_points")
    name = fields.CharField(max_length=255, description="卖点名称")
    description = fields.TextField(description="卖点描述")
    marketing_copies = fields.JSONField(default=list, description="营销文案")
    name_chn = fields.CharField(max_length=255, null=True, description="卖点名称中文翻译")
    description_chn = fields.TextField(null=True, description="卖点描述中文翻译")
    marketing_copies_chn = fields.JSONField(default=list, description="营销文案中文翻译")
    status = CharEnumField(SellingPointStatus, default=SellingPointStatus.FRESH, description="标注状态")
    last_edited_at = fields.DatetimeField(null=True, description="上次标注时间")
    version = fields.ForeignKeyField("models.DataVersion", null=True, related_name="selling_points", description="当前版本")
    elements = fields.ManyToManyField(
        "models.PageElement",
        related_name="selling_points",  # 反向关系名称
        through="page_element_selling_point"  # 中间表名称
    )

    class Meta:
        table = "product_selling_point"
        indexes = ["product_id", "status"]


class ProductConcernPoint(CrawlStatusMixin):
    id = fields.IntField(pk=True)
    product = fields.ForeignKeyField("models.ProductMetadata", related_name="concern_points")
    name = fields.CharField(max_length=255, description="顾虑点名称")
    name_chn = fields.CharField(max_length=255, null=True, description="顾虑点名称中文翻译")
    marketing_copies = fields.JSONField(default=list, description="营销文案")
    marketing_copies_chn = fields.JSONField(default=list, null=True, description="营销文案中文翻译")
    status = CharEnumField(SellingPointStatus, default=SellingPointStatus.FRESH, description="标注状态")
    last_edited_at = fields.DatetimeField(null=True, description="上次标注时间")
    version = fields.ForeignKeyField("models.DataVersion", null=True, related_name="concern_points",
                                     description="当前版本")

    class Meta:
        table = "product_concern_point"
        indexes = ["product_id", "status"]


class ProductFaqs(CrawlStatusMixin):
    """产品FAQ"""
    id = fields.IntField(pk=True)
    product = fields.ForeignKeyField("models.ProductMetadata", related_name="faqs")
    faqs = fields.JSONField(default=list, description="常见问题和解答")  # List[Faq]
    status = CharEnumField(SellingPointStatus, default=SellingPointStatus.FRESH, description="标注状态")
    last_edited_at = fields.DatetimeField(null=True, description="上次标注时间")
    version = fields.ForeignKeyField("models.DataVersion", null=True, related_name="faqs", description="当前版本")

    class Meta:
        table = "product_faqs"
        indexes = ["product_id", "status"]


class ProductSearchEnhancement(CrawlStatusMixin):
    """产品搜索增强"""
    id = fields.IntField(pk=True)
    product = fields.ForeignKeyField("models.ProductMetadata", related_name="search_enhancements")
    new_title = fields.CharField(max_length=512, description="增强后的标题")
    new_description = fields.TextField(description="增强后的描述")
    new_product_type = fields.CharField(max_length=255, description="增强后的产品类型")
    filterable_attributes = fields.JSONField(default=dict, description="增强后的可过滤属性")

    class Meta:
        table = "product_search_enhancement"
        indexes = ["product_id"]


class ProductTypePvSchema(CrawlStatusMixin):
    """产品类型的属性点"""
    id = fields.IntField(pk=True)
    site = fields.ForeignKeyField("models.ShopifySite", related_name="product_type_pv_schemas")
    product_type = fields.CharField(max_length=255, description="网站展示的字面产品类型")
    properties = fields.JSONField(default=dict, description="产品类型的属性点")

    class Meta:
        table = "product_type_pv_schema"
        indexes = ["site_id", "product_type"]


class PageMetadata(CrawlStatusMixin):
    id = fields.IntField(pk=True)
    url = fields.CharField(max_length=1024, unique=True, description="页面的 URL")
    raw_content = fields.TextField(description="页面原始 Markdown 内容")
    site = fields.ForeignKeyField("models.ShopifySite", related_name="pages")

    class Meta:
        table = "page_metadata"
        indexes = ["url", "site_id"]


class BlogMetadata(CrawlStatusMixin):
    id = fields.IntField(pk=True)
    url = fields.CharField(max_length=1024, unique=True, description="博客的 URL")
    raw_content = fields.TextField(description="博客原始 Markdown 内容")
    site = fields.ForeignKeyField("models.ShopifySite", related_name="blogs")

    class Meta:
        table = "blog_metadata"
        indexes = ["url", "site_id"]


class ProductKeyword(CrawlStatusMixin):
    """产品关键词"""
    id = fields.IntField(pk=True)
    product = fields.ForeignKeyField("models.ProductMetadata", related_name="keywords")
    keywords = fields.JSONField(default=list, description="产品关键词,用来做指代的理解")

    class Meta:
        table = "product_keyword"
        indexes = ["product_id"]


class ProductSummary(CrawlStatusMixin):
    """产品摘要"""
    id = fields.IntField(pk=True)
    product = fields.ForeignKeyField("models.ProductMetadata", related_name="summary")
    summary = fields.TextField(description="产品摘要, 用于描述产品的主要特点和卖点, 主要在推荐子agent中使用")

    class Meta:
        table = "product_summary"
        indexes = ["product_id"]


class ExtractStatusMixin(Model):
    gmt_create = fields.DatetimeField(auto_now_add=True, description="创建时间")
    gmt_modified = fields.DatetimeField(null=True, auto_now=True, description="最后更新时间")

    class Meta:
        abstract = True
        indexes = ["gmt_create"]


class StoreKnowledgePoint(ExtractStatusMixin):
    """
    店铺知识点表
    """
    id = fields.IntField(pk=True, description='自增主键 ID')
    point_id = fields.CharField(max_length=36, unique=True, description='知识点业务 ID (UUID)')
    topic = fields.CharField(null=True, max_length=255, description='知识点主题')
    question = fields.TextField(description='问题')
    answer = fields.TextField(description='答案')
    source = fields.CharField(max_length=50, description='来源类型')
    source_detail = fields.TextField(null=True, description='来源详情')
    quality = fields.IntField(default=1, description='是否启用，1: 启用，0: 禁用')
    label = fields.CharField(null=True, max_length=255, description='标签')
    detailed_label = fields.CharField(null=True, max_length=255, description='详细标签')
    extra_questions = fields.JSONField(null=True, description='问题的其他表达方式列表')
    is_deleted = fields.IntField(default=0, description='逻辑删除标志 (0: 未删除, 1: 已删除)')
    site = fields.ForeignKeyField("models.ShopifySite", related_name="store_knowledge_point")

    class Meta:
        table = "store_knowledge_points"
        indexes = ["point_id", "site_id"]


class StoreKnowledgeValue(ExtractStatusMixin):
    """
    店铺知识归纳值表
    """
    id = fields.IntField(pk=True, description='自增主键 ID')
    label = fields.CharField(max_length=255, description='标签')
    detailed_label = fields.CharField(max_length=255, description='详细标签')
    value = fields.TextField(description='归纳值')
    site = fields.ForeignKeyField("models.ShopifySite", related_name="store_knowledge_value")

    class Meta:
        table = "store_knowledge_value"
        indexes = ["label", "detailed_label", "site_id"]
        unique_together = ("label", "detailed_label", "site_id")


class StoreDocumentFAQ(CrawlStatusMixin):
    """商家文档知识"""
    id = fields.IntField(pk=True)
    site = fields.ForeignKeyField("models.ShopifySite", related_name="document_faqs")
    file_url = fields.CharField(max_length=1024, description="文件url路径")
    markdown_content = fields.TextField(description="文件的 Markdown 文本")
    faqs = fields.JSONField(default=list, description="大模型抽取的 faqs")  # List[NameAndDescription]

    class Meta:
        table = "store_document_faqs"
        indexes = ["site_id"]


class CollectionKnowledgePoint(ExtractStatusMixin):
    """
    collection知识点表
    """
    id = fields.IntField(pk=True, description='自增主键 ID')
    point_id = fields.CharField(max_length=36, unique=True, description='知识点业务 ID (UUID)')
    topic = fields.CharField(null=True, max_length=255, description='知识点主题')
    title = fields.CharField(max_length=255, description='知识点标题')
    content = fields.TextField(description='知识点内容')
    collection_id = fields.CharField(max_length=255, description='Collection ID')
    source = fields.CharField(max_length=50, description='来源类型')
    source_detail = fields.TextField(null=True, description='来源详情')
    label = fields.CharField(null=True, max_length=255, description='标签')
    site = fields.ForeignKeyField("models.ShopifySite", related_name="collection_knowledge_points")

    class Meta:
        table = "collection_knowledge_points"
        indexes = ["point_id", "collection_id", "site_id"]


class ParseFileTask(Model):
    """
    文件解析任务记录.
    """
    id = fields.IntField(pk=True, description='自增主键 ID')
    task_id = fields.CharField(max_length=256, unique=True, description='shopify-knowledge 的任务 ID, 同步任务状态时需要用')
    file_key = fields.CharField(max_length=512, description='待解析文件的 id 类信息，读取文件内容时，需要向 store-center 申请文件下载链接.')
    site = fields.ForeignKeyField("models.ShopifySite", related_name="parse_file_tasks", description='关联的 Shopify 站点')
    product_ids = fields.JSONField(default=list, description='任务处理的产品 ID 列表, 用于关联到具体的产品知识点')
    knowledge_type = fields.CharField(max_length=50, description='知识类型, 如 PRODUCT, STORE 等')
    markdown = fields.TextField(default='', description='通过 AI 从文件内容中提取出来的 Markdown 格式文本, 这部分文本将用于进一步知识挖掘')
    status = fields.CharEnumField(FileProcessStatus, default=FileProcessStatus.PENDING, description='任务状态')
    created_at = fields.DatetimeField(auto_now_add=True, description='任务创建时间')
    updated_at = fields.DatetimeField(auto_now=True, description='任务最后更新时间')

    class Meta:
        table = "parse_file_tasks"
        indexes = ["task_id", "status", "created_at"]


class ShopifyPage(CrawlStatusMixin):
    """和一个 shopify 站点相关，区别于 ShopifySite 这里维护的是页面渲染模板的元数据"""
    id = fields.IntField(pk=True)
    site = fields.ForeignKeyField("models.ShopifySite", related_name="shopify_pages")
    page_url = fields.CharField(
        max_length=1024, unique=True,
        description="shopify 页面完整 URL, 如 example.com/products/womens-heated-softshell-vest")
    data = fields.JSONField(default=dict, description="页面额外数据")
    # Relationships
    elements: fields.ReverseRelation["PageElement"]

    class Meta:
        table = "shopify_page"
        indexes = ["page_url"]


class PageContentSummary(Model):
    """和一个 shopify 站点相关，维护的是页面摘要缓存。因为 LLM 对文字生成摘要的结果不稳定，需要维护一个‘长文本’和‘摘要’的关系"""
    site = fields.ForeignKeyField("models.ShopifySite", related_name="page_content_summaries")
    content = fields.TextField()
    summary = fields.TextField()

    class Meta:
        table = "page_content_summary"


class PageElement(Model):
    """页面元素定义"""
    id = fields.IntField(pk=True, description='自增主键 ID')
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="最后更新时间")
    status = CharEnumField(PageElementStatus, default=PageElementStatus.FRESH, description="标注状态")
    page = fields.ForeignKeyField("models.ShopifyPage", related_name="elements")
    target = fields.CharField(max_length=255, description="元素唯一标识，如：相关推荐/推荐商品[1]")
    selectors = fields.JSONField(default=list, description='元素选择器列表, 用于定位页面元素')
    data = fields.JSONField(default=dict, description="用于存储与元素相关的其他信息, 如 element tag")

    class Meta:
        table = "page_elements"
        unique_together = (("page", "target"),)


class User(Model):
    """用户表，存储从SSO获取的用户信息"""
    id = fields.IntField(pk=True)
    username = fields.CharField(max_length=255, unique=True, description="用户名，多为邮箱前缀")
    email = fields.CharField(max_length=255, null=True, description="邮箱")
    full_name = fields.CharField(max_length=255, null=True, description="全名，多为真实姓名")
    phone_number = fields.CharField(max_length=20, null=True, description="手机号")
    is_active = fields.BooleanField(default=True, description="是否激活")
    is_superuser = fields.BooleanField(default=False, description="是否超级用户")
    last_login = fields.DatetimeField(null=True, description="最后登录时间")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    # 扩展字段，用于存储SSO返回的额外信息
    sso_metadata = fields.JSONField(default=dict, description="SSO返回的元数据")

    class Meta:
        table = "users"
        indexes = ["username", "email"]
