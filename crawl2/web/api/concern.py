from io import BytesIO

import pandas as pd
from fastapi import APIRouter, HTTPException
from starlette.responses import StreamingResponse

from crawl2 import schema
from crawl2.db import operations
from crawl2.db.ops import product as product_ops
from crawl2.web.i18n import i18n

router = APIRouter(prefix="/sites", tags=["product_concerns"])


@router.get("/{domain}/concern")
async def list_domain_concerns_paginated(
    domain: str,
    page: int = 1,
    size: int = 2000
) -> schema.PaginatedResponse[schema.ProductMetaAndConcern]:
    """
    获取指定站点的产品顾虑点（分页）
    """
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    return await operations.list_domain_concern_points_paginated(domain, page, size)


@router.put("/{domain}/concerns/{concern_id}")
async def update_concern_with_version(
    domain: str,
    concern_id: int,
    update_request: schema.ProductConcernUpdateRequest
) -> schema.MessageOnlyResponse:
    """更新顾虑点数据并创建版本记录"""
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    success = await operations.update_concern_point_with_version(
        concern_id, update_request
    )

    if not success:
        raise HTTPException(status_code=404, detail="FAQ not found")

    return schema.MessageOnlyResponse(message="FAQ updated successfully with version tracking")


@router.get("/{domain}/concerns/{concern_id}/versions")
async def get_concern_versions(
    domain: str,
    concern_id: int
) -> list[schema.DataVersion]:
    """获取顾虑点的版本历史"""
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    return await operations.get_concern_point_versions(concern_id)


@router.get("/{domain}/concerns/edit_stats")
async def get_concern_point_edit_stats(domain: str)-> schema.EditStatusStats:
    """根据状态筛选卖点"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    return await operations.get_concern_point_edit_stats(domain)


@router.get("/{domain}/concerns/status/{status}")
async def list_concerns_by_status(
    domain: str,
    status: schema.SellingPointStatus
) -> list[schema.ProductMetaAndConcern]:
    """根据状态筛选顾虑点"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    return await operations.list_concerns_by_status(domain, status)


@router.get("/{domain}/concerns/status")
async def list_all_concerns_by_status(
    domain: str
) -> list[schema.ProductMetaAndConcern]:
    """获取所有顾虑点（按状态分组）"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    return await operations.list_concerns_by_status(domain, None)


@router.get("/{domain}/products/{product_id}/concerns")
async def get_product_concerns(
    domain: str,
    product_id: str
) -> list[schema.ProductMetaAndConcern]:
    """获取指定产品的顾虑点信息"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    # 获取产品元数据
    product = await operations.get_product_metadata_by_product_id(domain, product_id)
    if not product:
        raise HTTPException(status_code=404, detail=f"Product {product_id} not found")

    # 获取顾虑点信息
    return await product_ops.get_product_concerns_by_url(product.url)


@router.get("/{domain}/concern.excel")
async def download_concerns(domain: str):
    """下载站点的所有产品顾虑点数据为 Excel 文件"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    # 获取所有产品及其顾虑点
    concerns = await operations.list_domain_concern_points(domain)

    # 准备数据
    data = []
    for cp in concerns:
        for (marketing_copy, marketing_copy_chn) in zip(cp.marketing_copies or [],
                                                        cp.marketing_copies_chn or []):
            data.append({
                i18n('concern_id'): cp.id,
                i18n('product_url'): cp.product_url,
                i18n('product_id'): cp.product_id,
                i18n('product_title'): cp.product_title,
                i18n('product_price'): cp.product_price,
                i18n('product_type'): cp.product_type,
                i18n('concern'): cp.concern,
                i18n('concern_chn'): cp.concern_chn or '',
                i18n('marketing_copies'): marketing_copy,
                i18n('marketing_copies_chn'): marketing_copy_chn,
                i18n('status'): cp.status.value,
                i18n('last_edited_at'): cp.last_edited_at.isoformat() if cp.last_edited_at else '',
                i18n('version_id'): cp.version_id or '',
            })

    # 创建 DataFrame
    df = pd.DataFrame(data)

    # 创建 Excel 文件
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Product Concerns')

    output.seek(0)

    # 返回文件
    return StreamingResponse(
        output,
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers={
            'Content-Disposition': f'attachment; filename="{domain}_product_concerns.xlsx"'
        }
    )
