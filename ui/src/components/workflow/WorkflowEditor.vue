<template>
  <div style="display: inline-block">
    <button v-if="!initialWorkflow" class="bg-green-500 text-white border-none rounded px-4 py-2 cursor-pointer text-sm mx-2 hover:bg-green-600" @click="openDialog">{{ displayName }}</button>
    <teleport to="body">
      <div v-if="dialogVisible" class="fixed inset-0 bg-black bg-opacity-25 z-[2000] flex items-center justify-center">
        <div class="bg-white rounded-lg min-w-[800px] max-w-[90vw] max-h-[90vh] shadow-lg p-0 overflow-hidden flex flex-col">
          <div class="flex justify-between items-center bg-gray-50 p-4 px-6 font-semibold text-lg">
            <span>{{ initialWorkflow ? '编辑工作流' : '创建工作流' }}</span>
            <button class="bg-transparent border-none text-2xl cursor-pointer text-gray-500" @click="closeDialog">×</button>
          </div>
          <div class="p-6 overflow-y-auto flex-1">
            <!-- 工作流基本信息 -->
            <div class="mb-8 pb-4 border-b border-gray-200">
              <div class="flex items-center gap-4 mb-4">
                <label for="workflow-name" class="min-w-[120px] font-medium">工作流名称</label>
                <input
                  v-model="workflowName"
                  type="text"
                  id="workflow-name"
                  placeholder="输入工作流名称"
                  class="flex-1 p-2 border border-gray-300 rounded text-base"
                  required
                />
              </div>
            </div>

            <!-- 任务选择区域 -->
            <div>
              <h3 class="m-0 mb-4 text-gray-800">选择任务</h3>
              <div class="mb-4">
                <input
                  v-model="taskSearchKeyword"
                  type="text"
                  placeholder="搜索任务..."
                  class="w-full p-2 border border-gray-300 rounded text-base"
                />
              </div>
              
              <div class="grid grid-cols-2 gap-8 h-[400px]">
                <!-- 可用任务列表 -->
                <div class="flex flex-col">
                  <h4 class="m-0 mb-4 text-gray-700 text-base">可用任务 ({{ filteredAvailableTasks.length }})</h4>
                  <div class="flex-1 overflow-y-auto border border-gray-200 rounded-md bg-gray-50">
                    <div
                      v-for="task in filteredAvailableTasks"
                      :key="task.name"
                      class="p-4 border-b border-gray-200 cursor-pointer transition-colors duration-200 hover:bg-gray-100"
                      @click="addTaskToWorkflow(task)"
                    >
                      <div class="font-semibold text-gray-700 font-mono text-sm flex-1">{{ task.name }}</div>
                      <div class="text-gray-600 text-sm mb-2 leading-relaxed">{{ task.description }}</div>
                      <div class="text-xs text-gray-500 font-mono">
                        <span v-if="task.params && task.params.properties">
                          {{ Object.keys(task.params.properties).join(', ') }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 已选任务列表 -->
                <div class="flex flex-col">
                  <h4 class="m-0 mb-4 text-gray-700 text-base">工作流任务 ({{ selectedTasks.length }})</h4>
                  <div class="flex-1 overflow-y-auto border border-gray-200 rounded-md bg-gray-50">
                    <div
                      v-for="(task, index) in selectedTasks"
                      :key="`${task.name}-${index}`"
                      class="bg-white border-2 border-green-500 m-2 rounded-md cursor-default hover:bg-white"
                    >
                      <div class="flex items-center gap-2 mb-2 p-4">
                        <span class="bg-green-500 text-white w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold">{{ index + 1 }}</span>
                        <span class="font-semibold text-gray-700 font-mono text-sm flex-1">{{ task.name }}</span>
                        <button 
                          class="bg-red-500 text-white border-none rounded-full w-6 h-6 cursor-pointer text-base flex items-center justify-center hover:bg-red-600"
                          @click="removeTaskFromWorkflow(index)"
                        >
                          ×
                        </button>
                      </div>
                      <div class="text-gray-600 text-sm mb-2 leading-relaxed px-4">{{ task.description }}</div>
                      
                      <!-- 任务参数编辑 -->
                      <div class="mt-2 pt-2 border-t border-gray-200 px-4 pb-4">
                        <div class="flex justify-between items-center mb-2">
                          <span>参数:</span>
                          <button 
                            type="button"
                            class="bg-transparent border-none text-green-500 cursor-pointer text-xs underline"
                            @click="toggleTaskParams(index)"
                          >
                            {{ task.showParams ? '收起' : '展开' }}
                          </button>
                        </div>
                        <div v-if="task.showParams && task.params && task.params.properties" class="bg-gray-50 p-2 rounded">
                          <div 
                            v-for="(paramSchema, paramName) in task.params.properties" 
                            :key="paramName"
                            class="flex items-center gap-2 mb-2"
                          >
                            <label :for="`${task.name}-${paramName}`" class="min-w-[80px] text-xs text-gray-700">{{ paramName }}</label>
                            <input
                              v-model="task.kwargs[paramName]"
                              :type="getInputType(paramSchema)"
                              :id="`${task.name}-${paramName}`"
                              :placeholder="paramName"
                              class="flex-1 p-1 border border-gray-300 rounded text-xs"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex gap-4 mt-8 pt-4 border-t border-gray-200">
              <button 
                type="button" 
                @click="submitWorkflow" 
                :disabled="loading || !workflowName.trim() || selectedTasks.length === 0"
                class="px-5 py-2 border-none rounded text-base cursor-pointer bg-green-500 text-white hover:bg-green-600 disabled:bg-green-300 disabled:cursor-not-allowed"
              >
                {{ loading ? '创建中...' : (initialWorkflow ? '更新工作流' : '创建工作流') }}
              </button>
              <button type="button" @click="closeDialog" class="px-5 py-2 border-none rounded text-base cursor-pointer bg-gray-200 text-gray-800 hover:bg-gray-300">取消</button>
            </div>

            <!-- 状态信息 -->
            <div v-if="error" class="text-red-500 mt-4 p-2 bg-red-50 border border-red-200 rounded">{{ error }}</div>
            <div v-if="success" class="text-green-600 mt-4 p-2 bg-green-50 border border-green-200 rounded">
              工作流已{{ initialWorkflow ? '更新' : '创建' }}！
              <div class="flex gap-4 mt-2 flex-wrap">
                <button 
                  v-if="workflowResponse?.id" 
                  @click="navigateToWorkflow"
                  class="text-blue-600 no-underline font-medium px-2 py-1 rounded bg-gray-50 border border-gray-200 transition-all duration-200 text-sm cursor-pointer inline-block hover:bg-blue-50 hover:border-blue-600"
                >
                  查看工作流详情
                </button>
                <a 
                  v-if="workflowResponse?.id && workflowResponse?.log_url" 
                  :href="workflowResponse.log_url"
                  target="_blank"
                  class="text-blue-600 no-underline font-medium px-2 py-1 rounded bg-gray-50 border border-gray-200 transition-all duration-200 text-sm cursor-pointer inline-block hover:bg-blue-50 hover:border-blue-600"
                >
                  点击查看日志
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </teleport>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { api } from '@/api/api.js'
import { useTasks } from '@/composables/useTasks.js'

const router = useRouter()

// 使用tasks composable
const { taskDefinitions, fetchTaskDefinitions } = useTasks()

const props = defineProps({
  displayName: { type: String, default: '创建工作流' },
  initialWorkflow: { type: Object, default: null },
  initialName: { type: String, default: '' }
})

const emit = defineEmits(['workflow-created'])

const dialogVisible = ref(false)
const loading = ref(false)
const error = ref('')
const success = ref(false)
const workflowResponse = ref(null) // 统一保存工作流响应对象
const workflowName = ref('')
const taskSearchKeyword = ref('')
const availableTasks = ref([])
const selectedTasks = ref([])

// 过滤可用任务
const filteredAvailableTasks = computed(() => {
  if (!taskSearchKeyword.value.trim()) {
    return availableTasks.value
  }
  
  const keywords = taskSearchKeyword.value.toLowerCase().trim().split(/\s+/).filter(k => k.length > 0)
  return availableTasks.value.filter(task => {
    const taskName = task.name?.toLowerCase() || ''
    const taskDescription = task.description?.toLowerCase() || ''
    
    return keywords.every(keyword => 
      taskName.includes(keyword) || taskDescription.includes(keyword)
    )
  })
})

// 获取输入类型
const getInputType = (schema) => {
  if (schema.type === 'number' || schema.type === 'integer') {
    return 'number'
  }
  if (schema.type === 'boolean') {
    return 'checkbox'
  }
  return 'text'
}

// 添加任务到工作流
const addTaskToWorkflow = (task) => {
  const newTask = {
    ...task,
    kwargs: {},
    showParams: false
  }
  
  // 初始化参数
  if (task.params && task.params.properties) {
    Object.keys(task.params.properties).forEach(paramName => {
      newTask.kwargs[paramName] = ''
    })
  }
  
  selectedTasks.value.push(newTask)
}

// 从工作流中移除任务
const removeTaskFromWorkflow = (index) => {
  selectedTasks.value.splice(index, 1)
}

// 切换任务参数显示
const toggleTaskParams = (index) => {
  selectedTasks.value[index].showParams = !selectedTasks.value[index].showParams
}

// 加载可用任务
const loadAvailableTasks = async () => {
  try {
    loading.value = true
    await fetchTaskDefinitions()
    availableTasks.value = taskDefinitions.value
  } catch (err) {
    error.value = '加载任务定义失败'
    console.error('Error loading task definitions:', err)
  } finally {
    loading.value = false
  }
}

// 打开对话框
const openDialog = async () => {
  dialogVisible.value = true
  error.value = ''
  success.value = false
  workflowResponse.value = null // 重置工作流响应对象
  
  // 如果有初始工作流定义，使用它
  if (props.initialWorkflow) {
    workflowName.value = props.initialName || props.initialWorkflow.name || ''
    selectedTasks.value = props.initialWorkflow.tasks.map(task => ({
      name: task.name,
      description: '', // 需要从任务定义中获取
      params: null, // 需要从任务定义中获取
      kwargs: task.kwargs || {},
      showParams: true
    }))
  } else {
    workflowName.value = ''
    taskSearchKeyword.value = ''
    selectedTasks.value = []
  }
  
  if (availableTasks.value.length === 0) {
    await loadAvailableTasks()
  }
  
  // 如果有初始工作流，需要补充任务信息
  if (props.initialWorkflow && availableTasks.value.length > 0) {
    selectedTasks.value = selectedTasks.value.map(task => {
      const taskDef = availableTasks.value.find(t => t.name === task.name)
      if (taskDef) {
        return {
          ...task,
          description: taskDef.description,
          params: taskDef.params
        }
      }
      return task
    })
  }
}

// 监听初始工作流变化，自动打开对话框
watch(() => props.initialWorkflow, (newWorkflow) => {
  if (newWorkflow) {
    openDialog()
  }
}, { immediate: true })

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
  error.value = ''
  success.value = false
  workflowResponse.value = null // 重置工作流响应对象
}

// 导航到工作流详情
const navigateToWorkflow = () => {
  const id = workflowResponse.value?.id
  closeDialog()
  router.push(`/workflows/${id}`)
}

// 提交工作流
const submitWorkflow = async () => {
  if (!workflowName.value.trim()) {
    error.value = '请输入工作流名称'
    return
  }
  
  if (selectedTasks.value.length === 0) {
    error.value = '请至少选择一个任务'
    return
  }
  
  loading.value = true
  error.value = ''
  success.value = false
  
  try {
    const workflowData = {
      name: workflowName.value.trim(),
      tasks: selectedTasks.value.map(task => ({
        name: task.name,
        kwargs: task.kwargs
      }))
    }
    
    const response = await api.post('/api/v1/workflows', workflowData)
    success.value = true
    workflowResponse.value = response.data
    
    // 触发workflow-created事件
    emit('workflow-created', response.data.id)
  } catch (err) {
    error.value = err?.response?.data?.detail || '创建工作流失败'
    console.error('Error creating workflow:', err)
  } finally {
    loading.value = false
  }
}
</script> 