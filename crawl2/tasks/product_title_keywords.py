from collections import defaultdict

from loguru import logger

from crawl2.clients import llm
from crawl2.clients import shopify_knowledge
from crawl2.db import operations, models
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync

PRODUCT_TITLE_KEYWORDS_PROMPT_TEMPLATE = """
从给定的商品标题中提取能够指代该商品的型号词。
要求提取的型号词必须是商品标题中的连续子串。
输出用```json ```包裹的型号词列表, 如果没有型号词则返回空列表。
禁止做任何解释。

商品标题: {product_title}


输出格式：
```json
["keyword1", "keyword2", ...]
```
"""

llm_conf = llm.LLMConfig('usq', 'qwen2.5-32b-chat-fp8-offline', 0, 5000)


async def extract_product_title_keywords(metadata: models.ProductMetadata) -> list[str]:
    prompt = PRODUCT_TITLE_KEYWORDS_PROMPT_TEMPLATE.format(
        product_title=metadata.title)
    logger.info(
        f"Extracting product title keywords for {metadata.url} with prompt: {prompt}")
    result = await llm.call_llm(llm_conf, prompt)
    logger.info(f"Extracted product title keywords for {metadata.url} with result: {result}")
    if result is None:
        return []
    return result


@celery_task
async def extract_product_title_keywords_for_single_product(product_url: str, clear_existing: bool = False):
    """挖掘单个商品的标题关键词"""
    metadata = await operations.get_product_metadata(product_url)
    if not metadata:
        logger.error(f"No metadata found for product URL: {product_url}")
        return
    existed = await operations.get_product_keywords(metadata)
    if existed and not clear_existing:
        logger.info(f"Product keywords already exists for {metadata.url}, skipping extraction.")
        return
    elif existed and clear_existing:
        logger.info(f"Clearing existing product keywords for {metadata.url}.")
        await operations.delete_product_keywords(metadata)
    keywords = await extract_product_title_keywords(metadata)
    await operations.save_product_keywords(metadata, keywords)


@celery_task
async def extract_product_title_keywords_for_all_products(domain: str, clear_existing: bool = False):
    """挖掘指定站点所有商品的标题关键词"""
    metadata_list = await operations.list_product_metadata(domain)
    for metadata in CeleryTaskTqdmAsync(metadata_list, desc="Extracting product title keywords"):
        await extract_product_title_keywords_for_single_product(metadata.url, clear_existing)


@celery_task
async def sync_product_title_keywords_to_knowledge_base(domain: str):
    """同步指定站点的所有商品标题关键词到知识库"""
    knowledge_client = shopify_knowledge.ShopifyKnowledgeClient.get_client()
    site = await operations.get_site(domain)
    if not site:
        logger.error(f"No site found for {domain}")
        return
    store_domain = site.shopify_domain
    product_keywords = await operations.list_product_keywords(domain)
    logger.info(f"Found {len(product_keywords)} product keywords for {domain}")

    maps = defaultdict(list)
    for product_keyword in product_keywords:
        product_metadata = await product_keyword.product
        product_id = product_metadata.product_id
        product_type = product_metadata.product_type
        keywords = product_keyword.keywords
        for keyword in keywords:
            maps[(product_type, keyword)].append(product_id)
    req = shopify_knowledge.ImportProductKeywordsReq(
        storeDomain=store_domain,
        items=[shopify_knowledge.ProductKeywordItem(
            productType=product_type,
            keyword=keyword,
            productIdList=product_id_list
        ) for (product_type, keyword), product_id_list in maps.items()]
    )
    result = await knowledge_client.import_product_keywords(req)
    logger.info(f"Synced product keywords to knowledge base for {domain}, result: {result}")
