from loguru import logger

from crawl2 import schema
from crawl2.clients import llm
from crawl2.db import operations
from crawl2.schema import ProductsAspects
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync

ASPECT_PROMPT_TEMPLATE = """
You are a senior product user experience expert. First, analyze the following product titles to determine the most appropriate product type. Then, based on your expertise, summarize the key aspects that users need to pay attention to when purchasing products in this product type.

The following are some product titles to help you determine the product type:
{item_title_list}

Please follow these steps:
1. Determine the most appropriate product type based on the given titles
2. Generate at least 20 key aspects that users should consider when purchasing products in this product type
3. Use short, clear words for each aspect

Output your result in the following format:
{{
  "product_type": "determined product type",
  "aspect_list": [
    {{
      "name": "aspect_name",
      "description": "short description here"
    }}
  ]
}}
"""


EXPAND_ASPECT_PROMPT_TEMPLATE = """
You are a senior product user experience expert. Your task is to analyze the product type and expand the existing aspect list with additional relevant aspects that users should consider when purchasing this type of product.

Product Type: {product_type}
Product Title: {product_title}
Product Price: {product_price}

Existing Aspects:
{existing_aspects}

Product page content:
{product_page_content}

Please follow these steps:
1. Focus on the product type rather than specific product features
2. Consider aspects that are important for users when purchasing this type of product in general
3. Think about what factors influence purchase decisions for this category of products
4. For each new aspect, provide a clear and concise description
5. Ensure the new aspects are distinct and meaningful, not just variations of existing ones
6. Avoid listing specific product features or specifications

Output your result in the following format:
{{
  "product_type": "determined product type",
  "aspect_list": [
    {{
      "name": "aspect_name",
      "description": "short description here"
    }}
  ]
}}
"""


llm_conf = llm.LLMConfig('doubao', llm.DOUBAO_DEEPSEEK, 0.6, 10000)


async def extract_product_type_aspects(
        domain: str, product_type: str) -> schema.ProductsAspects | None:
    products = await operations.filter_product_metadata(domain, product_type=product_type)
    all_product_titles = "\n".join([product.title for product in products[:3]])
    prompt = ASPECT_PROMPT_TEMPLATE.format(item_title_list=all_product_titles)
    result = await llm.call_llm(llm_conf, prompt)
    if not result:
        return None
    if not isinstance(result, dict) or "aspect_list" not in result:
        return schema.ProductsAspects(product_type=product_type, aspect_list=[])
    else:
        return schema.ProductsAspects.model_validate(result, strict=False)


@celery_task
async def extract_all_product_type_aspects(domain: str):
    """
    挖掘指定站点所有商品类型的产品关注点
    """
    all_products = await operations.list_product_metadata(domain)
    product_types = set(p.product_type for p in all_products)
    tasks = [extract_and_save_single_product_type_aspects(domain, product_type) for product_type in product_types]
    await CeleryTaskTqdmAsync.gather(*tasks, desc="Extracting product aspects")


@celery_task
async def extract_and_save_single_product_type_aspects(
        domain: str, product_type: str, clear_existing: bool = False, do_expand: bool = False):
    """
    挖掘单个商品类型的产品关注点
    """
    site = await operations.get_site(domain)
    existed = await operations.list_product_type_aspects(site.id, product_type)
    if existed is not None and not clear_existing:
        logger.info(f"product type aspects already exist for {product_type}, skipping extraction.")
        return
    elif existed is not None and clear_existing:
        logger.info(f"clearing existing product type aspects for {product_type} in {domain}")
        await operations.delete_product_type_aspects(site.id, product_type)
    result = await extract_product_type_aspects(domain, product_type)
    if result:
        await operations.save_product_type_aspects(site.id, product_type, result)
    if do_expand:
        await expand_single_product_type_aspects(domain, product_type, clear_existing)


async def expand_product_aspects(domain: str, product_type: str) -> schema.ProductsAspects | None:
    site = await operations.get_site(domain)
    existed = await operations.list_product_type_aspects(site.id, product_type)
    if existed is None:
        logger.error(f"no aspects found for product_type {product_type} in {domain}")
        return None
    existing_aspects = "\n".join([f"- {item.name}: {item.description}" for item in existed.aspect_list])
    longest_markdown_page = await operations.find_product_raw_pages_with_longest_markdown(domain, product_type)
    if longest_markdown_page is None:
        logger.error(f"no product page found for product_type {product_type}")
        return None
    product = await longest_markdown_page.metadata
    prompt = EXPAND_ASPECT_PROMPT_TEMPLATE.format(
        product_type=product_type,
        product_title=product.title,
        product_price=product.price,
        existing_aspects=existing_aspects,
        product_page_content=longest_markdown_page.markdown_content
    )
    result = await llm.call_llm(llm_conf, prompt)
    if not result:
        return None
    logger.info("expand product aspects: {}", result)
    aspects = ProductsAspects.from_llm_result(result)
    expanded_aspect_list = aspects.aspect_list if aspects else []
    merged_aspect_list = schema.NameAndDescription.merge_name_description_lists(
        existed.aspect_list, expanded_aspect_list)
    merged_aspects_result = ProductsAspects(product_type=existed.product_type,
                                            aspect_list=merged_aspect_list)
    return merged_aspects_result


@celery_task
async def expand_batch_product_type_aspects(domain: str):
    """
    扩展指定站点所有商品类型的产品关注点
    """
    all_products = await operations.list_product_metadata(domain)
    product_types = set(p.product_type for p in all_products)
    tasks = [expand_single_product_type_aspects(domain, product_type) for product_type in product_types]
    await CeleryTaskTqdmAsync.gather(*tasks, desc="Expanding product aspects")


@celery_task
async def expand_single_product_type_aspects(domain: str, product_type: str, clear_existing: bool = False):
    """ 扩展单个商品类型的产品关注点"""
    site = await operations.get_site(domain)
    existed = await operations.list_expanded_product_type_aspects(site.id, product_type)
    if existed is not None and not clear_existing:
        logger.info(f"product type aspects already expanded for {product_type}, skipping extraction.")
        return
    elif existed is not None and clear_existing:
        logger.info(f"clearing existing expanded product type aspects for {product_type} in {domain}")
        await operations.delete_expanded_product_type_aspects(site.id, product_type)
    result = await expand_product_aspects(domain, product_type)
    if result:
        await operations.save_expanded_product_type_aspects(site.id, product_type, result)
