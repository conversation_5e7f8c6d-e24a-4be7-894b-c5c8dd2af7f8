import { createRouter, createWebHashHistory } from 'vue-router'
import SiteList from '../views/SiteList.vue'
import SiteDetails from '../views/SiteDetails.vue'
import ProductDetails from '../views/ProductDetails.vue'
import SiteSellingPoints from '../views/SiteSellingPoints.vue'
import SiteProductConcerns from '../views/SiteProductConcerns.vue'
import SiteFaqs from '../views/SiteFaqs.vue'
import SessionReplay from '../views/SessionReplay/SessionReplay.vue'
import TaskDefinitions from '../views/TaskDefinitions.vue'
import TaskRuns from '../views/TaskRuns.vue'
import WorkflowDefinitions from '../views/WorkflowDefinitions.vue'
import WorkflowHistory from '../views/WorkflowHistory.vue'
import WorkflowDetails from '../views/WorkflowDetails.vue'
import LoginView from '../views/LoginView.vue'

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: LoginView,
      meta: { public: true }
    },
    {
      path: '/',
      name: 'home',
      component: SiteList
    },
    {
      path: '/sites/:domain',
      name: 'site-details',
      component: SiteDetails
    },
    {
      path: '/sites/:domain/products/:productId',
      name: 'product-details',
      component: ProductDetails
    },
    {
      path: '/sites/:domain/selling_points',
      name: 'site-selling-points',
      component: SiteSellingPoints
    },
    {
      path: '/sites/:domain/concerns',
      name: 'site-product-concerns',
      component: SiteProductConcerns
    },
    {
      path: '/sites/:domain/faqs',
      name: 'site-faqs',
      component: SiteFaqs
    },
    {
      path: '/sites/:domain/session-replay',
      name: 'session-replay',
      component: SessionReplay
    },
    {
      path: '/task-definitions',
      name: 'task-definitions',
      component: TaskDefinitions
    },
    {
      path: '/task-runs',
      name: 'task-runs',
      component: TaskRuns
    },
    {
      path: '/workflow-definitions',
      name: 'workflow-definitions',
      component: WorkflowDefinitions
    },
    {
      path: '/workflow-history',
      name: 'workflow-history',
      component: WorkflowHistory
    },
    {
      path: '/workflows/:workflowId',
      name: 'workflow-details',
      component: WorkflowDetails
    }
  ]
})

import { useAuthStore } from '@/stores/auth'

router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  const doRedirect = () => {
    // 2. 如果检测到已登陆，且 to view 是 login，就跳转到 home
    if (authStore.isAuthenticated && to.name === 'Login') {
      return next({ name: 'home' })
    }
    // 3. 如果检测到已登陆，且 to view 不是 login，则跳转到 to view
    if (authStore.isAuthenticated && to.name !== 'Login') {
      return next()
    }
    // 4. 如果检测到未登陆且 to view 是 public 的，则直接跳转到 to view
    if (!authStore.isAuthenticated && to.meta.public) {
      return next()
    }
    // 5. 如果检测到没有登陆，则跳转到 login view
    return next({ name: 'Login' })
  }

  // 1. 如果检测到 isLoading, 则等待 isLoading 完成再执行跳转
  if (authStore.isLoading) {
    const unwatch = authStore.$subscribe((mutation, state) => {
      if (!state.isLoading) {
        unwatch()
        doRedirect()
      }
    })
  } else {
    doRedirect()
  }
})

export default router