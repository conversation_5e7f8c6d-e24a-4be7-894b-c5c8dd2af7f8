import asyncio
from crawl4ai import <PERSON>raw<PERSON><PERSON>unConfig, CacheMode
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from crawl2.clients import browser
from crawl2.db import operations
from crawl2.schema import PageMetaData, BlogMetaData
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync


# 定义重试装饰器，针对网络错误和超时进行重试
retry_on_network_error = retry(
    stop=stop_after_attempt(3),  # 最多重试3次
    wait=wait_exponential(multiplier=1, min=2, max=10),  # 指数退避，2-10秒
    retry=retry_if_exception_type((asyncio.TimeoutError, ConnectionError, OSError)),
)


@retry_on_network_error
async def crawl_raw_content(url: str, timeout: int = 60):
    """
    只爬取页面的原始 markdown 内容，不做 LLM 抽取
    Args:
        url: 要爬取的URL
        timeout: 超时时间（秒），默认60秒
    """
    crawl_config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        remove_overlay_elements=True,
        exclude_external_links=True,
        exclude_social_media_links=True,
        excluded_tags=["nav", "header", "footer"],
        remove_forms=True,
    )

    try:
        # 使用asyncio.wait_for添加超时控制
        result = await browser.crawl_with_timeout(url, config=crawl_config, timeout=timeout)

        if result and hasattr(result, 'success') and result.success:
            result_data = {
                "url": url,
                "raw_content": result.markdown if hasattr(result, 'markdown') else None,
            }
            return result_data
        else:
            error_msg = result.error_message if result and hasattr(result, 'error_message') else "Unknown error"
            logger.error(f"Crawl page markdown failed for {result.url}: {error_msg}")
            return None
    except asyncio.TimeoutError:
        logger.error(f"Crawl page markdown timeout for {url} after {timeout} seconds")
        raise  # 重新抛出异常以触发重试
    except Exception as e:
        logger.opt(exception=e).error(f"Crawl page markdown failed for {url}: {str(e)}")
        return None


async def _crawl_page_metadata(url: str, clear_existing: bool = False, timeout: int = 60) -> PageMetaData | None:
    """
    爬取单个 page 元数据
    :param url: page 的原始 url
    :param clear_existing: 是否清除已存在的内容
    :param timeout: 超时时间（秒），默认60秒
    :return: PageMetaData 或 None
    """
    try:
        from crawl2.db import operations
        existed = await operations.get_page_metadata(url)
        if existed and not clear_existing:
            logger.info(f"page metadata already exist for {url}, skipping crawl.")
            return None
        elif existed and clear_existing:
            logger.info(f"Clearing existing page metadata for {url}.")
        result = await crawl_raw_content(url, timeout=timeout)
        if result:
            return PageMetaData(
                url=result["url"],
                raw_content=result["raw_content"],
            )
        else:
            return None
    except Exception as e:
        logger.opt(exception=e).error(f"Error crawling page metadata from {url}: {e}")
        return None


async def _crawl_blog_metadata(url: str, clear_existing: bool = False, timeout: int = 60) -> BlogMetaData | None:
    """
    爬取单个 blog 元数据
    :param url: blog 的原始 url
    :param clear_existing: 是否清除已存在的内容
    :param timeout: 超时时间（秒），默认60秒
    :return: BlogMetaData 或 None
    """
    try:
        from crawl2.db import operations
        existed = await operations.get_blog_metadata(url)
        if existed and not clear_existing:
            logger.info(f"blog metadata already exist for {url}, skipping crawl.")
            return None
        elif existed and clear_existing:
            logger.info(f"Clearing existing blog metadata for {url}.")
        result = await crawl_raw_content(url, timeout=timeout)
        if result:
            return BlogMetaData(
                url=result["url"],
                raw_content=result["raw_content"],
            )
        else:
            return None
    except Exception as e:
        logger.opt(exception=e).error(f"Error crawling blog metadata from {url}: {e}")
        return None


@celery_task
async def crawl_single_page_metadata(domain: str, page_url: str, clear_existing: bool = False, timeout: int = 60):
    """
    爬取单个指定自定义页面内容，提取为 markdown 文本.
    """
    page = await _crawl_page_metadata(page_url, clear_existing=clear_existing, timeout=timeout)
    if page:
        await operations.save_page_metadata(domain, page)


@celery_task
async def crawl_single_blog_metadata(domain: str, blog_url: str, clear_existing: bool = False, timeout: int = 60):
    """
    爬取单个指定博客页面内容，提取为 markdown 文本.
    """
    blog = await _crawl_blog_metadata(blog_url, clear_existing=clear_existing, timeout=timeout)
    if blog:
        await operations.save_blog_metadata(domain, blog)


async def crawl_multiple_pages_metadata(domain: str, page_urls: list[str], clear_existing: bool = False,
                                        timeout: int = 60):
    tasks = [_crawl_page_metadata(url, clear_existing=clear_existing, timeout=timeout) for url in page_urls]
    for t in CeleryTaskTqdmAsync.as_completed(tasks, desc="Crawling page metadata"):
        page: PageMetaData | None = await t
        if page:
            await operations.save_page_metadata(domain, page)


async def crawl_multiple_blogs_metadata(domain: str, blog_urls: list[str], clear_existing: bool = False,
                                        timeout: int = 60):
    tasks = [_crawl_blog_metadata(url, clear_existing=clear_existing, timeout=timeout) for url in blog_urls]
    for t in CeleryTaskTqdmAsync.as_completed(tasks, desc="Crawling blog metadata"):
        blog: BlogMetaData | None = await t
        if blog:
            await operations.save_blog_metadata(domain, blog)


@celery_task
async def crawl_all_pages_metadata(domain: str, clear_existing: bool = False, timeout: int = 60):
    """
    爬取指定站点所有自定义页面内容，提取为 markdown 文本.
    """
    logger.info("开始crawl_all_pages_metadata任务")
    site = await operations.get_site(domain)
    await crawl_multiple_pages_metadata(domain, site.pages_urls, clear_existing=clear_existing, timeout=timeout)


@celery_task
async def crawl_all_blogs_metadata(domain: str, clear_existing: bool = False, timeout: int = 60):
    """
    爬取指定站点所有博客页面内容，提取为 markdown 文本.
    """
    logger.info("开始crawl_all_blogs_metadata任务")
    site = await operations.get_site(domain)
    await crawl_multiple_blogs_metadata(domain, site.blogs_urls, clear_existing=clear_existing, timeout=timeout)
