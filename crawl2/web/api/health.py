from fastapi import APIRouter
from tortoise.exceptions import OperationalError
from crawl2.db import models

router = APIRouter(prefix="/health", tags=["health"])


@router.get("")
@router.get("/")
async def health_check():
    """健康检查端点"""
    try:
        # 尝试执行一个简单的数据库查询
        await models.ShopifySite.all().count()
        return {
            "status": "healthy",
            "database": "connected"
        }
    except OperationalError:
        return {
            "status": "unhealthy",
            "database": "disconnected"
        }
