from crawl2.clients import cos, llm
from crawl2 import utils
from lxml import etree
from tqdm.asyncio import tqdm as tqdm_async
import asyncio
from loguru import logger
from concurrent.futures import ThreadPoolExecutor, wait
from httpx import AsyncClient
import html2text
import json
import os
from datetime import datetime


ALIGN_PROMPT = """
你是一位经验丰富的电商运营专家，需要判断给定的商品卖点是否直接来源于商品详情页的特定区域内容。

判断标准：
1. 该区域内容必须与卖点完全相关
2. 该区域内容必须是卖点的直接来源或核心信息
3. 如果区域内容只是泛泛提及相关概念，而不是具体描述卖点，则不算相关

只输出 True 或者 False, True 表示该区域内容是该卖点的直接来源，False 表示不是。
不要输出额外的解释。

## 商品详情页特定区域内容
{element_content}

## 场景化卖点
{scenario_selling_point}

## 判断：该区域内容是否是该卖点的直接来源
"""


llm_conf = llm.LLMConfig("usq", "qwen2.5-32b-chat-fp8-offline", 0.6, 10)


async def get_html(url: str):
    page_url = "".join(utils.split_domain_and_path(url))
    cos_client = cos.CosClient.get_client()
    logger.info(f"get page {page_url} html from cos")
    return cos_client.read_page_html(page_url)


def get_page_element_with_content(page_html: str, selectors: list[str]):
    parser = etree.HTMLParser()
    tree = etree.fromstring(page_html, parser)
    h = html2text.HTML2Text()
    h.ignore_links = True
    h.ignore_images = True
    h.body_width = 0  # Disable line wrapping
    matched_contents = []
    for selector in selectors:
        elements = tree.xpath(selector)
        for element in elements:
            # Convert element to string and then to markdown
            element_html = etree.tostring(element, encoding="unicode", method="html")
            element_markdown = h.handle(element_html)
            matched_contents.append(element_markdown.strip())
    content = "\n".join(matched_contents)
    content = content.strip()
    return content


async def call_with_payload(prompt, payload):
    llm_response = await llm.call_llm(llm_conf, prompt, parse_json=False)
    return llm_response, payload


async def align_one_scenario_selling_points(
    point, product_url, product_elements, cached_element_content
):
    tasks = []
    for element in product_elements:
        try:
            target = element["target"]
            selectors = element["selectors"]
            if target not in cached_element_content:
                continue
            element_content = cached_element_content[target]
            if not element_content:
                # logger.info(f"element {selectors} content is empty, skip.")
                continue

            aspect = point["aspect"]
            description = point.get("description", point.get("content", ""))
            prompt = ALIGN_PROMPT.format(
                element_content=element_content,
                scenario_selling_point={
                    "aspect": aspect,
                    "description": description,
                },
            )
            tasks.append(
                call_with_payload(
                    prompt,
                    {
                        "element": element,
                        "prompt": prompt,
                        "element_content": element_content,
                    },
                )
            )
        except Exception as e:
            logger.error(f"error when align one scenario selling points: {e}")
            continue
    related_elements = []
    related_element_contents = []
    for task in tqdm_async(
        asyncio.as_completed(tasks),
        total=len(tasks),
        desc="Generate Scenario Selling Point Align",
    ):
        content, payload = await task
        target = payload["element"]["target"]
        selectors = payload["element"]["selectors"]
        element_content = payload["element_content"]
        # logger.info(
        #     "get content: {} product_url: {} element: {} prompt: {}",
        #     content,
        #     product_url,
        #     f"{target}:{selectors}",
        #     payload["prompt"],
        # )
        if content and "True" in content:
            related_elements.append(payload["element"])
            related_element_contents.append(element_content)
    return related_elements, related_element_contents


def process_element(element, page_html, cached_element_content):
    """Function to process a single element and store its content."""
    element_content = get_page_element_with_content(page_html, element["selectors"])
    cached_element_content[element["target"]] = element_content


async def list_raw_page_elements(page_url: str):
    api_url = f"https://shop-pupil.leyanbot.com/api/v1/behavior-analysis/page-elements?url={page_url}"
    async with AsyncClient() as client:
        response = await client.get(api_url)
        return response.json()


async def get_product_selling_points(product_id: str):
    api_url = f"https://usq-apiv2.jolect.com/shopify-knowledge/selling_point_concerns/selling_point?spuId={product_id}"
    # JWT token for API authentication
    # JWT token for API authentication - split into multiple parts
    auth_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsZXlhbl91c2VyIjp7Im5hbWUiOiJhZG1pbiJ9LCJpc3MiOiJzaG9waWZ5LXN0b3JlLWNlbnRlciIsInN0b3JlSW5mbyI6eyJzdG9yZUlkIjo0LjAsImRvbWFpbiI6Im9yb3JvLWNhLm15c2hvcGlmeS5jb20iLCJleHRlcm5hbFN0b3JlSWQiOiI0NjIwMzg5NTk2MSJ9LCJleHAiOjE3NTQ2NDgwMzgsImlhdCI6MTc1NDA0MzIzOCwidXNlcklkIjozLCJqdGkiOiI4MDk3NmY1ZC0zODJmLTQyNTItYjcyMy1jMmJlZjI2YTUyMGMiLCJvcmdJZCI6NX0.IX2Pmx6BySwQx1QLZnYrflXiCJDNRXHehy2R6EyjUMA"
    async with AsyncClient() as client:
        client.headers.update({"Authorization": f"Bearer {auth_token}"})
        response = await client.get(api_url)
        result = response.json()
        if not result or "data" not in result:
            return []
        data = result["data"]
        if not data or "sellingPointsList" not in data:
            return []
        return data["sellingPointsList"]


async def get_product_infos(domain: str):
    api_url = f"https://shop-pupil.leyanbot.com/api/v1/sites/{domain}"
    async with AsyncClient() as client:
        response = await client.get(api_url)
        data = response.json()
        products = data.get("products", [])
        shopify_domain = data.get("shopify_domain", "")
        return products, shopify_domain


async def align_product_selling_point_with_element(product_url: str, points):
    product_url = "".join(utils.split_domain_and_path(product_url))
    product_elements = await list_raw_page_elements(product_url)
    # print(product_elements[0].keys())
    # logger.info("product_url: {} product_elements: {}", product_url, product_elements)
    page_html = await get_html(product_url)
    # logger.info("get page {} html: {}", product_url, page_html[:100] + "...")
    cached_element_content = {}
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [
            executor.submit(process_element, element, page_html, cached_element_content)
            for element in product_elements
        ]
        wait(futures)
    
    # for element in product_elements:
    #     process_element(element, page_html, cached_element_content)

    tasks = []
    for point in points:
        tasks.append(
            align_one_scenario_selling_points(
                point, product_url, product_elements, cached_element_content
            )
        )
    results = await asyncio.gather(*tasks)
    return results


def save_site_alignment_results_to_file(
    site_results, domain, shopify_domain, output_dir="alignment_results"
):
    """
    保存站点对齐结果到本地文件，包含该站点下所有商品的结果

    Args:
        site_results: 包含所有商品对齐结果的列表，每个元素为 (product_info, results, selling_points)
        domain: 站点域名
        output_dir: 输出目录
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"alignment_site_{domain}.json"
    filepath = os.path.join(output_dir, filename)

    # 构建站点结果数据结构
    site_alignment_data = {
        "domain": domain,
        "shopify_domain": shopify_domain,
        "timestamp": timestamp,
        "total_products": len(site_results),
        "products": [],
    }

    # 处理每个商品的结果
    for product_info, results, selling_points in site_results:
        product_id = product_info["product_id"]
        product_url = product_info["url"]

        product_data = {
            "product_id": product_id,
            "product_url": product_url,
            "selling_points_count": len(selling_points),
            "alignments": [],
        }

        # 将结果与原始卖点对应
        for i, (point, result) in enumerate(zip(selling_points, results)):
            related_elements, related_element_contents = result

            alignment_entry = {
                "selling_point": {
                   **point
                },
                "related_elements": related_elements,
                "related_element_contents": related_element_contents,
            }

            product_data["alignments"].append(alignment_entry)

        site_alignment_data["products"].append(product_data)

    # 保存到文件
    with open(filepath, "w", encoding="utf-8") as f:
        json.dump(site_alignment_data, f, ensure_ascii=False, indent=2)

    logger.info(f"Site alignment results saved to: {filepath}")
    return filepath


async def run_for_site(domain: str):
    product_infos, shopify_domain = await get_product_infos(domain)
    logger.info("find {} products for domain:{}", len(product_infos), domain)

    # 创建输出目录
    output_dir = "alignment_results"
    os.makedirs(output_dir, exist_ok=True)

    # 收集所有商品的结果
    site_results = []

    for product_info in tqdm_async(product_infos, desc="Processing products"):
        product_id = product_info["product_id"]
        product_url = product_info["url"]
        selling_points = await get_product_selling_points(product_id)

        if not selling_points:
            continue

        results = await align_product_selling_point_with_element(product_url, selling_points)

        # 将结果添加到站点结果列表
        site_results.append((product_info, results, selling_points))

        # 打印对齐结果摘要
        logger.info(f"Product {product_id} alignment completed:")
        for i, (point, result) in enumerate(zip(selling_points, results)):
            related_elements, related_element_contents = result
            description = point.get("content", "N/A")
            point_id = point.get("id", "N/A")
            logger.info(
                f"  Selling point {point_id}: {description} - {len(related_elements)} related elements found"
            )

    # 保存站点级别的结果到文件
    if site_results:
        filepath = save_site_alignment_results_to_file(site_results, domain, shopify_domain, output_dir)
        logger.info(f"Site alignment results saved to: {filepath}")
        logger.info(f"Total products processed: {len(site_results)}")
    else:
        logger.info("No products with selling points found for this site")


if __name__ == "__main__":
    # product_url = (
    #     "https://www.ororowear.ca/products/mens-classic-heated-vest-all-colors"
    # )
    # element = asyncio.run(list_raw_page_elements(product_url))
    # print(element)
    # points = [
    #     {
    #         "aspect": "heating performance",
    #         "description": "4 carbon fiber heating elements with 3 adjustable settings (up to 10 hours on low)",
    #         "marketingCopies": [],
    #     }
    # ]
    # results = asyncio.run(
    #     align_product_selling_point_with_element(
    #         product_url,
    #         points,
    #     )
    # )
    # for result in results:
    #     related_elements, related_element_contents = result
    #     # print(related_elements)
    #     for content in related_element_contents:
    #         print(content)
    #         print("-" * 100)
    # product_id = "7802714030251"
    # selling_point = asyncio.run(get_product_selling_point(product_id))
    # print(selling_point)
    domain = "ororowear.ca"
    asyncio.run(run_for_site(domain))
