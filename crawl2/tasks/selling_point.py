import json
from typing import Dict

from loguru import logger

from crawl2 import schema
from crawl2.clients import llm, shopify_knowledge
from crawl2.db import operations
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync, CeleryTaskTqdm
from crawl2.tasks.scenario_selling_point import get_scenario_labels
from crawl2.config import settings


PROMPT_TEMPLATE = """You are a professional e-commerce marketing copywriter. Given product detail page content, please identify product selling points and create compelling marketing copy based on user concerns and usage scenarios.

1. I will provide you with product detail page content and user concerns for this category
2. Please identify product selling points based on user concerns
3. For each selling point, create multiple marketing copies incorporating user scenarios
4. Output the results in the following JSON format:
```json
{{
    "selling_points": [
        {{
            "name": "User Concern Point",
            "value": "Selling Point Value",
            "marketing_copies": [
                "Marketing Copy 1 - With specific usage scenario",
                "Marketing Copy 2 - With specific usage scenario",
                "Marketing Copy 3 - With specific usage scenario"
            ]
        }}
    ]
}}
```

Example output:
```json
{{
    "selling_points": [
        {{
            "name": "Heating Performance",
            "value": "Uses graphene heating technology with 3-second rapid heating",
            "marketing_copies": [
                "No more shivering at bus stops - warm up in 3 seconds during your winter commute",
                "Essential for ski trips - graphene continuous heating keeps you cozy even in -10°C snow",
                "Your warmth guardian for outdoor work - quick heating technology makes it feel like spring even in cold winds"
            ]
        }},
        {{
            "name": "Battery Life",
            "value": "4000mAh large capacity battery, 8 hours of continuous heating",
            "marketing_copies": [
                "One charge lasts all day - work confidently at cold construction sites",
                "8-hour endurance to accompany you through long outdoor hiking adventures"
            ]
        }}
    ]
}}
```

Product Detail Page Data:
{content}

User Concerns:
{user_concerned_points}

Regarding selling points and marketing copy:
1. Return all product selling points mentioned in this product detail page
2. If no selling points are mentioned in the product detail page, return an empty list
3. For each user concern, return only the most important selling point; ignore the user concern if not mentioned in the product detail
4. Generate at most 5 marketing copies for each selling point that:
   - Describe specific user scenarios that resonate with customers
   - Highlight how the product solves specific user problems in that scenario
   - Use immersive language that helps users visualize using the product
   - Keep language natural and engaging, avoid over-marketing
   - Ensure diversity, don't repeat similar scenarios
5. Values and marketing copies should be in English
"""


PROMPT_TEMPLATE_V2 = """You are a professional e-commerce marketing copywriter. Given product detail page content, user concerns, and related usage scenarios for this product category, please identify product selling points and create compelling marketing copy.

1. I will provide you with:
   - Product detail page content
   - User concerns for this category
   - Related usage scenarios for this product type (for reference)

2. Please identify product selling points based on user concerns

3. For each selling point, create multiple marketing copies incorporating user scenarios:
   - You can use the provided related usage scenarios as inspiration
   - You can also create new scenarios that are similar in granularity to the provided ones
   - Don't make scenarios too detailed or too broad - keep them at a similar level to the reference scenarios

4. Output the results in the following JSON format:
```json
{{
    "selling_points": [
        {{
            "name": "User Concern Point",
            "value": "Selling Point Value",
            "marketing_copies": [
                "Marketing Copy 1 - With specific usage scenario",
                "Marketing Copy 2 - With specific usage scenario",
                "Marketing Copy 3 - With specific usage scenario"
            ]
        }}
    ]
}}
```

Example output:
```json
{{
    "selling_points": [
        {{
            "name": "Heating Performance",
            "value": "Uses graphene heating technology with 3-second rapid heating",
            "marketing_copies": [
                "No more shivering at bus stops - warm up in 3 seconds during your winter commute",
                "Essential for ski trips - graphene continuous heating keeps you cozy even in -10°C snow",
                "Your warmth guardian for outdoor work - quick heating technology makes it feel like spring even in cold winds"
            ]
        }},
        {{
            "name": "Battery Life",
            "value": "4000mAh large capacity battery, 8 hours of continuous heating",
            "marketing_copies": [
                "One charge lasts all day - work confidently at cold construction sites",
                "8-hour endurance to accompany you through long outdoor hiking adventures"
            ]
        }}
    ]
}}
```

Product Detail Page Data:
{content}

User Concerns:
{user_concerned_points}

Related Usage Scenarios (for reference):
{scenario_labels}

Regarding selling points and marketing copy:
1. Return all product selling points mentioned in this product detail page
2. If no selling points are mentioned in the product detail page, return an empty list
3. For each user concern, return only the most important selling point; ignore the user concern if not mentioned in the product detail
4. Generate at most 5 marketing copies for each selling point that:
   - Describe specific user scenarios that resonate with customers
   - Use the provided related usage scenarios as inspiration for scenario granularity
   - Create new scenarios that are similar in scope and detail level to the reference scenarios
   - Highlight how the product solves specific user problems in that scenario
   - Use immersive language that helps users visualize using the product
   - Keep language natural and engaging, avoid over-marketing
   - Ensure diversity, don't repeat similar scenarios
5. Values and marketing copies should be in English
"""


llm_conf = llm.LLMConfig('usq', 'qwen2.5-32b-chat-fp8-offline', 0.6, 10000)


async def extract_single_product_selling_points(url: str) -> list[schema.ProductSellingPoint] | None:
    """
    Extracts the main selling points of a single product from a given URL.

    This function retrieves metadata and related knowledge documents for a product
    based on the provided URL. It leverages user-defined aspects for the product
    type and combines these with extracted document content to generate meaningful
    selling points using a language model.

    Parameters:
    url: str
        The URL of the product whose selling points are to be extracted.

    Returns:
    list[schema.ProductSellingPoint] | None
        A list of product selling points represented as `ProductSellingPoint`
        objects. Returns None if no product data, product ID, or relevant knowledge
        is found, or if the language model fails to produce a result.

    Raises:
        Does not explicitly raise any errors within the function but may
        implicitly raise exceptions from called operations or dependencies.
    """
    product = await operations.get_product_metadata(url)
    if not product:
        logger.warning(f"No product found for URL: {url}")
        return None
    if not product.product_id:
        logger.error(f"No product ID found for URL: {product.url}")
        return None
    knowledge = await operations.get_product_knowledge(url)
    if not knowledge or not knowledge.documents:
        logger.warning(f"No knowledge found for product URL: {product.url}")
        return None
    product_type = product.product_type
    site = await product.site
    aspects = await operations.list_expanded_product_type_aspects(site.id, product_type)
    if aspects:
        user_aspect_points = [r.name for r in aspects.aspect_list]
    else:
        user_aspect_points = []
    serialized_user_aspect_points = json.dumps(user_aspect_points, ensure_ascii=False, indent=2)
    documents = [p for p in knowledge.documents]
    product_content = json.dumps(documents, ensure_ascii=False, indent=2)
    version = settings.MARKETING_COPY_PROMPT_VERSION
    if version == 2:
        scenario_labels = await get_scenario_labels(product_type=product_type)
        logger.info(f"{product_type} using v2 marketing prompt. scenario_labels: {scenario_labels}")
        prompt = PROMPT_TEMPLATE_V2.format(
            content=product_content,
            user_concerned_points=serialized_user_aspect_points,
            scenario_labels=scenario_labels or ""
        )
    else:
        prompt = PROMPT_TEMPLATE.format(
            content=product_content,
            user_concerned_points=serialized_user_aspect_points
        )
    result = await llm.call_llm(llm_conf, prompt)
    if result is None:
        return None
    points = schema.ProductSellingPoint.from_llm_result(result)
    return points


@celery_task
async def extract_and_save_single_product_selling_points(product_url: str, clear_existing: bool = False):
    """
    挖掘单个商品的卖点
    """
    existing_selling_points = await operations.get_product_selling_points(product_url)
    if existing_selling_points and not clear_existing:
        logger.info(f"Product selling points already exist for {product_url}, skipping extraction.")
        return
    elif existing_selling_points and clear_existing:
        logger.info(f"Clearing existing selling points for {product_url}.")
        await operations.delete_product_selling_points(product_url)
    selling_points = await extract_single_product_selling_points(product_url)
    if not selling_points:
        return
    await operations.save_product_selling_points(product_url, selling_points)


@celery_task
async def extract_all_selling_points(domain: str, clear_existing: bool = False) -> None:
    """
    挖掘指定站点所有商品的卖点（遵循挖掘策略）
    """
    # 根据策略获取需要处理的产品URL列表
    product_urls = await operations.get_products_by_strategy(domain, 'product_selling_point')

    if not product_urls:
        logger.info(f"No products to process for selling points in domain {domain}")
        return

    tasks = [extract_and_save_single_product_selling_points(url, clear_existing=clear_existing) for url in product_urls]
    await CeleryTaskTqdmAsync.gather(*tasks,
                                     desc=f"Extracting selling points for {len(product_urls)} products in {domain}")


@celery_task
async def sync_product_selling_points_to_knowledge_base(domain: str):
    """
    同步标注为 ready 状态的产品卖点到线上知识库
    """
    site = await operations.get_site(domain)
    if not site:
        logger.error(f"Site {domain} not found.")
        return
    selling_points = await operations.list_selling_points_by_status(
            domain, schema.SellingPointStatus.READY,
            # 只要是标注为 ready 的都同步到 shopify-knowledge，即使爬取策略中没包含对应商品
            should_match_crawl_strategy=False)
    if not selling_points:
        logger.warning(f"No selling points found for store {domain} with status READY.")
        return

    product_sp_groups: Dict[str, list[schema.ProductMetaAndSellingPoint]] = {}
    for sp in selling_points:
        product_sp_groups.setdefault(sp.product_id, []).append(sp)

    client = shopify_knowledge.ShopifyKnowledgeClient.get_client()
    for product_id, sp_list in CeleryTaskTqdm(product_sp_groups.items(), total=len(product_sp_groups),
                                              desc=f"Syncing selling points for {domain}"):
        shopify_sp = shopify_knowledge.ProductSellingPoints(
            spuId=product_id,
            sellingPointsList=[
                shopify_knowledge.ProductSellingPointItem(
                    aspect=sp.selling_point_name,
                    description=sp.selling_point_value,
                    marketingCopies=sp.selling_point_marketing_copies or [],
                    element_targets=sp.element_targets
                ) for sp in sp_list
            ]
        )
        try:
            await client.sync_shopify_knowledge(site.shopify_domain, shopify_sp)
        except Exception as e:
            logger.opt(exception=e).error(f"Failed to sync selling points"
                                          f" for product {product_id} in site {domain}: {str(e)}")
