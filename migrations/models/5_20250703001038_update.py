from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX IF EXISTS "idx_store_knowl_store_d_86d20c";
        ALTER TABLE "store_knowledge_value" DROP CONSTRAINT IF EXISTS "uid_store_knowl_store_d_86d20c";
        DROP INDEX IF EXISTS "idx_store_knowl_point_i_cf73b1";
        DROP INDEX IF EXISTS "idx_page_metada_url_d888dd";
        DROP INDEX IF EXISTS "idx_blog_metada_url_784ef9";
        ALTER TABLE "blog_metadata" DROP COLUMN "store_domain";
        ALTER TABLE "page_metadata" DROP COLUMN "store_domain";
        ALTER TABLE "store_knowledge_points" ADD "site_id" INT NOT NULL;
        ALTER TABLE "store_knowledge_points" ADD "gmt_create" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP;
        ALTER TABLE "store_knowledge_points" ADD "gmt_modified" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP;
        ALTER TABLE "store_knowledge_points" DROP COLUMN "store_domain";
        ALTER TABLE "store_knowledge_value" ADD "site_id" INT NOT NULL;
        ALTER TABLE "store_knowledge_value" ADD "gmt_create" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP;
        ALTER TABLE "store_knowledge_value" ADD "gmt_modified" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP;
        ALTER TABLE "store_knowledge_value" DROP COLUMN "store_domain";
        CREATE INDEX IF NOT EXISTS "idx_blog_metada_url_3a4de8" ON "blog_metadata" ("url", "site_id");
        CREATE INDEX IF NOT EXISTS "idx_page_metada_url_05a929" ON "page_metadata" ("url", "site_id");
        ALTER TABLE "store_knowledge_points" ADD CONSTRAINT "fk_store_kn_shopify__162184bf" FOREIGN KEY ("site_id") REFERENCES "shopify_site" ("id") ON DELETE CASCADE;
        CREATE INDEX IF NOT EXISTS "idx_store_knowl_point_i_e7dbf2" ON "store_knowledge_points" ("point_id", "site_id");
        ALTER TABLE "store_knowledge_value" ADD CONSTRAINT "fk_store_kn_shopify__f2a8daae" FOREIGN KEY ("site_id") REFERENCES "shopify_site" ("id") ON DELETE CASCADE;
        CREATE UNIQUE INDEX IF NOT EXISTS "uid_store_knowl_label_109be0" ON "store_knowledge_value" ("label", "detailed_label", "site_id");
        CREATE INDEX IF NOT EXISTS "idx_store_knowl_label_109be0" ON "store_knowledge_value" ("label", "detailed_label", "site_id");"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX IF EXISTS "idx_store_knowl_label_109be0";
        DROP INDEX IF EXISTS "uid_store_knowl_label_109be0";
        ALTER TABLE "store_knowledge_value" DROP CONSTRAINT IF EXISTS "fk_store_kn_shopify__f2a8daae";
        DROP INDEX IF EXISTS "idx_store_knowl_point_i_e7dbf2";
        ALTER TABLE "store_knowledge_points" DROP CONSTRAINT IF EXISTS "fk_store_kn_shopify__162184bf";
        DROP INDEX IF EXISTS "idx_page_metada_url_05a929";
        DROP INDEX IF EXISTS "idx_blog_metada_url_3a4de8";
        ALTER TABLE "blog_metadata" ADD "store_domain" VARCHAR(255) NOT NULL;
        ALTER TABLE "page_metadata" ADD "store_domain" VARCHAR(255) NOT NULL;
        ALTER TABLE "store_knowledge_points" ADD "store_domain" VARCHAR(255) NOT NULL;
        ALTER TABLE "store_knowledge_points" DROP COLUMN "site_id";
        ALTER TABLE "store_knowledge_points" DROP COLUMN "gmt_create";
        ALTER TABLE "store_knowledge_points" DROP COLUMN "gmt_modified";
        ALTER TABLE "store_knowledge_value" ADD "store_domain" VARCHAR(255) NOT NULL;
        ALTER TABLE "store_knowledge_value" DROP COLUMN "site_id";
        ALTER TABLE "store_knowledge_value" DROP COLUMN "gmt_create";
        ALTER TABLE "store_knowledge_value" DROP COLUMN "gmt_modified";
        CREATE INDEX IF NOT EXISTS "idx_blog_metada_url_784ef9" ON "blog_metadata" ("url", "store_domain", "site_id");
        CREATE INDEX IF NOT EXISTS "idx_page_metada_url_d888dd" ON "page_metadata" ("url", "store_domain", "site_id");
        CREATE INDEX IF NOT EXISTS "idx_store_knowl_point_i_cf73b1" ON "store_knowledge_points" ("point_id", "store_domain");
        CREATE UNIQUE INDEX IF NOT EXISTS "uid_store_knowl_store_d_86d20c" ON "store_knowledge_value" ("store_domain", "label", "detailed_label");
        CREATE INDEX IF NOT EXISTS "idx_store_knowl_store_d_86d20c" ON "store_knowledge_value" ("store_domain", "label", "detailed_label");"""
