import asyncio
from datetime import datetime
from typing import TypeV<PERSON>, Type, Dict, List, Literal
from uuid import uuid4

import celery
import pypika_tortoise
import tortoise.functions
from loguru import logger
from tortoise import exceptions, connections
from tortoise.expressions import Q, Case, When

from crawl2 import schema

from crawl2.db import models
from crawl2.schema import ScenarioSellingPoint, PaginatedResponse
from crawl2.utils import generate_log_url

T = TypeVar("T", bound=models.CrawlStatusMixin)


async def mark_status_processing(model: Type[T], **kwargs):
    """
    Mark the status of the object as processing.
    """
    await model.update_or_create(defaults={"crawl_status": models.CrawlStatus.PROCESSING}, **kwargs)
    logger.info(f"Marked {model.__name__} as processing with kwargs: {kwargs}")


async def save_site_metadata(domain: str, shopify_domain: str, product_urls: list[str], collection_urls: list[str],
                             pages_urls: list[str], blogs_urls: list[str]) -> models.ShopifySite:
    """
    Save the site metadata to the database.
    """
    site, _ = await models.ShopifySite.update_or_create(
        domain=domain,
        defaults={
            "shopify_domain": shopify_domain,
            "product_urls": product_urls,
            "collection_urls": collection_urls,
            "pages_urls": pages_urls,
            "blogs_urls": blogs_urls,
            "crawl_status": models.CrawlStatus.SUCCESS,
        },
    )
    logger.info(f"updated shopify site: {domain}, {shopify_domain},"
                f" product_urls({len(product_urls)})={product_urls[:1]}... pages_urls({len(pages_urls)})"
                f" blogs_urls({len(blogs_urls)})")
    return site


async def init_site_metadata(domain: str) -> models.ShopifySite:
    try:
        site = await models.ShopifySite.create(
            domain=domain,
            crawl_status=models.CrawlStatus.PENDING
        )
        return site
    except exceptions.IntegrityError as e:
        logger.error(f"create site for {domain} error: {e}")
        return await models.ShopifySite.get(domain=domain)


async def get_site(domain: str) -> models.ShopifySite | None:
    """
    Get the site metadata from the database.
    """
    site = await models.ShopifySite.get_or_none(domain=domain)
    if not site:
        site = await models.ShopifySite.get_or_none(shopify_domain=domain)
        if not site:
            logger.warning(f"Shopify site {domain} not found in the database.")
            return None
    return site


async def get_site_by_myshopify_main(domain: str) -> models.ShopifySite | None:
    """
    Get the site metadata from the database.
    """
    site = await models.ShopifySite.get_or_none(shopify_domain=domain)
    if not site:
        logger.warning(f"Shopify site {domain} not found in the database.")
        return None
    return site


class JsonLengthFunc(tortoise.functions.Function):
    """PostgreSQL JSONB array length function.

    usage: model.annotate(product_count=JsonLengthFunc('product_urls'))
    """
    class PypikaJsonLen(pypika_tortoise.terms.Function):
        def __init__(self, *args, **kwargs):
            super().__init__("jsonb_array_length", *args, **kwargs)
    database_func = PypikaJsonLen


async def render_site_brief(site):
    domain, shopify_domain = site['domain'], site['shopify_domain']
    return schema.SiteBrief(
        domain=domain,
        shopify_domain=shopify_domain,
        crawl_status=site['crawl_status'],
        product_count=site['product_count'],
        selling_point_stats=await get_selling_point_edit_stats(domain),
        concern_point_stats=await get_concern_point_edit_stats(domain),
        faq_stats=await get_faqs_edit_stats(domain),
    )


async def list_site_briefs() -> list[schema.SiteBrief]:
    sites = await (models.ShopifySite.annotate(product_count=JsonLengthFunc('product_urls'))
                   .values('domain', 'shopify_domain', 'crawl_status', 'product_count'))
    briefs = await asyncio.gather(*[render_site_brief(site) for site in sites])
    return briefs


async def get_site_brief(domain: str) -> schema.SiteBrief | None:
    site = await models.ShopifySite.filter(domain=domain).get_or_none()
    if not site:
        return None
    return await render_site_brief({'domain': site.domain, 'shopify_domain': site.shopify_domain,
                                    'crawl_status': site.crawl_status,
                                    'product_count': len(site.product_urls)})


async def save_products_metadata(domain: str, *products: schema.ProductMetaData):
    site = await get_site(domain)
    if not site:
        site = await init_site_metadata(domain)
    for product in products:
        await models.ProductMetadata.update_or_create(
            url=product.url,
            defaults=dict(
                product_id=product.product_id,
                product_type=product.product_type,
                title=product.title,
                price=product.price,
                tags=product.tags,
                vendor=product.vendor,
                site=site,
                variant_info=product.variant_info.model_dump() if product.variant_info else {},
            )
        )
    logger.info(f'saved {len(products)} product for {domain}')


async def list_product_metadata(domain: str) -> list[models.ProductMetadata]:
    site = await get_site(domain)
    if not site:
        return []
    return [p for p in await models.ProductMetadata.filter(url__in=site.product_urls).all()]


async def get_product_metadata(url: str) -> models.ProductMetadata | None:
    return await models.ProductMetadata.get_or_none(url=url)


async def get_product_metadata_by_product_id(domain: str, product_id: str) -> models.ProductMetadata | None:
    """
    Get product metadata by domain and product ID.
    """
    return await models.ProductMetadata.filter(site__domain=domain, product_id=product_id).first()


async def get_product_metadata_by_product_id_only(product_id: str) -> models.ProductMetadata | None:
    """
    Get product metadata by product ID only.
    """
    return await models.ProductMetadata.filter(product_id=product_id).first()


async def get_product_details(domain: str, product_id: str) -> schema.ProductDetails | None:
    product = await models.ProductMetadata.filter(site__domain=domain, product_id=product_id).get_or_none()
    if not product:
        logger.warning(f"Product {product_id} not found in the database.")
        return None
    details = schema.ProductDetails(
        url=product.url,
        product_id=product.product_id,
        title=product.title,
        product_type=product.product_type,
        price=product.price,
        tags=product.tags,
        vendor=product.vendor,
        page_markdown=None,
        knowledge_markdown=None,
        knowledge_documents=[],
        selling_points=[],
    )
    raw_page = await product.product_raw_pages.order_by('-id').first()
    if raw_page:
        details.page_markdown = raw_page.markdown_content
    knowledge = await product.product_knowledge.order_by('-id').first()
    if knowledge:
        details.knowledge_markdown = knowledge.markdown_content
        details.knowledge_documents = [schema.ProductKnowledgeWithTopic.model_validate(item)
                                       for item in knowledge.documents]
    selling_points = await product.selling_points.all()
    for sp in selling_points:
        point = schema.ProductSellingPoint(
            name=sp.name, value=sp.description,
            marketing_copies=sp.marketing_copies)
        details.selling_points.append(point)
    details.faqs = await get_product_faqs(product.url)
    return details


async def filter_product_metadata(domain: str, product_type: str) -> list[models.ProductMetadata]:
    site = await get_site(domain)
    if not site:
        return []
    return [p for p in await models.ProductMetadata.filter(url__in=site.product_urls, product_type=product_type).all()]


async def get_product_raw_page(url: str) -> models.ProductRawPage | None:
    return await models.ProductRawPage.filter(metadata__url=url).get_or_none()


async def list_product_raw_pages(domain: str) -> list[models.ProductRawPage]:
    raw_pages = await models.ProductRawPage.filter(metadata__site__domain=domain).all()
    return [page for page in raw_pages]


async def find_product_raw_pages_with_longest_markdown(domain: str, product_type: str) -> models.ProductRawPage | None:
    pages = await models.ProductRawPage.filter(metadata__site__domain=domain, metadata__product_type=product_type).all()
    longest_markdown_page = max(pages, key=lambda p: len(p.markdown_content))
    return longest_markdown_page


async def save_product_raw_page(url: str, markdown: str, image_ocr_result: str | None = None):
    metadata = await get_product_metadata(url)
    if not metadata:
        logger.warning(f"Product metadata not found for {url}, skipping save raw page.")
        return
    links, cleaned_markdown = schema.CleanedProductPageMarkdown.extract_links(markdown)
    json_objects, cleaned_markdown = schema.CleanedProductPageMarkdown.extract_json_objects(cleaned_markdown)
    if image_ocr_result:
        cleaned_markdown = cleaned_markdown + "\n\n" + image_ocr_result

    await models.ProductRawPage.update_or_create(
        metadata_id=metadata.id,
        defaults=dict(html_content='',
                      markdown_content=cleaned_markdown,
                      extracted_links=[link.model_dump() for link in links],
                      json_objects=json_objects,
                      crawl_status=models.CrawlStatus.SUCCESS,
                      ))


def check_document_is_valid(document: schema.ProductKnowledgeWithTopic) -> bool:
    """
    Check if the document is valid.
    A valid document should have content and the content should not be empty.
    If the content only contains '#', it is not valid.
    """
    if not document.content:
        return False
    content = document.content
    content = content.replace("#", "")
    content = content.strip()
    if content == "":
        return False
    return True


async def save_product_knowledge(
    product_url: str,
    topics: list[schema.ProductKnowledgeGroupByTopic],
    source: str,
):
    metadata = await get_product_metadata(product_url)
    documents = []
    for topic in topics:
        for doc in schema.ProductKnowledgeWithTopic.from_product_knowledge_topic_group(topic):
            documents.append(doc)
    if not documents:
        logger.warning(f"No valid documents found for product {product_url}: {topics}.")
        return
    markdown = schema.ProductKnowledgeGroupByTopic.merge_topics_as_markdown(metadata.url, topics)
    await models.ProductKnowledge.create(
        metadata=metadata,
        markdown_content=markdown,
        documents=[doc.model_dump() for doc in documents if check_document_is_valid(doc)],
        crawl_status=models.CrawlStatus.SUCCESS,
        source=source
    )


async def get_product_knowledge(url: str, source: str = "product_page") -> models.ProductKnowledge | None:
    knowledge = await models.ProductKnowledge.filter(metadata__url=url, source=source).order_by('-id').first()
    if not knowledge:
        logger.warning(f"Product knowledge for {url} not found in the database.")
        return None
    return knowledge


async def delete_product_knowledge(url: str, source: str = "product_page"):
    # 先查询出符合条件的记录，然后删除
    # 这样可以避免 Tortoise ORM 生成不兼容 PostgreSQL 的 DELETE 语句
    knowledge_records = await models.ProductKnowledge.filter(metadata__url=url, source=source).all()
    for record in knowledge_records:
        await record.delete()


async def get_product_knowledge_all_source(url: str) -> list[models.ProductKnowledge]:
    knowledge_all = await models.ProductKnowledge.filter(metadata__url=url).all()
    return [knowledge for knowledge in knowledge_all]


async def list_product_knowledge(domain: str) -> list[models.ProductKnowledge]:
    knowledge_all = await models.ProductKnowledge.filter(metadata__site__domain=domain).all()
    return [knowledge for knowledge in knowledge_all]


async def save_product_type_aspects(
        site_id: int, product_type: str, result: schema.ProductsAspects):
    await models.ProductTypeAspects.create(
        site_id=site_id,
        product_type=product_type,
        llm_determined_product_type=result.product_type,
        aspects=[aspect.model_dump() for aspect in result.aspect_list],
        crawl_status=models.CrawlStatus.SUCCESS,
    )


async def list_product_type_aspects(site_id: int, product_type: str) -> schema.ProductsAspects | None:
    existed = await models.ProductTypeAspects.filter(site_id=site_id, product_type=product_type).get_or_none()
    if not existed:
        return None
    else:
        return schema.ProductsAspects(
            product_type=existed.product_type,
            aspect_list=[schema.NameAndDescription.model_validate(item) for item in existed.aspects]
        )


async def save_product_type_scenarios(
        product_type: str, scenarios: List[Dict[str, str]]):
    await models.ProductTypeScenarios.create(
        product_type=product_type,
        scenarios=scenarios,
        crawl_status=models.CrawlStatus.SUCCESS
    )


async def get_product_type_scenarios(product_type: str) -> List[Dict[str, str]] | None:
    existed: models.ProductTypeScenarios = await models.ProductTypeScenarios.filter(product_type=product_type).first()
    if not existed:
        return None
    else:
        return existed.scenarios


async def list_product_type_scenarios(product_type: str) -> list[schema.ProductTypeScenario]:
    existed: models.ProductTypeScenarios = await models.ProductTypeScenarios.filter(product_type=product_type).first()
    if not existed:
        return []
    else:
        res = []
        for scenario in existed.scenarios:
            item = schema.ProductTypeScenario(label=scenario.get('label', ''),
                                              explanation=scenario.get('explanation', ''))
            if item.label and item.explanation:
                res.append(item)
        return res


async def list_expanded_product_type_aspects(site_id: int, product_type: str) -> schema.ProductsAspects | None:
    existed = await models.ProductTypeExpandedAspects.filter(site_id=site_id, product_type=product_type).get_or_none()
    if not existed:
        return None
    else:
        return schema.ProductsAspects(
            product_type=existed.product_type,
            aspect_list=[schema.NameAndDescription.model_validate(item) for item in existed.aspects]
        )


async def list_all_expanded_product_type_aspects(site_domain: str) -> Dict[str, list[schema.NameAndDescription]]:
    site = await models.ShopifySite.filter(domain=site_domain).get_or_none()
    if not site:
        logger.warning(f"Shopify site {site_domain} not found in the database.")
        return {}
    existed = await models.ProductTypeExpandedAspects.filter(site_id=site.id).all()
    if not existed:
        return {}
    else:
        return {
            item.product_type: [schema.NameAndDescription.model_validate(aspect) for aspect in item.aspects]
            for item in existed
        }


async def list_product_type_concerns(site_id: int, product_type: str) -> schema.ProductsConcerns | None:
    existed = await models.ProductTypeConcerns.filter(site_id=site_id, product_type=product_type).get_or_none()
    if not existed:
        return None
    else:
        return schema.ProductsConcerns(
            product_type=existed.product_type,
            concern_list=[schema.NameAndDescription.model_validate(item) for item in existed.concerns]
        )


async def save_product_type_concerns(
        site_id: int, product_type: str, result: schema.ProductsConcerns):
    await models.ProductTypeConcerns.create(
        site_id=site_id,
        product_type=product_type,
        llm_determined_product_type=result.product_type,
        concerns=[concern.model_dump() for concern in result.concern_list],
        crawl_status=models.CrawlStatus.SUCCESS,
    )


async def save_expanded_product_type_aspects(
        site_id: int, product_type: str, result: schema.ProductsAspects):
    await models.ProductTypeExpandedAspects.create(
        site_id=site_id,
        product_type=product_type,
        llm_determined_product_type=result.product_type,
        aspects=[aspect.model_dump() for aspect in result.aspect_list],
        crawl_status=models.CrawlStatus.SUCCESS,
    )


async def save_expanded_product_type_concerns(
        site_id: int, product_type: str, result: schema.ProductsConcerns):
    await models.ProductTypeExpandedConcerns.create(
        site_id=site_id,
        product_type=product_type,
        llm_determined_product_type=result.product_type,
        concerns=[concern.model_dump() for concern in result.concern_list],
        crawl_status=models.CrawlStatus.SUCCESS,
    )


async def list_expanded_product_type_concerns(site_id: int, product_type: str) -> schema.ProductsConcerns | None:
    existed = await models.ProductTypeExpandedConcerns.filter(site_id=site_id, product_type=product_type).get_or_none()
    if not existed:
        return None
    else:
        return schema.ProductsConcerns(
            product_type=existed.product_type,
            concern_list=[schema.NameAndDescription.model_validate(item) for item in existed.concerns]
        )


async def list_all_expanded_product_type_concerns(site_domain: str) -> dict[str, list[schema.NameAndDescription]]:
    site = await models.ShopifySite.filter(domain=site_domain).get_or_none()
    if not site:
        logger.warning(f"Shopify site {site_domain} not found in the database.")
        return {}
    existed = await models.ProductTypeExpandedConcerns.filter(site_id=site.id).all()
    if not existed:
        return {}
    else:
        return {
            item.product_type: [schema.NameAndDescription.model_validate(concern) for concern in item.concerns]
            for item in existed
        }


async def get_product_selling_points(url: str) -> list[schema.ProductSellingPoint]:
    product = await models.ProductMetadata.filter(url=url).get_or_none()
    if not product:
        logger.warning(f"Product {url} not found in the database.")
        return []
    # 过滤掉已删除的卖点
    selling_points = await product.selling_points.all()
    return [schema.ProductSellingPoint(
        name=point.name,
        value=point.description,
        marketing_copies=point.marketing_copies,
    ) for point in selling_points]


async def get_product_concern_points(url: str) -> list[schema.ProductConcernPoint]:
    product = await models.ProductMetadata.filter(url=url).get_or_none()
    if not product:
        logger.warning(f"Product {url} not found in the database.")
        return []
    selling_points = await product.concern_points.all()
    return [schema.ProductConcernPoint(
        concern=point.name,
        marketing_copies=point.marketing_copies,
    ) for point in selling_points]


async def list_domain_selling_points(
        domain: str,  should_match_crawl_strategy: bool = True) -> list[schema.ProductMetaAndSellingPoint]:
    """
    List all products with their selling points for a given domain.

    Args:
        domain: 站点域名
        should_match_crawl_strategy: 是否根据爬取策略筛选产品
    """
    site = await models.ShopifySite.filter(domain=domain).get_or_none()
    if not site:
        logger.warning(f"Shopify site {domain} not found in the database.")
        return []

    # 根据策略筛选产品
    if should_match_crawl_strategy:
        filtered_product_ids = await _get_filtered_product_ids_by_strategy(domain, 'product_selling_point')
        if filtered_product_ids is not None:  # 策略不是 'all'
            if not filtered_product_ids:  # 没有符合策略的产品
                return []
            products = await (models.ProductMetadata
                              .filter(site=site, product_id__in=filtered_product_ids)
                              .prefetch_related('selling_points').all())
        else:  # 策略是 'all'，获取所有产品
            products = await models.ProductMetadata.filter(site=site).prefetch_related('selling_points').all()
    else:
        # 原来的逻辑，获取所有产品
        products = await models.ProductMetadata.filter(site=site).prefetch_related('selling_points').all()

    result = []
    for product in products:
        # 过滤掉已删除的卖点
        selling_points = await product.selling_points.all()
        for sp in selling_points:
            result.append(await _convert_product_selling_point_to_pydantic(sp, product))
    return result


def get_status_order_case():
    """
    获取状态排序的 Case 表达式
    优先级：fresh < editing < ready < disabled < archived
    """
    return Case(
        When(status='fresh', then='1'),
        When(status='editing', then='2'),
        When(status='ready', then='3'),
        When(status='disabled', then='4'),
        When(status='archived', then='5'),
        default='6'
    )


async def list_domain_selling_points_paginated(
        domain: str, page: int, size: int,
        should_match_crawl_strategy: bool = True) -> schema.PaginatedResponse[schema.ProductMetaAndSellingPoint]:
    """
    以分页的方式列出指定域名下所有产品的卖点信息.
    按状态优先级排序：fresh -> editing -> ready -> disabled -> archived
    包含各状态的统计信息

    Args:
        domain: 站点域名
        page: 页码
        size: 每页大小
        should_match_crawl_strategy: 是否根据爬取策略筛选产品
    """
    site = await models.ShopifySite.filter(domain=domain).get_or_none()
    if not site:
        return schema.PaginatedResponse(
            items=[],
            page=page,
            size=size,
            total=0,
            total_pages=0,
        )

    # 根据策略筛选产品
    if should_match_crawl_strategy:
        filtered_product_ids = await _get_filtered_product_ids_by_strategy(domain, 'product_selling_point')
        if filtered_product_ids is not None:  # 策略不是 'all'
            if not filtered_product_ids:  # 没有符合策略的产品
                return schema.PaginatedResponse(
                    items=[],
                    page=page,
                    size=size,
                    total=0,
                    total_pages=0,
                )
            qs = models.ProductSellingPoint.filter(product__site=site, product__product_id__in=filtered_product_ids)
        else:  # 策略是 'all'，获取所有产品
            qs = models.ProductSellingPoint.filter(product__site=site)
    else:
        # 原来的逻辑，获取所有产品
        qs = models.ProductSellingPoint.filter(product__site=site)

    offset = (page - 1) * size

    # 按status排序，确保fresh数据优先
    selling_points = await qs.prefetch_related('product').annotate(
        status_order=get_status_order_case()
    ).order_by('status_order').offset(offset).limit(size).all()

    result = []
    for sp in selling_points:
        result.append(await _convert_product_selling_point_to_pydantic(sp, sp.product))

    total_count = await qs.count()
    total_pages = (total_count + size - 1) // size

    return schema.PaginatedResponse(
        items=result,
        page=page,
        size=size,
        total=total_count,
        total_pages=total_pages,
    )


def _convert_product_concern_point_to_pydantic(
        cp: models.ProductConcernPoint,
        product: models.ProductMetadata) -> schema.ProductMetaAndConcern:
    return schema.ProductMetaAndConcern(
        id=cp.id,
        product_url=product.url,
        product_id=product.product_id,
        product_type=product.product_type,
        product_title=product.title,
        product_price=product.price,
        concern=cp.name,
        concern_chn=cp.name_chn or '',
        marketing_copies=cp.marketing_copies or [],
        marketing_copies_chn=cp.marketing_copies_chn or [],
        status=cp.status,
        created_at=cp.created_at,
        last_edited_at=cp.last_edited_at,
        version_id=cp.version_id,
    )


async def list_domain_concern_points(
        domain: str, should_match_crawl_strategy: bool = True) -> list[schema.ProductMetaAndConcern]:
    """
    List all products with their concern points for a given domain.

    Args:
        domain: 站点域名
        should_match_crawl_strategy: 是否根据爬取策略筛选产品
    """
    site = await models.ShopifySite.filter(domain=domain).get_or_none()
    if not site:
        logger.warning(f"Shopify site {domain} not found in the database.")
        return []

    # 根据策略筛选产品
    if should_match_crawl_strategy:
        filtered_product_ids = await _get_filtered_product_ids_by_strategy(domain, 'product_concern_point')
        if filtered_product_ids is not None:  # 策略不是 'all'
            if not filtered_product_ids:  # 没有符合策略的产品
                return []
            products = await (models.ProductMetadata
                              .filter(site=site, product_id__in=filtered_product_ids)
                              .prefetch_related('concern_points').all())
        else:  # 策略是 'all'，获取所有产品
            products = await models.ProductMetadata.filter(site=site).prefetch_related('concern_points').all()
    else:
        # 原来的逻辑，获取所有产品
        products = await models.ProductMetadata.filter(site=site).prefetch_related('concern_points').all()

    result = []
    for product in products:
        # 过滤掉已删除的顾虑点
        concern_points = await product.concern_points.all()
        for cp in concern_points:
            result.append(_convert_product_concern_point_to_pydantic(cp, product))
    return result


async def list_domain_concern_points_paginated(
        domain: str,
        page: int,
        size: int,
        should_match_crawl_strategy: bool = True) -> schema.PaginatedResponse[schema.ProductMetaAndConcern]:
    """
    以分页的方式列出指定域名下所有产品的顾虑点信息.
    按状态优先级排序：fresh -> editing -> ready -> disabled -> archived
    包含各状态的统计信息

    Args:
        domain: 站点域名
        page: 页码
        size: 每页大小
        should_match_crawl_strategy: 是否根据爬取策略筛选产品
    """
    site = await models.ShopifySite.filter(domain=domain).get_or_none()
    if not site:
        return schema.PaginatedResponse(
            items=[],
            page=page,
            size=size,
            total=0,
            total_pages=0,
        )

    # 根据策略筛选产品
    if should_match_crawl_strategy:
        filtered_product_ids = await _get_filtered_product_ids_by_strategy(domain, 'product_concern_point')
        if filtered_product_ids is not None:  # 策略不是 'all'
            if not filtered_product_ids:  # 没有符合策略的产品
                return schema.PaginatedResponse(
                    items=[],
                    page=page,
                    size=size,
                    total=0,
                    total_pages=0,
                )
            qs = models.ProductConcernPoint.filter(product__site=site, product__product_id__in=filtered_product_ids)
        else:  # 策略是 'all'，获取所有产品
            qs = models.ProductConcernPoint.filter(product__site=site)
    else:
        # 原来的逻辑，获取所有产品
        qs = models.ProductConcernPoint.filter(product__site=site)

    offset = (page - 1) * size

    # 按status排序，确保fresh数据优先
    concern_points = await qs.prefetch_related('product').annotate(
        status_order=get_status_order_case()
    ).order_by('status_order').offset(offset).limit(size).all()

    result = []
    for cp in concern_points:
        result.append(_convert_product_concern_point_to_pydantic(cp, cp.product))

    total_count = await qs.count()
    total_pages = (total_count + size - 1) // size
    
    return schema.PaginatedResponse(
        items=result,
        page=page,
        size=size,
        total=total_count,
        total_pages=total_pages,
    )


def _convert_product_faq_to_pydantic(record: models.ProductFaqs, product: models.ProductMetadata,
                                     shopify_domain: str):
    faq_items = []
    if record.faqs:
        for faq_item in record.faqs:
            faq_items.append(schema.FaqItem(
                question=faq_item.get('question', ''),
                answer=faq_item.get('answer', ''),
                question_chn=faq_item.get('question_chn', ''),
                answer_chn=faq_item.get('answer_chn', ''),
            ))
    return schema.ProductMetaAndFaq(
        id=record.id,
        shopify_domain=shopify_domain,
        product_url=product.url,
        product_id=product.product_id,
        product_type=product.product_type,
        product_title=product.title,
        product_price=product.price,
        faqs=faq_items,
        status=record.status,
        created_at=record.created_at,
        last_edited_at=record.last_edited_at,
        version_id=record.version_id,
    )


async def list_domain_faqs(
        domain: str, should_match_crawl_strategy: bool = True) -> list[schema.ProductMetaAndFaq]:
    """
    List all products with their FAQs for a given domain.

    Args:
        domain: 站点域名
        should_match_crawl_strategy: 是否根据爬取策略筛选产品
    """
    site = await models.ShopifySite.filter(domain=domain).get_or_none()
    if not site:
        logger.warning(f"Shopify site {domain} not found in the database.")
        return []

    # 根据策略筛选产品
    if should_match_crawl_strategy:
        filtered_product_ids = await _get_filtered_product_ids_by_strategy(domain, 'product_faq')
        if filtered_product_ids is not None:  # 策略不是 'all'
            if not filtered_product_ids:  # 没有符合策略的产品
                return []
            products = await models.ProductMetadata.filter(site=site, product_id__in=filtered_product_ids).all()
        else:  # 策略是 'all'，获取所有产品
            products = await models.ProductMetadata.filter(site=site).all()
    else:
        # 原来的逻辑，获取所有产品
        products = await models.ProductMetadata.filter(site=site).all()

    if not products:
        return []

    product_map = {p.id: p for p in products}
    faqs_records = await models.ProductFaqs.filter(product_id__in=product_map.keys()).all()

    result = []
    for record in faqs_records:
        product = product_map.get(record.product_id)
        if product:
            result.append(_convert_product_faq_to_pydantic(record, product, shopify_domain=site.shopify_domain))
    return result


async def delete_product_selling_points(url: str):
    product = await models.ProductMetadata.filter(url=url).get_or_none()
    if not product:
        logger.warning(f"Product {url} not found in the database.")
        return
    count = await models.ProductSellingPoint.filter(product=product).delete()
    logger.info(f"Cleared {count} selling points for product {url}")


async def delete_product_concern_points(url: str):
    product = await models.ProductMetadata.filter(url=url).get_or_none()
    if not product:
        logger.warning(f"Product {url} not found in the database.")
        return
    count = await models.ProductConcernPoint.filter(product=product).delete()
    logger.info(f"Cleared {count} concern points for product {url}")


async def save_product_selling_points(url: str,
                                      selling_points: list[schema.ProductSellingPoint]):
    product = await models.ProductMetadata.filter(url=url).get_or_none()
    if not product:
        logger.warning(f"Product {url} not found in the database.")
        return
    for point in selling_points:
        await models.ProductSellingPoint.create(
            product=product,
            name=point.name,
            description=point.value,
            marketing_copies=point.marketing_copies,
            status=models.SellingPointStatus.FRESH,  # 新创建的卖点默认为 fresh 状态
            crawl_status=models.CrawlStatus.SUCCESS
        )


async def save_product_concern_points(url: str,
                                      concern_points: list[schema.ProductConcernPoint]):
    product = await models.ProductMetadata.filter(url=url).get_or_none()
    if not product:
        logger.warning(f"Product {url} not found in the database.")
        return
    for point in concern_points:
        await models.ProductConcernPoint.create(
            product=product,
            name=point.concern,
            marketing_copies=point.marketing_copies,
            status=models.SellingPointStatus.FRESH,  # 新创建的顾虑点默认为 fresh 状态
            crawl_status=models.CrawlStatus.SUCCESS
        )


async def save_product_scenario_selling_points(
        url: str,
        scenario_selling_points: List[ScenarioSellingPoint]
):
    product = await models.ProductMetadata.filter(url=url).get_or_none()
    if not product:
        logger.warning(f"Product {url} not found in the database.")
        return
    for point in scenario_selling_points:
        await models.ProductScenarioSellingPoint.create(
            product=product,
            scenario=point.name,
            content=point.reason,
            crawl_status=models.CrawlStatus.SUCCESS
        )
    logger.info("Success save {} scenario selling points: {}", url, scenario_selling_points)


async def list_product_scenario_selling_points(domain: str) -> list[schema.ProductScenarioSellingPoint]:
    """找出一个站点的所有场景卖点."""
    site = await models.ShopifySite.filter(domain=domain).get_or_none()
    if not site:
        logger.warning(f"Shopify site {domain} not found in the database.")
        return []

    products = await models.ProductMetadata.filter(site=site).all()
    result = []
    for product in products:
        scenario_selling_points = await product.scenario_selling_points.all()
        points = []
        for point in scenario_selling_points:
            elements = await point.elements
            element_targets = [e.target for e in elements]
            points.append(
                schema.ScenarioAndContent(
                    scenario=point.scenario,
                    content=point.content,
                    element_targets=element_targets
                )
            )
        if points:
            result.append(
                schema.ProductScenarioSellingPoint(
                    product_url=product.url,
                    product_id=product.product_id,
                    selling_points=points
                )
            )
    return result


async def list_single_product_scenario_selling_points(url: str) -> list[schema.ScenarioAndContent]:
    """找出一个产品的所有场景卖点."""
    product = await models.ProductMetadata.filter(url=url).get_or_none()
    if not product:
        logger.warning(f"Product {url} not found in the database.")
        return []
    scenario_selling_points = await product.scenario_selling_points.all()
    points = []
    for point in scenario_selling_points:
        elements = await point.elements
        element_targets = [e.target for e in elements]
        points.append(
            schema.ScenarioAndContent(
                scenario=point.scenario,
                content=point.content,
                element_targets=element_targets
            )
        )
    return points


async def list_raw_product_scenario_selling_points(domain: str) -> list[models.ProductScenarioSellingPoint]:
    site = await models.ShopifySite.filter(domain=domain).get_or_none()
    if not site:
        logger.warning(f"Shopify site {domain} not found in the database.")
        return []

    products = await models.ProductMetadata.filter(site=site).all()
    result = []
    for product in products:
        scenario_selling_points = await product.scenario_selling_points.all()
        for point in scenario_selling_points:
            result.append(point)
    return result


async def delete_scenario_selling_points(url: str):
    product = await models.ProductMetadata.filter(url=url).get_or_none()
    if not product:
        logger.warning(f"Product {url} not found in the database.")
        return
    await models.ProductScenarioSellingPoint.filter(product=product).delete()
    logger.info(f"Cleared scenario selling points for product {url}")


async def get_product_search_enhancement(url: str) -> schema.ProductSearchEnhancement | None:
    product = await models.ProductMetadata.filter(url=url).get_or_none()
    if not product:
        logger.warning(f"Product {url} not found in the database.")
        return None
    enhancement = await models.ProductSearchEnhancement.filter(product=product).get_or_none()
    if not enhancement:
        return None
    return schema.ProductSearchEnhancement(
        new_title=enhancement.new_title,
        new_description=enhancement.new_description,
        new_product_type=enhancement.new_product_type,
        filterable_attributes=enhancement.filterable_attributes,
    )


async def delete_product_search_enhancement(url: str):
    product = await models.ProductMetadata.filter(url=url).get_or_none()
    if not product:
        logger.warning(f"Product {url} not found in the database.")
        return
    await models.ProductSearchEnhancement.filter(product=product).delete()
    logger.info(f"Cleared search enhancement for product {url}")


async def save_product_search_enhancement(url: str, enhancement: schema.ProductSearchEnhancement):
    product = await models.ProductMetadata.filter(url=url).get_or_none()
    if not product:
        logger.warning(f"Product {url} not found in the database.")
        return
    await models.ProductSearchEnhancement.create(
        product=product,
        **enhancement.model_dump(),
    )


def celery_task_to_pydantic(task: models.CeleryTask) -> schema.CeleryTaskDetail:
    """
    Convert a CeleryTask object to a Pydantic model.
    """
    return schema.CeleryTaskDetail(
        id=task.id,
        task_id=task.task_id,
        workflow_id=task.workflow_id,
        name=task.name,
        status=task.status,
        params=task.params,
        result=task.result,
        error=task.error,
        created_at=task.created_at,
        finished_at=task.finished_at,
        log_url=generate_log_url(task_id=task.task_id),
    )


async def list_celery_tasks(page: int = 1, size: int = 20, **kwargs) -> PaginatedResponse[schema.CeleryTaskDetail]:
    """
    List all celery tasks in the database with pagination.
    """
    logger.info(f"Listing celery tasks with filters: {kwargs}, page={page}, size={size}")
    
    queryset = models.CeleryTask.all()
    
    # 处理不同的过滤条件
    if kwargs.get('domain'):
        # 从 params JSON 字段中查找包含该域名的任务
        queryset = queryset.filter(params__filter={'domain': kwargs['domain']})
    
    if kwargs.get('product_url'):
        # 从 params JSON 字段中查找包含该产品URL的任务
        queryset = queryset.filter(params__filter={'product_url': kwargs['product_url']})
    
    if kwargs.get('task_name'):
        # 按任务名称过滤
        queryset = queryset.filter(name=kwargs['task_name'])
    
    if kwargs.get('status'):
        # 按任务状态过滤
        queryset = queryset.filter(status=kwargs['status'])
    
    total = await queryset.count()
    offset = (page - 1) * size
    tasks = await queryset.order_by('-id').offset(offset).limit(size)
    task_details = [celery_task_to_pydantic(task) for task in tasks]
    await fulfill_celery_task_progress(task_details)
    total_pages = (total + size - 1) // size if size else 1
    return PaginatedResponse[schema.CeleryTaskDetail](
        items=task_details,
        page=page,
        size=size,
        total=total,
        total_pages=total_pages
    )


async def create_task(name: str, params: dict) -> schema.CeleryTaskDetail:
    """
    Update or create a celery task record in the database.
    """
    task = await models.CeleryTask.create(
        task_id=str(uuid4()),
        name=name,
        status='PENDING',
        params=params,
    )
    task_detail = celery_task_to_pydantic(task)
    await fulfill_celery_task_progress(task_detail)
    return task_detail


async def fulfill_celery_task_progress(task_or_tasks: schema.CeleryTaskDetail | list[schema.CeleryTaskDetail]):
    """
    查询指定任务的进度信息，并将其填充到任务对象中.
    """
    if isinstance(task_or_tasks, schema.CeleryTaskDetail):
        task_or_tasks = [task_or_tasks]

    task_id_set = set()
    for task in task_or_tasks:
        if not isinstance(task, schema.CeleryTaskDetail):
            raise ValueError("Expected a CeleryTaskDetail instance.")
        task_id_set.add(task.id)
    tqdm_records = await models.TqdmRecord.filter(
        relate_id__in=task_id_set,
        relate_type='celery_task'
    ).all()
    tqdm_map = {record.relate_id: record for record in tqdm_records}
    for task in task_or_tasks:
        tqdm_record = tqdm_map.get(task.id)
        if tqdm_record:
            task.progress = schema.Progress(
                n=tqdm_record.n,
                total=tqdm_record.total,
                desc=tqdm_record.desc,
                last_updated_at=tqdm_record.updated_at,
            )


async def mark_celery_task_start(task_id: str, workflow_id: int | None,
                                 name: str, params: dict) -> schema.CeleryTaskDetail:
    """
    Update or create a celery task record in the database.
    """
    task = await models.CeleryTask.filter(task_id=task_id).get_or_none()
    if not task:
        task = models.CeleryTask(task_id=task_id, name=name, params=params)
    if workflow_id is not None:
        task.workflow_id = int(workflow_id)
    task.name = name
    task.status = models.CeleryTaskStatus.STARTED
    await task.save()
    if workflow_id is not None:
        await update_workflow_status_via_task(task)
    return celery_task_to_pydantic(task)


async def mark_celery_task_success(task_id: str, result) -> schema.CeleryTaskDetail | None:
    """
    Mark a celery task as successful in the database.
    """
    result = str(result) if result is not None else None
    task = await models.CeleryTask.get_or_none(task_id=task_id)
    if not task:
        logger.warning(f"Task {task_id} not found in the database.")
        return None
    task.status = models.CeleryTaskStatus.SUCCESS
    task.result = result
    task.finished_at = datetime.now()
    await task.save()
    await update_workflow_status_via_task(task)
    return celery_task_to_pydantic(task)


async def mark_celery_task_failure(task_id: str, error: str) -> schema.CeleryTaskDetail | None:
    """
    Mark a celery task as failed in the database.
    """
    task = await models.CeleryTask.get_or_none(task_id=task_id)
    if not task:
        logger.warning(f"Task {task_id} not found in the database.")
        return None
    task.status = models.CeleryTaskStatus.FAILED
    task.error = error
    task.finished_at = datetime.now()
    await task.save()
    await update_workflow_status_via_task(task)
    return celery_task_to_pydantic(task)


async def mark_celery_task_canceled(task_id: str, reason: str = "Task was canceled by user") -> schema.CeleryTaskDetail | None:
    """
    Mark a celery task as canceled in the database.
    """
    task = await models.CeleryTask.get_or_none(task_id=task_id)
    if not task:
        logger.warning(f"Task {task_id} not found in the database.")
        return None

    if task.status in (models.CeleryTaskStatus.SUCCESS, models.CeleryTaskStatus.FAILED, models.CeleryTaskStatus.CANCELED):
        logger.info(f"Task {task_id} is already finished with status {task.status}, cannot cancel.")
        return celery_task_to_pydantic(task)

    task.status = models.CeleryTaskStatus.CANCELED
    task.error = reason
    task.finished_at = datetime.now()
    await task.save()
    await update_workflow_status_via_task(task)
    logger.info(f"Task {task_id} marked as canceled.")
    return celery_task_to_pydantic(task)


async def mark_celery_task_retried(task_id: str) -> schema.CeleryTaskDetail | None:
    """
    Mark a celery task as retried in the database.
    """
    task = await models.CeleryTask.get_or_none(task_id=task_id)
    if not task:
        logger.warning(f"Task {task_id} not found in the database.")
        return None

    task.status = models.CeleryTaskStatus.RETRIED
    task.finished_at = datetime.now()
    await task.save()
    logger.info(f"Task {task_id} marked as retried.")
    return celery_task_to_pydantic(task)


async def get_celery_task(task_id: str) -> schema.CeleryTaskDetail | None:
    task = await models.CeleryTask.get_or_none(task_id=task_id)
    if not task:
        return None
    task_detail = celery_task_to_pydantic(task)
    await fulfill_celery_task_progress(task_detail)
    return task_detail


async def update_celery_task_progress(task_id: int, n: int, total: int, desc: str | None):
    await models.TqdmRecord.update_or_create(
        relate_id=task_id,
        relate_type='celery_task',
        defaults=dict(
            n=n,
            total=total,
            desc=(desc or '')[:255],  # Limit description length to 255 characters
        )
    )


async def delete_product_type_aspects(site_id: int, product_type: str):
    """
    Delete product type aspects for a given site and product type.
    """
    deleted_count = await models.ProductTypeAspects.filter(site_id=site_id, product_type=product_type).delete()
    if deleted_count == 0:
        logger.warning(f"No product type aspects found for site_id={site_id}, product_type={product_type}.")
    else:
        logger.info(f"Deleted {deleted_count} product type aspects for site_id={site_id}, product_type={product_type}.")


async def delete_expanded_product_type_aspects(site_id: int, product_type: str):
    """
    Delete expanded product type aspects for a given site and product type.
    """
    deleted_count = await models.ProductTypeExpandedAspects.filter(site_id=site_id, product_type=product_type).delete()
    if deleted_count == 0:
        logger.warning(f"No expanded product type aspects found for site_id={site_id}, product_type={product_type}.")
    else:
        logger.info(f"Deleted {deleted_count} expanded product type aspects for site_id={site_id},"
                    f" product_type={product_type}.")


async def delete_product_type_concerns(site_id: int, product_type: str):
    """
    Delete product type concerns for a given site and product type.
    """
    deleted_count = await models.ProductTypeConcerns.filter(site_id=site_id, product_type=product_type).delete()
    if deleted_count == 0:
        logger.warning(f"No product type concerns found for site_id={site_id}, product_type={product_type}.")
    else:
        logger.info(f"Deleted {deleted_count} product type concerns for site_id={site_id},"
                    f" product_type={product_type}.")


async def delete_expanded_product_type_concerns(site_id: int, product_type: str):
    """
    Delete expanded product type concerns for a given site and product type.
    """
    deleted_count = await models.ProductTypeExpandedConcerns.filter(site_id=site_id, product_type=product_type).delete()
    if deleted_count == 0:
        logger.warning(f"No expanded product type concerns found for site_id={site_id}, product_type={product_type}.")
    else:
        logger.info(f"Deleted {deleted_count} expanded product type concerns for site_id={site_id},"
                    f" product_type={product_type}.")


async def get_product_faqs(product_url: str) -> list[schema.Faq] | None:
    faqs_record = await models.ProductFaqs.filter(product__url=product_url).order_by('-id').first()
    if not faqs_record:
        logger.warning(f"Product FAQs for {product_url} not found in the database.")
        return None
    return [schema.Faq.model_validate(faq) for faq in faqs_record.faqs]


async def delete_product_faqs(product_url: str):
    # 先查询出符合条件的记录，然后删除
    # 这样可以避免 Tortoise ORM 生成不兼容 PostgreSQL 的 DELETE 语句
    faq_records = await models.ProductFaqs.filter(product__url=product_url).all()
    for record in faq_records:
        await record.delete()


async def save_product_faqs(product_url: str, faqs: list[schema.Faq]):
    product_metadata = await models.ProductMetadata.filter(url=product_url).get_or_none()
    if not product_metadata:
        logger.warning(f"Product metadata for {product_url} not found in the database.")
        return
    await models.ProductFaqs.create(
        product=product_metadata,
        faqs=[faq.model_dump() for faq in faqs],
        crawl_status=models.CrawlStatus.SUCCESS
    )


async def create_data_version(
        data_type: str,
        data_id: int,
        value: dict,
        comment: str | None = None,
        prev_id: int | None = None
) -> models.DataVersion:
    """创建数据版本记录"""
    version = await models.DataVersion.create(
        data_type=data_type,
        data_id=data_id,
        value=value,
        comment=comment,
        prev_id=prev_id
    )
    logger.info(f"Created data version for {data_type} {data_id}")
    return version


async def get_data_version_history(data_type: str, data_id: int) -> list[models.DataVersion]:
    """获取数据版本历史"""
    versions = await models.DataVersion.filter(
        data_type=data_type,
        data_id=data_id
    ).order_by('-created_at').all()
    return versions


async def update_selling_point_with_version(
        selling_point_id: int,
        update_data: schema.SellingPointUpdateRequest
) -> bool:
    """
    更新卖点数据并创建版本记录
    """
    selling_point = await models.ProductSellingPoint.filter(id=selling_point_id).get_or_none()

    if not selling_point:
        logger.warning(f"Selling point with ID {selling_point_id} not found")
        return False

    # 准备当前版本的值
    current_value = {
        'name': selling_point.name,
        'description': selling_point.description,
        'marketing_copies': selling_point.marketing_copies,
        'name_chn': selling_point.name_chn,
        'description_chn': selling_point.description_chn,
        'marketing_copies_chn': selling_point.marketing_copies_chn,
        'status': selling_point.status.value,
    }

    # 更新字段
    if update_data.name is not None:
        selling_point.name = update_data.name
    if update_data.description is not None:
        selling_point.description = update_data.description
    if update_data.marketing_copies is not None:
        selling_point.marketing_copies = update_data.marketing_copies
    if update_data.name_chn is not None:
        selling_point.name_chn = update_data.name_chn
    if update_data.description_chn is not None:
        selling_point.description_chn = update_data.description_chn
    if update_data.marketing_copies_chn is not None:
        selling_point.marketing_copies_chn = update_data.marketing_copies_chn
    if update_data.status is not None:
        selling_point.status = update_data.status

    # 更新编辑时间
    selling_point.last_edited_at = datetime.now()

    # 创建版本记录
    prev_version_id = selling_point.version_id if selling_point.version else None
    new_version = await create_data_version(
        data_type="product_selling_point",
        data_id=selling_point_id,
        value=current_value,
        comment=update_data.comment,
        prev_id=prev_version_id
    )

    # 更新卖点的版本引用
    selling_point.version = new_version
    await selling_point.save()

    logger.info(f"Updated selling point {selling_point_id} with version {new_version.id}")
    return True


async def update_concern_point_with_version(concern_id: int, update_data: schema.ProductConcernUpdateRequest) -> bool:
    """
    更新顾虑点数据并创建版本记录
    """
    concern_point = await models.ProductConcernPoint.filter(id=concern_id).get_or_none()

    if not concern_point:
        logger.warning(f"Concern point with ID {concern_id} not found")
        return False

    # 准备当前版本的值
    current_value = {
        'name': concern_point.name,
        'marketing_copies': concern_point.marketing_copies,
        'name_chn': concern_point.name_chn,
        'marketing_copies_chn': concern_point.marketing_copies_chn,
        'status': concern_point.status.value,
    }

    # 更新字段
    if update_data.marketing_copies is not None:
        concern_point.marketing_copies = update_data.marketing_copies
    if update_data.marketing_copies_chn is not None:
        concern_point.marketing_copies_chn = update_data.marketing_copies_chn
    if update_data.status is not None:
        concern_point.status = update_data.status

    # 更新编辑时间
    concern_point.last_edited_at = datetime.now()

    # 创建版本记录
    prev_version_id = concern_point.version_id if concern_point.version else None
    new_version = await create_data_version(
        data_type="product_concern",
        data_id=concern_id,
        value=current_value,
        comment=update_data.comment,
        prev_id=prev_version_id
    )

    # 更新顾虑点的版本引用
    concern_point.version = new_version
    await concern_point.save()

    logger.info(f"Updated concern point {concern_id} with version {new_version.id}")
    return True


async def get_selling_point_versions(selling_point_id: int) -> list[schema.DataVersion]:
    """获取卖点的版本历史"""
    versions = await get_data_version_history("product_selling_point", selling_point_id)
    return [
        schema.DataVersion(
            id=v.id,
            prev_id=v.prev_id,
            value=v.value,
            data_type=v.data_type,
            data_id=v.data_id,
            comment=v.comment,
            created_at=v.created_at
        )
        for v in versions
    ]


async def get_concern_point_versions(concern_id: int) -> list[schema.DataVersion]:
    """获取顾虑点的版本历史"""
    versions = await get_data_version_history("product_concern", concern_id)
    return [
        schema.DataVersion(
            id=v.id,
            prev_id=v.prev_id,
            value=v.value,
            data_type=v.data_type,
            data_id=v.data_id,
            comment=v.comment,
            created_at=v.created_at
        )
        for v in versions
    ]


async def _convert_product_selling_point_to_pydantic(sp: models.ProductSellingPoint,
        product: models.ProductMetadata,
        need_elements: bool = False) -> schema.ProductMetaAndSellingPoint:
    if need_elements:
        elements = await sp.elements
        element_targets = [element.target for element in elements]
    else:
        element_targets = []
    return schema.ProductMetaAndSellingPoint(
        id=sp.id,
        product_url=product.url,
        product_id=product.product_id,
        product_type=product.product_type,
        product_title=product.title,
        product_price=product.price,
        selling_point_name=sp.name,
        selling_point_value=sp.description,
        selling_point_marketing_copies=sp.marketing_copies or [],
        selling_point_name_chn=sp.name_chn or '',
        selling_point_value_chn=sp.description_chn or '',
        selling_point_marketing_copies_chn=sp.marketing_copies_chn or [],
        status=sp.status,
        created_at=sp.created_at,
        last_edited_at=sp.last_edited_at,
        version_id=sp.version_id,
        element_targets=element_targets
    )


async def list_selling_points_by_status(
        domain: str,
        status: schema.SellingPointStatus | None = None,
        should_match_crawl_strategy: bool = True) -> list[schema.ProductMetaAndSellingPoint]:
    """
    根据状态筛选卖点

    Args:
        domain: 站点域名
        status: 卖点状态
        should_match_crawl_strategy: 是否根据爬取策略筛选产品
    """
    site = await models.ShopifySite.filter(domain=domain).get_or_none()
    if not site:
        logger.warning(f"Shopify site {domain} not found in the database.")
        return []

    # 根据策略筛选产品
    if should_match_crawl_strategy:
        filtered_product_ids = await _get_filtered_product_ids_by_strategy(domain, 'product_selling_point')
        if filtered_product_ids is not None:  # 策略不是 'all'
            if not filtered_product_ids:  # 没有符合策略的产品
                return []
            query = models.ProductSellingPoint.filter(product__site=site, product__product_id__in=filtered_product_ids)
        else:  # 策略是 'all'，获取所有产品
            query = models.ProductSellingPoint.filter(product__site=site)
    else:
        # 原来的逻辑，获取所有产品
        query = models.ProductSellingPoint.filter(product__site=site)

    if status:
        query = query.filter(status=status)

    selling_points = await query.prefetch_related('product').all()

    result = []
    for sp in selling_points:
        result.append(await _convert_product_selling_point_to_pydantic(sp, sp.product, need_elements=True))
    return result


async def update_faq_with_version(
        faq_id: int,
        update_data: schema.FaqUpdateRequest
) -> bool:
    """
    更新FAQ数据并创建版本记录
    """
    faq_record = await models.ProductFaqs.filter(id=faq_id).get_or_none()

    if not faq_record:
        logger.warning(f"FAQ record with ID {faq_id} not found")
        return False

    # 准备当前版本的值
    current_value = {
        'faqs': faq_record.faqs,
        'status': faq_record.status.value,
    }

    # 更新FAQ内容
    if update_data.faqs is not None:
        # 将FaqItem对象转换为字典格式
        faq_dicts = []
        for faq_item in update_data.faqs:
            faq_dicts.append({
                'question': faq_item.question,
                'answer': faq_item.answer,
                'question_chn': faq_item.question_chn,
                'answer_chn': faq_item.answer_chn,
            })
        faq_record.faqs = faq_dicts

    # 更新状态
    if update_data.status is not None:
        faq_record.status = update_data.status

    # 更新编辑时间
    faq_record.last_edited_at = datetime.now()

    # 创建版本记录
    prev_version_id = faq_record.version_id if faq_record.version else None
    new_version = await create_data_version(
        data_type="product_faq",
        data_id=faq_id,
        value=current_value,
        comment=update_data.comment,
        prev_id=prev_version_id
    )

    # 更新FAQ记录的版本引用
    faq_record.version = new_version
    await faq_record.save()

    logger.info(f"Updated FAQ {faq_id} with version {new_version.id}")
    return True


async def list_faqs_by_status(
        domain: str,
        status: schema.SellingPointStatus | None = None,
        should_match_crawl_strategy: bool = True) -> list[schema.ProductMetaAndFaq]:
    """
    根据状态筛选FAQ

    Args:
        domain: 站点域名
        status: FAQ状态
        should_match_crawl_strategy: 是否根据爬取策略筛选产品
    """
    site = await models.ShopifySite.filter(domain=domain).get_or_none()
    if not site:
        logger.warning(f"Shopify site {domain} not found in the database.")
        return []

    # 根据策略筛选产品
    if should_match_crawl_strategy:
        filtered_product_ids = await _get_filtered_product_ids_by_strategy(domain, 'product_faq')
        if filtered_product_ids is not None:  # 策略不是 'all'
            if not filtered_product_ids:  # 没有符合策略的产品
                return []
            query = models.ProductFaqs.filter(product__site=site, product__product_id__in=filtered_product_ids)
        else:  # 策略是 'all'，获取所有产品
            query = models.ProductFaqs.filter(product__site=site)
    else:
        # 原来的逻辑，获取所有产品
        query = models.ProductFaqs.filter(product__site=site)

    if status:
        query = query.filter(status=status)

    faqs_records = await query.prefetch_related('product').all()

    result = []
    for record in faqs_records:
        result.append(_convert_product_faq_to_pydantic(record, record.product, site.shopify_domain))
    return result


async def get_faq_versions(faq_id: int) -> list[schema.DataVersion]:
    """获取FAQ的版本历史"""
    faq_record = await models.ProductFaqs.filter(id=faq_id).get_or_none()
    if not faq_record:
        logger.warning(f"FAQ record with ID {faq_id} not found")
        return []

    versions = []
    current_version = faq_record.version
    while current_version:
        versions.append(schema.DataVersion(
            id=current_version.id,
            data_type=current_version.data_type,
            data_id=current_version.data_id,
            value=current_version.value,
            comment=current_version.comment,
            created_at=current_version.created_at,
            prev_id=current_version.prev_id
        ))
        if current_version.prev_id:
            current_version = await models.DataVersion.filter(id=current_version.prev_id).get_or_none()
        else:
            break

    return list(reversed(versions))


async def list_concerns_by_status(
        domain: str,
        status: schema.SellingPointStatus | None = None,
        should_match_crawl_strategy: bool = True) -> list[schema.ProductMetaAndConcern]:
    """
    根据状态筛选顾虑点

    Args:
        domain: 站点域名
        status: 顾虑点状态
        should_match_crawl_strategy: 是否根据爬取策略筛选产品
    """
    site = await models.ShopifySite.filter(domain=domain).get_or_none()
    if not site:
        logger.warning(f"Shopify site {domain} not found in the database.")
        return []

    # 根据策略筛选产品
    if should_match_crawl_strategy:
        filtered_product_ids = await _get_filtered_product_ids_by_strategy(domain, 'product_concern_point')
        if filtered_product_ids is not None:  # 策略不是 'all'
            if not filtered_product_ids:  # 没有符合策略的产品
                return []
            query = models.ProductConcernPoint.filter(product__site=site, product__product_id__in=filtered_product_ids)
        else:  # 策略是 'all'，获取所有产品
            query = models.ProductConcernPoint.filter(product__site=site)
    else:
        # 原来的逻辑，获取所有产品
        query = models.ProductConcernPoint.filter(product__site=site)

    if status:
        query = query.filter(status=status)

    concern_points = await query.prefetch_related('product').all()

    result = []
    for cp in concern_points:
        result.append(_convert_product_concern_point_to_pydantic(cp, cp.product))
    return result


async def save_collection_metadata(domain: str, *collections: schema.CollectionMetaData):
    site = await get_site(domain)
    if not site:
        site = await init_site_metadata(domain)
    for collection in collections:
        await models.CollectionMetadata.update_or_create(
            url=collection.url,
            collection_id=collection.collection_id,
            defaults={
                "site": site,
                "title": collection.title if collection.title else "",
                "description": collection.description if collection.description else "",
                "product_ids": collection.product_ids,
                "raw_content": collection.raw_content,
            }
        )
    logger.info(f"Saved {len(collections)} collection metadata for {domain}")


async def list_collection_metadata(domain: str) -> list[schema.CollectionMetaData]:
    site = await get_site(domain)
    if not site:
        return []
    return await models.CollectionMetadata.filter(site=site).all()


async def get_collection_metadata(url: str) -> models.CollectionMetadata | None:
    return await models.CollectionMetadata.filter(url=url).order_by("-id").first()


async def save_page_metadata(domain: str, *pages: 'schema.PageMetaData'):
    site = await get_site(domain)
    if not site:
        site = await init_site_metadata(domain)
    for page in pages:
        await models.PageMetadata.update_or_create(
            url=page.url,
            defaults={
                "raw_content": page.raw_content,
                "site": site,
            }
        )
    logger.info(f"Saved {len(pages)} page metadata for {domain}")


async def save_blog_metadata(domain: str, *blogs: 'schema.BlogMetaData'):
    site = await get_site(domain)
    if not site:
        site = await init_site_metadata(domain)
    for blog in blogs:
        await models.BlogMetadata.update_or_create(
            url=blog.url,
            defaults={
                "raw_content": blog.raw_content,
                "site": site,
            }
        )
    logger.info(f"Saved {len(blogs)} blog metadata for {domain}")


async def save_product_type_pv_schema(domain: str, product_type: str, schema: schema.ProductTypePvSchema):
    site = await get_site(domain)
    if not site:
        site = await init_site_metadata(domain)
    await models.ProductTypePvSchema.update_or_create(
        site=site,
        product_type=product_type,
        defaults={
            "properties": schema.properties,
        }
    )
    logger.info(f"Saved product type pv schema for {product_type} in {domain}")


async def get_product_type_pv_schema(domain: str, product_type: str) -> schema.ProductTypePvSchema | None:
    site = await get_site(domain)
    if not site:
        return None
    return await models.ProductTypePvSchema.filter(site=site, product_type=product_type).order_by('-id').first()


async def delete_product_type_pv_schema(domain: str, product_type: str):
    site = await get_site(domain)
    if not site:
        return
    await models.ProductTypePvSchema.filter(site=site, product_type=product_type).delete()
    logger.info(f"Deleted product type pv schema for {product_type} in {domain}")


async def delete_all_product_type_pv_schema(domain: str):
    site = await get_site(domain)
    if not site:
        return
    await models.ProductTypePvSchema.filter(site=site).delete()
    logger.info(f"Deleted all product type pv schema in {domain}")


async def get_product_type_list(domain: str) -> list[str]:
    site = await get_site(domain)
    if not site:
        return []
    product_types = await models.ProductMetadata.filter(site=site).values_list('product_type', flat=True)
    return list(set(product_types))


async def save_product_property_and_value(metadata: models.ProductMetadata, property_values: dict):
    await models.ProductPropertyAndValue.update_or_create(
        metadata=metadata,
        defaults={
            "property_values": property_values,
        }
    )
    logger.info(f"Saved product property and value for {metadata.url}")


async def list_product_property_and_value(domain: str) -> list[models.ProductPropertyAndValue]:
    site = await get_site(domain)
    if not site:
        return []
    return await models.ProductPropertyAndValue.filter(metadata__site=site).all()


async def get_product_property_and_value(metadata: models.ProductMetadata) -> models.ProductPropertyAndValue | None:
    return await models.ProductPropertyAndValue.filter(metadata=metadata).order_by('-id').first()


async def delete_product_property_and_value(metadata: models.ProductMetadata):
    await models.ProductPropertyAndValue.filter(metadata=metadata).delete()
    logger.info(f"Deleted product property and value for {metadata.url}")


async def save_product_keywords(metadata: models.ProductMetadata, keywords: list[str]):
    await models.ProductKeyword.update_or_create(
        product=metadata,
        defaults={
            "keywords": keywords,
        }
    )
    logger.info(f"Saved product keywords for {metadata.url}")


async def get_product_keywords(metadata: models.ProductMetadata) -> models.ProductKeyword | None:
    return await models.ProductKeyword.filter(product=metadata).order_by('-id').first()


async def delete_product_keywords(metadata: models.ProductMetadata):
    await models.ProductKeyword.filter(product=metadata).delete()
    logger.info(f"Deleted product keywords for {metadata.url}")


async def list_product_keywords(domain: str) -> list[models.ProductKeyword]:
    site = await get_site(domain)
    if not site:
        return []
    return await models.ProductKeyword.filter(product__site=site).all()


async def save_product_summary(metadata: models.ProductMetadata, summary: str):
    await models.ProductSummary.update_or_create(
        product=metadata,
        defaults={
            "summary": summary,
        }
    )
    logger.info(f"Saved product summary for {metadata.url}")


async def get_product_summary(metadata: models.ProductMetadata) -> models.ProductSummary | None:
    return await models.ProductSummary.filter(product=metadata).order_by('-id').first()


async def delete_product_summary(metadata: models.ProductMetadata):
    await models.ProductSummary.filter(product=metadata).delete()
    logger.info(f"Deleted product summary for {metadata.url}")


async def save_product_type(metadata: models.ProductMetadata, product_type: str):
    await models.ProductMetadata.filter(id=metadata.id).update(product_type=product_type)
    logger.info(f"Saved product type for {metadata.url}")


async def save_store_knowledge_points(domain: str, knowledge_points: list['schema.StoreKnowledgePoint']):
    """
    批量保存店铺知识点
    """
    site = await get_site(domain)
    if not site:
        site = await init_site_metadata(domain)
    for kp in knowledge_points:
        await models.StoreKnowledgePoint.update_or_create(
            point_id=kp.point_id,
            defaults={
                "topic": kp.topic,
                "question": kp.question,
                "answer": kp.answer,
                "source": kp.source,
                "source_detail": kp.source_detail,
                "quality": kp.quality,
                "label": kp.label,
                "detailed_label": kp.detailed_label,
                "extra_questions": kp.extra_questions,
                "is_deleted": kp.is_deleted,
                "site": site,
            }
        )
    logger.info(f"Saved {len(knowledge_points)} store knowledge points for {domain}")


async def get_store_knowledge_value(domain: str, label: str, detailed_label: str) -> models.StoreKnowledgeValue | None:
    return (
        await models.StoreKnowledgeValue.filter(
            site__domain=domain, label=label, detailed_label=detailed_label
        )
        .order_by("-id")
        .first()
    )


async def save_store_knowledge_value(domain: str, label: str, detailed_label: str, value: str):
    """
    保存店铺知识归纳值
    """
    site = await get_site(domain)
    if not site:
        site = await init_site_metadata(domain)
    await models.StoreKnowledgeValue.update_or_create(
        label=label,
        detailed_label=detailed_label,
        defaults={
            "value": value,
            "site": site,
        }
    )


async def list_pages_raw_pages(domain: str) -> list[models.PageMetadata]:
    site = await get_site(domain)
    if not site:
        return []
    raw_pages = await models.PageMetadata.filter(site=site).all()
    return [page for page in raw_pages]


async def list_blogs_raw_pages(domain: str) -> list[models.BlogMetadata]:
    site = await get_site(domain)
    if not site:
        return []
    raw_pages = await models.BlogMetadata.filter(site=site).all()
    return [page for page in raw_pages]


async def list_store_knowledge(domain: str) -> list[models.StoreKnowledgePoint]:
    site = await get_site(domain)
    if not site:
        return []
    knowledge_all = await models.StoreKnowledgePoint.filter(site=site).all()
    return [knowledge for knowledge in knowledge_all]


async def list_store_knowledge_value(domain: str) -> list[models.StoreKnowledgeValue]:
    site = await get_site(domain)
    if not site:
        return []
    knowledge_all = await models.StoreKnowledgeValue.filter(site=site).all()
    return [knowledge for knowledge in knowledge_all]


async def save_store_document_faqs(site_id: int, file_url: str, markdown_content: str, faqs: List[Dict[str, str]]):
    await models.StoreDocumentFAQ.create(
        site_id=site_id,
        file_url=file_url,
        markdown_content=markdown_content,
        faqs=faqs
    )


async def get_collection_knowledge_point(source: str, source_detail: str) -> models.CollectionKnowledgePoint | None:
    return (
        await models.CollectionKnowledgePoint.filter(source=source, source_detail=source_detail)
        .order_by("-id")
        .first()
    )


async def save_collection_knowledge_points(domain: str, knowledge_points: list['schema.CollectionKnowledgePoint']):
    """
    批量保存collection知识点
    """
    site = await get_site(domain)
    if not site:
        site = await init_site_metadata(domain)
    for kp in knowledge_points:
        await models.CollectionKnowledgePoint.update_or_create(
            point_id=kp.point_id,
            defaults={
                "topic": kp.topic,
                "title": kp.title,
                "content": kp.content,
                "collection_id": kp.collection_id,
                "source": kp.source,
                "source_detail": kp.source_detail,
                "label": kp.label,
                "site": site,
            }
        )
    logger.info(f"Saved {len(knowledge_points)} collection knowledge points for {domain}")


async def list_collection_knowledge(domain: str) -> list[models.CollectionKnowledgePoint]:
    site = await get_site(domain)
    if not site:
        return []
    knowledge_all = await models.CollectionKnowledgePoint.filter(site=site).all()
    return [knowledge for knowledge in knowledge_all]


async def create_parse_file_task(req: schema.ParseFileTaskRequest, site: models.ShopifySite):
    await models.ParseFileTask.create(
        task_id=req.task_id,
        file_key=req.file_key,
        site=site,
        product_ids=req.product_ids,
        knowledge_type=req.knowledge_type,
        status=models.FileProcessStatus.PENDING,
    )


async def get_parse_file_task(task_id: str) -> schema.ParseFileTaskRequest | None:
    task = await models.ParseFileTask.get_or_none(task_id=task_id)
    if not task:
        return None
    return schema.ParseFileTaskRequest(
        task_id=task.task_id,
        store_domain=(await task.site).shopify_domain,
        file_key=task.file_key,
        product_ids=task.product_ids,
        knowledge_type=task.knowledge_type,
    )


async def get_extract_file_knowledge_task(task_id: str) -> schema.ExtractFileKnowledgeTaskRequest | None:
    task = await models.ParseFileTask.get_or_none(task_id=task_id)
    if not task:
        return None
    return schema.ExtractFileKnowledgeTaskRequest(
        task_id=task.task_id,
        store_domain=(await task.site).shopify_domain,
        file_key=task.file_key,
        product_ids=task.product_ids,
        knowledge_type=task.knowledge_type,
        markdown=task.markdown or '',
    )


async def list_parse_file_tasks_for_markdown_extracting(domain: str) -> list[schema.ParseFileTaskRequest]:
    site = await get_site(domain)
    if site is None:
        return []
    need_markdown_extract_statues = [
        models.FileProcessStatus.PENDING,
        models.FileProcessStatus.MARKDOWN_EXTRACT_FAILED
    ]
    tasks = await models.ParseFileTask.filter(site=site, status__in=need_markdown_extract_statues).all()
    return [schema.ParseFileTaskRequest(
        task_id=task.task_id,
        store_domain=(await task.site).shopify_domain,
        file_key=task.file_key,
        product_ids=task.product_ids,
        knowledge_type=task.knowledge_type,
    ) for task in tasks]


async def list_tasks_for_knowledge_extracting(
        domain: str, knowledge_type: str) -> list[schema.ExtractFileKnowledgeTaskRequest]:
    site = await get_site(domain)
    if site is None:
        return []
    tasks = await models.ParseFileTask.filter(site=site, status=models.FileProcessStatus.MARKDOWN_EXTRACTED,
                                              knowledge_type=knowledge_type).all()
    return [schema.ExtractFileKnowledgeTaskRequest(
        task_id=task.task_id,
        store_domain=(await task.site).shopify_domain,
        file_key=task.file_key,
        product_ids=task.product_ids,
        knowledge_type=task.knowledge_type,
        markdown=task.markdown,
    ) for task in tasks]


async def update_parse_file_task_status(task_id: str, status: models.FileProcessStatus, markdown: str | None):
    task = await models.ParseFileTask.get_or_none(task_id=task_id)
    if task is None:
        logger.warning(f"Parse file task {task_id} not found.")
        return
    if status == models.FileProcessStatus.MARKDOWN_EXTRACTED:
        if not markdown:
            raise ValueError("Markdown content must be provided when status is MARKDOWN_EXTRACTED.")
        task.status = status
        task.markdown = markdown
    elif status == models.FileProcessStatus.MARKDOWN_EXTRACT_FAILED:
        if markdown:
            raise ValueError("Markdown content must be None when status is MARKDOWN_EXTRACT_FAILED.")
        task.status = status
        task.markdown = ''
    else:
        task.status = status
    await task.save()


async def get_page_metadata(url: str) -> models.PageMetadata | None:
    return await models.PageMetadata.get_or_none(url=url)


async def get_blog_metadata(url: str) -> models.BlogMetadata | None:
    return await models.BlogMetadata.get_or_none(url=url)


async def list_shopify_domain_pages(domain: str) -> list[schema.ShopifyPage]:
    pages = await models.ShopifyPage.filter(site__domain=domain).all()
    return [to_schema_shopify_page(page) for page in pages]


async def exists_page(page_url: str) -> bool:
    return await models.ShopifyPage.filter(page_url=page_url).exists()


def to_schema_shopify_page(page: models.ShopifyPage) -> schema.ShopifyPage:
    return schema.ShopifyPage(
        id=page.id,
        created_at=page.created_at,
        crawl_status=page.crawl_status,
        page_url=page.page_url,
        data=page.data
    )


async def get_page(page_url: str) -> schema.ShopifyPage:
    page = await models.ShopifyPage.get(page_url=page_url)
    return to_schema_shopify_page(page)


async def create_page(domain: str, page_url: str) -> schema.ShopifyPage:
    site = await get_site(domain)
    page = await models.ShopifyPage.create(site=site, page_url=page_url)
    return to_schema_shopify_page(page)


async def update_page_crawl_status(page_url: str, crawl_status: models.CrawlStatus):
    page = await models.ShopifyPage.get(page_url=page_url)
    page.crawl_status = crawl_status
    await page.save(update_fields=["crawl_status"])


async def update_page_data(page_url: str, product_id: str | None = None):
    page = await models.ShopifyPage.get(page_url=page_url)
    data_modified_flag = False
    if product_id is not None:
        page.data["product_id"] = product_id
        data_modified_flag = True
    if data_modified_flag:
        await page.save(update_fields=["data"])


async def exists_page_content_summary(domain: str, content: str):
    return await models.PageContentSummary.filter(site__domain=domain, content=content.strip()).exists()


async def get_page_content_summary(domain: str, content: str) -> str:
    return (
        await models.PageContentSummary.filter(site__domain=domain, content=content.strip()).first()
        .values_list("summary", flat=True)
    )


async def create_page_content_summary(domain: str, content: str, summary: str):
    site = await models.ShopifySite.get(domain=domain)
    await models.PageContentSummary.create(
        site=site,
        content=content.strip(),
        summary=summary.strip(),
    )


def to_schema_page_element(element: models.PageElement) -> schema.PageElement:
    schema_element = schema.PageElement(
        id=element.id,
        created_at=element.created_at,
        updated_at=element.updated_at,
        status=element.status,
        target=element.target,
        selectors=element.selectors,
        data=schema.PageElementData.model_validate(element.data),
    )
    return schema_element


async def clear_page_elements(page_url: str):
    page = await models.ShopifyPage.get(page_url=page_url)
    await models.PageElement.filter(page=page).delete()


async def list_page_elements(page_url: str) -> list[schema.PageElement]:
    page_elements = await models.PageElement.filter(page__page_url=page_url).all()
    return [to_schema_page_element(element) for element in page_elements]

async def list_raw_page_elements(page_url: str) -> list[models.PageElement]:
    page_elements = await models.PageElement.filter(page__page_url=page_url).all()
    return page_elements


async def exists_page_element(page_url: str, target: str) -> bool:
    return await models.PageElement.filter(page__page_url=page_url, target=target).exists()


async def exists_page_element_by_id(element_id: int) -> bool:
    return await models.PageElement.filter(id=element_id).exists()


async def exists_page_element_by_page_url(page_url: str) -> bool:
    return await models.PageElement.filter(page__page_url=page_url).exists()


async def get_page_element(element_id: int) -> schema.PageElement:
    page_element = await models.PageElement.get(id=element_id)
    return to_schema_page_element(page_element)


async def get_page_element_by_target(page_url: str, target: str) -> schema.PageElement:
    page_element = await models.PageElement.get(page__page_url=page_url, target=target)
    return to_schema_page_element(page_element)


async def create_page_element(page_url: str, editable: schema.PageElementEditable) -> schema.PageElement:
    page = await models.ShopifyPage.get(page_url=page_url)
    element = await models.PageElement.create(
        page=page,
        status=models.PageElementStatus.EDITED,
        target=editable.target,
        selectors=editable.selectors,
        data=editable.data.model_dump(),
    )
    return to_schema_page_element(element)


async def update_page_element(element_id: int, editable: schema.PageElementEditable):
    element = await models.PageElement.get(id=element_id)
    element.status = models.PageElementStatus.EDITED
    element.target = editable.target
    element.selectors = editable.selectors
    element.data = editable.data.model_dump()
    await element.save(update_fields=["status", "target", "selectors", "data"])


async def create_or_update_page_element(page_url, editable: schema.PageElementEditable) -> schema.PageElement:
    """创建或更新页面元素，如果已存在则合并 xpath 列表，否则创建新的元素。"""
    if await exists_page_element(page_url, editable.target):
        element = await get_page_element_by_target(page_url, editable.target)
        xpath_list = set(element.selectors) | set(editable.selectors)
        editable.selectors = list(xpath_list)
        await update_page_element(element.id, editable)
    else:
        element = await create_page_element(page_url, editable)
    return element


async def delete_page_element(element_id: int):
    await models.PageElement.filter(id=element_id).delete()


async def create_workflow(name: str, chain: celery.chain) -> int:
    """为一个 celery chain 创建 workflow 数据库记录，以便跟踪这个 chain 的执行情况."""
    workflow = models.CeleryWorkflow(
        name=name,
        signature=dict(chain),
    )
    await workflow.save()
    logger.bind(workflow_id=str(workflow.id)).info(f"Created workflow {workflow.id} with name {name}.")
    return workflow.id


async def list_workflows(page: int = 1, size: int = 20, search: str | None = None) -> PaginatedResponse[schema.CeleryWorkflowDetail]:
    """获取工作流列表，支持分页和搜索."""
    queryset = models.CeleryWorkflow.all()
    
    # 搜索过滤
    if search:
        search_lower = search.lower()
        queryset = queryset.filter(
            Q(name__icontains=search_lower) | 
            Q(id__icontains=search_lower)
        )
    
    total = await queryset.count()
    offset = (page - 1) * size
    workflows = await queryset.order_by('-id').offset(offset).limit(size)
    result = []
    for workflow_orm in workflows:
        workflow_detail = await convert_workflow_orm_to_pydantic(workflow_orm)
        result.append(workflow_detail)
    total_pages = (total + size - 1) // size if size else 1
    return PaginatedResponse[schema.CeleryWorkflowDetail](
        items=result,
        page=page,
        size=size,
        total=total,
        total_pages=total_pages
    )


async def get_workflow(workflow_id: int) -> schema.CeleryWorkflowDetail | None:
    workflow_orm = await models.CeleryWorkflow.get_or_none(id=workflow_id)
    if not workflow_orm:
        logger.warning(f"Workflow with ID {workflow_id} not found.")
        return None
    workflow_detail = await convert_workflow_orm_to_pydantic(workflow_orm)
    return workflow_detail


async def convert_workflow_orm_to_pydantic(workflow_orm: models.CeleryWorkflow) -> schema.CeleryWorkflowDetail:
    """将 ORM 对象转换为 Pydantic 模型."""
    workflow_signature = schema.CeleryWorkflowSignature(tasks=[],
                                                        options=workflow_orm.signature.get('options', {}))
    for task in workflow_orm.signature.get('kwargs', {}).get('tasks', []):
        workflow_signature.tasks.append(
            schema.CeleryTaskSignature(
                name=task['task'],
                kwargs=task.get('kwargs', {}),
            )
        )
    workflow_detail = schema.CeleryWorkflowDetail(
        id=workflow_orm.id,
        name=workflow_orm.name,
        signature=workflow_signature,
        tasks=[],
        status=workflow_orm.status,
        created_at=workflow_orm.created_at,
        updated_at=workflow_orm.updated_at,
        finished_at=workflow_orm.finished_at,
        log_url=generate_log_url(workflow_id=workflow_orm.id),
    )
    tasks = await models.CeleryTask.filter(workflow_id=workflow_orm.id).order_by('id').all()
    task_details = [celery_task_to_pydantic(task) for task in tasks]
    await fulfill_celery_task_progress(task_details)
    workflow_detail.tasks = task_details
    return workflow_detail


async def update_workflow_status(workflow_id: int, status: models.CeleryWorkflowStatus):
    """更新 workflow 的状态."""
    with logger.contextualize(workflow_id=str(workflow_id)):
        workflow = await models.CeleryWorkflow.get_or_none(id=workflow_id)
        if not workflow:
            logger.warning(f"Workflow with ID {workflow_id} not found.")
            return
        workflow.status = status
        if status in (models.CeleryWorkflowStatus.SUCCESS, models.CeleryWorkflowStatus.FAILED):
            workflow.finished_at = datetime.now()
        await workflow.save()
        logger.info(f"Updated workflow {workflow_id} status to {status}.")


async def update_workflow_status_via_task(task: models.CeleryTask):
    workflow_id = task.workflow_id
    if not workflow_id:
        return
    with logger.contextualize(workflow_id=str(workflow_id), task_name=task.name, task_id=task.task_id):
        workflow = await models.CeleryWorkflow.get_or_none(id=workflow_id)
        if not workflow:
            logger.warning(f"Workflow with ID {workflow_id} not found.")
            return
        task_name = task.name
        chain = celery.chain.from_dict(workflow.signature)
        task_index, task_sig = None, None
        for i, t in enumerate(chain.tasks):
            if t.name == task_name:
                task_index, task_sig = i, t
                break
        if task_sig is None:
            logger.warning(f"Task {task_name} not found in signature of workflow {workflow_id}.")
            return
        is_last_task = task_index == len(chain.tasks) - 1
        if task.status == models.CeleryTaskStatus.SUCCESS:
            if is_last_task:
                workflow.status = models.CeleryWorkflowStatus.SUCCESS
                workflow.finished_at = datetime.now()
            else:
                workflow.status = models.CeleryWorkflowStatus.RUNNING
        elif task.status == models.CeleryTaskStatus.FAILED:
            workflow.status = models.CeleryWorkflowStatus.FAILED
            workflow.finished_at = datetime.now()
        elif task.status == models.CeleryTaskStatus.CANCELED:
            workflow.status = models.CeleryWorkflowStatus.CANCELED
            workflow.finished_at = datetime.now()
        elif task.status == models.CeleryTaskStatus.STARTED:
            workflow.status = models.CeleryWorkflowStatus.RUNNING
        else:
            logger.warning(f"Unhandled task status {task.status} for task {task_name} in workflow {workflow_id}.")
            return
        await workflow.save()
        logger.info(f"Updated workflow {workflow_id} status to {workflow.status}.")


async def delete_store_knowledge_points_by_source(domain: str, source: str, source_detail: str):
    """
    删除指定站点的特定 source 和 source_detail 的店铺知识点
    """
    site = await get_site(domain)
    if not site:
        logger.warning(f"No site found for domain {domain}")
        return 0

    deleted_count = await models.StoreKnowledgePoint.filter(
        site=site,
        source=source,
        source_detail=source_detail
    ).delete()

    logger.info(
        f"Deleted {deleted_count} store knowledge points for {domain} "
        f"with source={source}, source_detail={source_detail}")
    return deleted_count


async def get_store_knowledge_point(source: str, source_detail: str) -> models.StoreKnowledgePoint | None:
    return (
        await models.StoreKnowledgePoint.filter(source=source, source_detail=source_detail)
        .order_by("-id")
        .first()
    )


async def create_or_update_user_from_hukou_id_token(token_payload: dict) -> schema.ShopPupilUser:
    """创建或更新用户."""
    user_info = schema.HukouUserInfo.model_validate(token_payload)
    obj, _ = await models.User.update_or_create(
        username=user_info.name,
        defaults={
            "email": user_info.email,
            "full_name": user_info.given_name,
            "phone_number": user_info.phone_number,
            "last_login": datetime.now(),
            "sso_metadata": token_payload,
        }
    )
    return _user_orm_to_user_model(obj)


def _user_orm_to_user_model(user_orm: models.User) -> schema.ShopPupilUser:
    """将 User ORM 对象转换为 ShopPupilUser Pydantic 模型."""
    return schema.ShopPupilUser(
        id=user_orm.id,
        username=user_orm.username,
        email=user_orm.email,
        full_name=user_orm.full_name,
        phone_number=user_orm.phone_number,
        is_active=user_orm.is_active,
        is_superuser=user_orm.is_superuser,
        last_login=user_orm.last_login,
        created_at=user_orm.created_at,
    )


async def get_user_by_id(user_id: int) -> schema.ShopPupilUser | None:
    """根据用户ID获取用户."""
    user = await models.User.get_or_none(id=user_id)
    if user is None:
        return None
    return _user_orm_to_user_model(user)


async def list_domain_faqs_paginated(
        domain: str,
        page: int, size: int,
        should_match_crawl_strategy: bool = True) -> schema.PaginatedResponse[schema.ProductMetaAndFaq]:
    """
    以分页的方式列出指定域名下所有产品的FAQ信息.
    按状态优先级排序：fresh -> editing -> ready -> disabled -> archived
    包含各状态的统计信息

    Args:
        domain: 站点域名
        page: 页码
        size: 每页大小
        should_match_crawl_strategy: 是否根据爬取策略筛选产品
    """
    site = await models.ShopifySite.filter(domain=domain).get_or_none()
    if not site:
        return schema.PaginatedResponse(
            items=[],
            page=page,
            size=size,
            total=0,
            total_pages=0,
        )

    # 根据策略筛选产品
    if should_match_crawl_strategy:
        filtered_product_ids = await _get_filtered_product_ids_by_strategy(domain, 'product_faq')
        if filtered_product_ids is not None:  # 策略不是 'all'
            if not filtered_product_ids:  # 没有符合策略的产品
                return schema.PaginatedResponse(
                    items=[],
                    page=page,
                    size=size,
                    total=0,
                    total_pages=0,
                )
            qs = models.ProductFaqs.filter(product__site=site, product__product_id__in=filtered_product_ids)
        else:  # 策略是 'all'，获取所有产品
            qs = models.ProductFaqs.filter(product__site=site)
    else:
        # 原来的逻辑，获取所有产品
        qs = models.ProductFaqs.filter(product__site=site)

    offset = (page - 1) * size
    faqs_records = await qs.prefetch_related('product').annotate(
        status_order=get_status_order_case()
    ).order_by('status_order').offset(offset).limit(size).all()

    result = []
    for record in faqs_records:
        result.append(_convert_product_faq_to_pydantic(record, record.product, shopify_domain=site.shopify_domain))

    total_count = await qs.count()
    total_pages = (total_count + size - 1) // size
    return schema.PaginatedResponse(
        items=result,
        page=page,
        size=size,
        total=total_count,
        total_pages=total_pages,
    )


async def get_selling_point_edit_stats(
        domain: str, should_match_crawl_strategy: bool = True) -> schema.EditStatusStats:
    """
    统计指定站点下所有产品卖点的各状态数量

    Args:
        domain: 站点域名
        should_match_crawl_strategy: 是否根据爬取策略筛选产品
    """
    # 根据策略筛选产品
    if should_match_crawl_strategy:
        filtered_product_ids = await _get_filtered_product_ids_by_strategy(domain, 'product_selling_point')
        if filtered_product_ids is not None:  # 策略不是 'all'
            if not filtered_product_ids:  # 没有符合策略的产品
                return schema.EditStatusStats.model_validate({})
            qs = models.ProductSellingPoint.filter(product__site__domain=domain,
                                                   product__product_id__in=filtered_product_ids)
        else:  # 策略是 'all'，获取所有产品
            qs = models.ProductSellingPoint.filter(product__site__domain=domain)
    else:
        # 原来的逻辑，获取所有产品
        qs = models.ProductSellingPoint.filter(product__site__domain=domain)

    status_counts_raw = await (qs.annotate(count=tortoise.functions.Count('id'))
                               .group_by('status').values('status', 'count'))
    status_counts = {item['status']: item['count'] for item in status_counts_raw}
    return schema.EditStatusStats.model_validate(status_counts)


async def get_concern_point_edit_stats(
        domain: str, should_match_crawl_strategy: bool = True) -> schema.EditStatusStats:
    """
    统计指定站点下所有产品顾虑点的各状态数量

    Args:
        domain: 站点域名
        should_match_crawl_strategy: 是否根据爬取策略筛选产品
    """
    # 根据策略筛选产品
    if should_match_crawl_strategy:
        filtered_product_ids = await _get_filtered_product_ids_by_strategy(domain, 'product_concern_point')
        if filtered_product_ids is not None:  # 策略不是 'all'
            if not filtered_product_ids:  # 没有符合策略的产品
                return schema.EditStatusStats.model_validate({})
            qs = (models.ProductConcernPoint
                  .filter(product__site__domain=domain, product__product_id__in=filtered_product_ids))
        else:  # 策略是 'all'，获取所有产品
            qs = models.ProductConcernPoint.filter(product__site__domain=domain)
    else:
        # 原来的逻辑，获取所有产品
        qs = models.ProductConcernPoint.filter(product__site__domain=domain)

    status_counts_raw = await (qs.annotate(count=tortoise.functions.Count('id'))
                               .group_by('status').values('status', 'count'))
    status_counts = {item['status']: item['count'] for item in status_counts_raw}
    return schema.EditStatusStats.model_validate(status_counts)


async def get_faqs_edit_stats(
        domain: str, should_match_crawl_strategy: bool = True) -> schema.EditStatusStats:
    """
    统计指定站点下所有产品FAQ的各状态数量

    Args:
        domain: 站点域名
        should_match_crawl_strategy: 是否根据爬取策略筛选产品
    """
    # 根据策略筛选产品
    if should_match_crawl_strategy:
        filtered_product_ids = await _get_filtered_product_ids_by_strategy(domain, 'product_faq')
        if filtered_product_ids is not None:  # 策略不是 'all'
            if not filtered_product_ids:  # 没有符合策略的产品
                return schema.EditStatusStats.model_validate({})
            qs = models.ProductFaqs.filter(product__site__domain=domain, product__product_id__in=filtered_product_ids)
        else:  # 策略是 'all'，获取所有产品
            qs = models.ProductFaqs.filter(product__site__domain=domain)
    else:
        # 原来的逻辑，获取所有产品
        qs = models.ProductFaqs.filter(product__site__domain=domain)

    status_counts_raw = await (qs.annotate(count=tortoise.functions.Count('id'))
                               .group_by('status').values('status', 'count'))
    status_counts = {item['status']: item['count'] for item in status_counts_raw}
    return schema.EditStatusStats.model_validate(status_counts)


async def get_site_crawl_strategy(domain: str) -> schema.SiteCrawlStrategy | None:
    """
    获取站点的挖掘策略

    Args:
        domain: 站点域名

    Returns:
        站点的挖掘策略，如果未设置则返回 None
    """
    site = await get_site(domain)
    if not site or not site.crawl_strategy:
        return None

    return schema.SiteCrawlStrategy(**site.crawl_strategy)


async def set_site_crawl_strategy(domain: str, strategy: schema.SiteCrawlStrategy, comment: str | None = None) -> None:
    """
    设置站点的挖掘策略

    Args:
        domain: 站点域名
        strategy: 挖掘策略
        comment: 策略变更原因
    """
    site = await get_site(domain)
    if not site:
        raise ValueError(f"Site {domain} not found")

    # 创建策略版本记录
    strategy_dict = strategy.model_dump()
    await create_data_version(
        data_type="site_crawl_strategy",
        data_id=site.id,
        value=strategy_dict,
        comment=comment
    )

    # 更新站点当前策略
    site.crawl_strategy = strategy_dict
    site.strategy_updated_at = datetime.now()
    await site.save()


async def delete_site_crawl_strategy(domain: str) -> None:
    """
    删除站点的挖掘策略，恢复为默认策略

    Args:
        domain: 站点域名
    """
    site = await get_site(domain)
    if not site:
        raise ValueError(f"Site {domain} not found")

    # 创建删除记录
    await create_data_version(
        data_type="site_crawl_strategy",
        data_id=site.id,
        value={"deleted": True},
        comment="策略已删除，恢复默认策略"
    )

    # 清空策略
    site.crawl_strategy = None
    site.strategy_updated_at = datetime.now()
    await site.save()


async def get_site_crawl_strategy_history(domain: str) -> list[schema.DataVersion]:
    """
    获取站点挖掘策略的历史版本

    Args:
        domain: 站点域名

    Returns:
        策略历史版本列表
    """
    site = await get_site(domain)
    if not site:
        return []

    return await get_data_version_history("site_crawl_strategy", site.id)


async def get_effective_crawl_strategy(domain: str) -> schema.SiteCrawlStrategy:
    """
    获取站点的有效挖掘策略（如果未设置则返回默认策略）

    Args:
        domain: 站点域名

    Returns:
        有效的挖掘策略
    """
    strategy = await get_site_crawl_strategy(domain)
    if strategy:
        return strategy

    # 返回默认策略
    return schema.SiteCrawlStrategy.get_default_strategy()


async def get_products_by_strategy(
        domain: str,
        strategy_field: Literal['product_selling_point', 'product_concern_point', 'product_faq']) -> list[str]:
    """
    根据策略获取需要处理的产品URL列表

    Args:
        domain: 站点域名
        strategy_field: 策略字段名称

    Returns:
        需要处理的产品URL列表
    """
    # 获取站点的有效策略
    strategy = await get_effective_crawl_strategy(domain)

    # 获取策略中对应字段的配置
    selector = getattr(strategy, strategy_field)

    if selector.target == 'all':
        # 挖掘所有产品
        products = await list_product_metadata(domain)
        return [product.url for product in products]
    elif selector.target == 'specific':
        # 只挖掘指定产品
        if not selector.product_ids:
            logger.warning(f"No product IDs specified in strategy for {domain}")
            return []

        # 获取所有产品，然后在内存中过滤，避免 N+1 查询
        all_products = await list_product_metadata(domain)
        product_id_to_url = {product.product_id: product.url for product in all_products}

        product_urls = []
        for product_id in selector.product_ids:
            if product_id in product_id_to_url:
                product_urls.append(product_id_to_url[product_id])
            else:
                logger.warning(f"Product {product_id} not found in site {domain}")

        return product_urls
    else:
        logger.error(f"Invalid target value {selector.target} in strategy for {domain}")
        return []


async def _get_filtered_product_ids_by_strategy(domain: str, strategy_field: str) -> list[str] | None:
    """
    根据策略获取需要处理的产品ID列表

    Args:
        domain: 站点域名
        strategy_field: 策略字段名称

    Returns:
        需要处理的产品ID列表，如果策略是 'all' 则返回 None（表示不需要过滤）
    """
    # 获取站点的有效策略
    strategy = await get_effective_crawl_strategy(domain)

    # 获取策略中对应字段的配置
    selector = getattr(strategy, strategy_field)

    if selector.target == 'all':
        # 挖掘所有产品，不需要过滤
        return None
    elif selector.target == 'specific':
        # 只挖掘指定产品
        return selector.product_ids
    else:
        logger.error(f"Invalid target value {selector.target} in strategy for {domain}")
        return []


async def get_task_run_stats() -> list[schema.TaskRunStats]:
    """
    获取所有任务的执行统计信息
    
    Returns:
        包含每个任务统计信息的列表，每个任务包含：
        - name: 任务名称
        - total_runs: 总执行次数
        - success_count: 成功次数
        - failed_count: 失败次数
        - running_count: 正在运行次数
        - pending_count: 等待中次数
        - last_run_time: 最近一次运行时间
        - last_run_status: 最近一次运行状态
    """
    # 使用原生SQL查询以获得更好的性能
    query = """
    SELECT 
        name,
        COUNT(*) as total_runs,
        COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as success_count,
        COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed_count,
        COUNT(CASE WHEN status = 'STARTED' THEN 1 END) as running_count,
        COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_count,
        MAX(created_at) as last_run_time,
        (SELECT status FROM celery_task ct2 
         WHERE ct2.name = ct1.name 
         ORDER BY ct2.created_at DESC 
         LIMIT 1) as last_run_status
    FROM celery_task ct1
    GROUP BY name
    ORDER BY total_runs DESC, name
    """

    conn = connections.get('default')
    result = await conn.execute_query_dict(query)
    
    stats = []
    for row in result:
        stats.append(schema.TaskRunStats(
            name=row['name'],
            total_runs=row['total_runs'],
            success_count=row['success_count'] or 0,
            failed_count=row['failed_count'] or 0,
            running_count=row['running_count'] or 0,
            pending_count=row['pending_count'] or 0,
            last_run_time=row['last_run_time'],
            last_run_status=row['last_run_status']
        ))
    
    return stats
