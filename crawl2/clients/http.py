import asyncio
from urllib.parse import urlparse

import httpx
from aiolimiter import AsyncLimiter
from leyan_tracing.httpx.httpx_tracing import AsyncClient

from crawl2.config import settings
from crawl2.utils import extract_domain_from_shopify_site_url, get_cookies

proxy = {}

if settings.CRAWL_PROXY:
    proxy['http'] = proxy['https'] = settings.CRAWL_PROXY


proxied_hostnames = {}


ua_header = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36'   # NOQA
}
httpx_client = AsyncClient(follow_redirects=True)
httpx_client.headers.update(ua_header)
if proxy:
    httpx_client_proxied = AsyncClient(proxy=proxy['http'], follow_redirects=True)
    httpx_client_proxied.headers.update(ua_header)
else:
    httpx_client_proxied = None

rate_limit = AsyncLimiter(5, 1)
concurrency_limit = asyncio.Semaphore(5)


def _convert_cookies_to_httpx_format(cookies_list):
    """Convert cookies from list of dicts to httpx-compatible format."""
    if not cookies_list:
        return {}
    return {cookie["name"]: cookie["value"] for cookie in cookies_list}


async def http_get(url: str) -> httpx.Response:
    async with rate_limit, concurrency_limit:
        domain = extract_domain_from_shopify_site_url(url)
        cookies_str, cookies = get_cookies(domain)
        httpx_cookies = _convert_cookies_to_httpx_format(cookies)
        if not httpx_client_proxied:
            return await httpx_client.get(url, cookies=httpx_cookies)
        else:
            hostname = urlparse(url).hostname
            if hostname in proxied_hostnames:
                return await httpx_client_proxied.get(url, cookies=httpx_cookies)
            try:
                return await httpx_client.get(url, cookies=httpx_cookies)
            except (httpx.NetworkError, httpx.TimeoutException):
                response = await httpx_client_proxied.get(url, cookies=httpx_cookies)
                proxied_hostnames[hostname] = True
                return response
