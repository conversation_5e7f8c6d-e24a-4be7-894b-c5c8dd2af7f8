"""
这个模板定义的 API 接口，供 shopify-knowledge 调用，用于同步更新B端修改的数据， 也提供一些内部调用大模型的接口。
"""

import re
from typing import Optional, List
from fastapi import APIRouter, HTTPException, Query
from crawl2.web.schema.store_knowledge_point import (
    KnowledgePointResponse,
    StoreKnowledgePoint,
)
from crawl2.web.schema.aigc import (
    AigcGenRequest,
    MarketingCopyGenResponse,
    FAQGenResponse,
    QuestionGenResponse,
)

from crawl2.web.service.aigc_service import aigc_service
from crawl2.clients.qdrant_importer import qdrant_importer_instance

from loguru import logger

router = APIRouter(prefix="/inner", tags=["inner"])


@router.post("/store_knowledge_point/upsert", response_model=KnowledgePointResponse)
async def create_or_update_store_knowledge_point(
    request: StoreKnowledgePoint,
) -> KnowledgePointResponse:
    logger.info(f"Upserting knowledge point {request.point_id} in Qdrant.")
    uuid_pattern = r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
    if not re.match(uuid_pattern, request.point_id.lower()):
        raise HTTPException(
            status_code=400,
            detail=f"point_id 必须是有效的 UUID 格式（如：550e8400-e29b-41d4-a716-************），当前值：{request.point_id}",  # NOQA
        )
    response = await qdrant_importer_instance.create_or_update_point(request)
    logger.info(
        f"Successfully upserted knowledge point {request.point_id} in Qdrant."
    )
    return response


@router.post("/aigc/gen_marketing_copy", response_model=MarketingCopyGenResponse)
async def gen_marketing_copy(data: AigcGenRequest) -> MarketingCopyGenResponse:
    return await aigc_service.gen_marketing_copy(data)


@router.post("/aigc/gen_faq", response_model=FAQGenResponse)
async def gen_faq(data: AigcGenRequest) -> FAQGenResponse:
    return await aigc_service.gen_faq(data)


@router.post("/aigc/gen_question", response_model=QuestionGenResponse)
async def gen_question(data: AigcGenRequest) -> QuestionGenResponse:
    return await aigc_service.gen_question(data)


# Store Knowledge Point CRUD API 接口
@router.get("/store_knowledge_point/{point_id}", response_model=KnowledgePointResponse)
async def get_store_knowledge_point(point_id: str) -> KnowledgePointResponse:
    """获取单个知识点"""
    return await qdrant_importer_instance.get_store_knowledge_point(point_id)


@router.put("/store_knowledge_point/{point_id}", response_model=KnowledgePointResponse)
async def update_store_knowledge_point(
    point_id: str, request: StoreKnowledgePoint
) -> KnowledgePointResponse:
    """更新知识点"""
    return await qdrant_importer_instance.update_store_knowledge_point(point_id, request)


@router.delete("/store_knowledge_point/{point_id}")
async def delete_store_knowledge_point(point_id: str) -> dict:
    """删除知识点"""
    success = await qdrant_importer_instance.delete_store_knowledge_point(point_id)
    return {"success": success, "point_id": point_id}


@router.get("/store_knowledge_point", response_model=List[KnowledgePointResponse])
async def list_store_knowledge_points(
    store_domain: Optional[str] = Query(None, description="店铺域名"),
    source: Optional[str] = Query(None, description="来源"),
    source_detail: Optional[str] = Query(None, description="来源详情"),
    limit: int = Query(10, description="限制数量"),
    offset: int = Query(0, description="偏移量")
) -> List[KnowledgePointResponse]:
    """列出知识点"""
    return await qdrant_importer_instance.list_store_knowledge_points(
        store_domain=store_domain,
        source=source,
        source_detail=source_detail,
        limit=limit,
        offset=offset
    )


@router.get("/store_knowledge_point/search", response_model=List[KnowledgePointResponse])
async def search_store_knowledge_points(
    query: str = Query(..., description="搜索查询"),
    store_domain: Optional[str] = Query(None, description="店铺域名"),
    limit: int = Query(10, description="限制数量")
) -> List[KnowledgePointResponse]:
    """搜索知识点"""
    return await qdrant_importer_instance.search_store_knowledge_points(
        query=query,
        store_domain=store_domain,
        limit=limit
    )
