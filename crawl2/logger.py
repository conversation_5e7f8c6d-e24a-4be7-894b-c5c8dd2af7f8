import logging
import os
import sys
import socket
from functools import lru_cache
from logging.config import dictConfig

from leyan_logging import context   # NOQA
from leyan_logging.http import HttpLoggingHandler
from leyan_logging.http import _container_meta   # NOQA


is_in_lain_cluster = all(var in os.environ for var in ('APOLLO_APP_ID', 'APOLLO_META', 'LAIN_APPNAME', 'env'))

if not is_in_lain_cluster:
    HttpLoggingHandler.endpoint = 'https://stq-api.leyanbot.com/gohangout/log-proxy'   # NOQA
    _container_meta['containerId'] = socket.gethostname()
    _container_meta['appName'] = os.environ.get('LAIN_APPNAME', 'shop-pupil')
    _container_meta['procName'] = os.environ.get('LAIN_PROCNAME', 'shop-pupil')


LOG = logging.getLogger(__name__)


# 使用 lru_cache 是为了避免重复初始化
@lru_cache()
def setup_std_logging():
    configs = {
        'version': 1,
        'incremental': False,
        'disable_existing_loggers': False,
        'formatters': {
            'default': {
                'format': '[%(asctime)s] [%(levelname)s] %(name)s: %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S',
            },
        },
        "handlers": {
            "http": {
                "class": 'leyan_logging.http.HttpLoggingHandler',
                "level": "DEBUG"
            },
            "stdout": {
                "class": "logging.StreamHandler",
                "level": "INFO",
                "formatter": "default",
                "stream": "ext://sys.stdout",
            },
        },
        "loggers": {
            "": {
                "handlers": ["http", 'stdout'],
                "level": "INFO",
            },
            "lepollo.remote": {
                "level": "WARNING",
            }
        },
    }
    dictConfig(configs)
    LOG.info('logging 初始化完成')


@lru_cache()
def setup_loguru():
    from loguru import logger

    format_ = ("<level>{level}</level> | "
               "<cyan>{module}:{function}:{line}</cyan> | "
               "<level>{extra}</level> | "
               "<level>{message}</level>")

    logger.configure(handlers=[
        dict(sink=sys.stderr, format=format_, level='INFO'),
        dict(sink=(HttpLoggingHandler()), format="{message}", level='DEBUG', filter={'': 'INFO'})
    ])
    logger.info('loguru 初始化完成')


def setup_fastapi_logging_and_tracing(app):
    from starlette.routing import Match

    @app.middleware('http')
    async def clear_logging_context(request, call_next):
        endpoint = None
        for route in request.app.routes:
            match, _ = route.matches(request.scope)
            if match == Match.FULL:
                endpoint = f'{request.method} {route.path}'
                context.Endpoint.set(endpoint)
                break
        try:
            response = await call_next(request)
            if endpoint and endpoint.strip():
                # kong 会把该字段输出到日志的 endpoint 字段中, 便于统计接口调用情况
                # ref: https://git.leyantech.com/infrastructure/kong/-/issues/30
                response.headers['api-endpoint'] = endpoint.strip()
            return response
        finally:
            context.clear()
    setup()


def setup():
    if is_in_lain_cluster:
        from leyan_logging import setup as setup_leyan_logging
        setup_leyan_logging()
        return
    setup_std_logging()
    setup_loguru()
