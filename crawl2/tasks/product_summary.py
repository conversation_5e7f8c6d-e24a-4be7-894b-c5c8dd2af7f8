from loguru import logger

from crawl2.clients import llm
from crawl2.db import operations, models
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync

PRODUCT_SUMMARY_PROMPT_TEMPLATE = """You are a helpful assistant. Your task is to create a detailed yet concise summary of a product based on its product page content.
The summary will be used by a recommendation system to understand the product.
All information in the summary MUST be extracted directly from the text provided. Do not add any information or make any interpretations that are not explicitly stated in the content.

Please format the output in Markdown and include the following sections:
- **Product Overview**: A short paragraph (2-3 sentences) that captures the essence of the product.
- **Key Features**: A bullet point list of the 5-7 most important features and benefits mentioned in the product page.
- **Key Specifications**: If available, a bullet point list of key technical specifications (e.g., color, size, dimensions, weight, battery life, material).

Product Page Content:
```
{product_page}
```

Generate a Markdown summary based *only* on the content provided above.
"""

llm_conf = llm.LLMConfig('usq', 'qwen2.5-32b-chat-fp8-offline', 0, 5000)


async def extract_product_summary(metadata: models.ProductMetadata) -> str | None:
    knowledge = await metadata.product_knowledge.order_by('-id').first()
    if knowledge is None:
        logger.warning(f"No knowledge found for {metadata.url}")
        return None
    prompt = PRODUCT_SUMMARY_PROMPT_TEMPLATE.format(product_page=knowledge.markdown_content)
    logger.info(f"Extracting product summary for {metadata.url} with prompt: {prompt}")
    result = await llm.call_llm(llm_conf, prompt, parse_json=False)
    result = result.replace("```markdown", "").replace("```", "").strip()
    logger.info(f"Extracted product summary for {metadata.url} with result: {result}")
    if result is None:
        return None
    return result


@celery_task
async def extract_product_summary_for_single_product(product_url: str, clear_existing: bool = False):
    """挖掘单个商品的摘要信息"""
    metadata = await operations.get_product_metadata(product_url)
    if not metadata:
        logger.error(f"No metadata found for product URL: {product_url}")
        return
    existed = await operations.get_product_summary(metadata)
    if existed and not clear_existing:
        logger.info(f"Product summary already exists for {metadata.url}, skipping extraction.")
        return
    elif existed and clear_existing:
        logger.info(f"Clearing existing product summary for {metadata.url}.")
        await operations.delete_product_summary(metadata)
    summary = await extract_product_summary(metadata)
    if summary:
        await operations.save_product_summary(metadata, summary)


@celery_task
async def extract_product_summary_for_all_products(domain: str, clear_existing: bool = False):
    """挖掘指定站点所有商品的摘要信息"""
    metadata_list = await operations.list_product_metadata(domain)
    async_tasks = [
        extract_product_summary_for_single_product(metadata.url, clear_existing)
        for metadata in metadata_list
    ]
    await CeleryTaskTqdmAsync.gather(*async_tasks, desc="Extracting product summary")
