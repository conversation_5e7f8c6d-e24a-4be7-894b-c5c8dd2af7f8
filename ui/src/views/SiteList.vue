<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { api } from '@/api/api.js'
import { AgGridVue } from 'ag-grid-vue3'
import Navigation from '../components/common/Navigation.vue'
import AddSiteDialog from '../components/site/AddSiteDialog.vue'
import { Plus } from 'lucide-vue-next'

const router = useRouter()
const sites = ref([])
const showAddDialog = ref(false)

const compareStats = (a, b) => {
  if (a.fresh !== b.fresh) {
    return a.fresh - b.fresh
  } else {
    return a.total - b.total
  }
}

// AG Grid column definitions
const columnDefs = [
  {
    field: 'domain',
    headerName: 'Domain',
    sortable: true,
    filter: true,
    flex: 1,
    cellRenderer: params => {
      const domain = params.data.domain || '';
      const shopifyDomain = params.data.shopify_domain || '';
      return `
        <div>
          <div style="font-weight: bold; font-size: 15px;">${domain}</div>
          <div style="color: #888; font-size: 12px;">${shopifyDomain}</div>
        </div>
      `;
    }
  },
  {
    field: 'product_count',
    headerName: '商品数',
    sortable: true,
    sort: 'desc',
    flex: 1
  },
  {
    field: 'selling_point_stats',
    headerName: '卖点',
    sortable: true,
    flex: 1,
    comparator: compareStats,
    cellRenderer: params => {
      const {fresh, total} = params.value
      const domain = params.data.domain
      return `<a href="#/sites/${domain}/selling_points" class="text-blue-600 underline">${fresh}/${total}</a>`;
    }
  },
  {
    field: 'concern_point_stats',
    headerName: '顾虑点',
    sortable: true,
    flex: 1,
    comparator: compareStats,
    cellRenderer: params => {
      const {fresh, total} = params.value
      const domain = params.data.domain
      return `<a href="#/sites/${domain}/concerns" class="text-blue-600 underline">${fresh}/${total}</a>`
    }
  },
  {
    field: 'faq_stats',
    headerName: 'faq',
    sortable: true,
    flex: 1,
    comparator: compareStats,
    cellRenderer: params => {
      const {fresh, total} = params.value
      const domain = params.data.domain;
      return `<a href="#/sites/${domain}/faqs" class="text-blue-600 underline">${fresh}/${total}</a>`;
    }
  },
]

// AG Grid default column settings
const defaultColDef = {
  resizable: true,
  minWidth: 100,
  sortable: true,
  filter: true
}

// AG Grid grid options
const gridOptions = {
  animateRows: true,
  domLayout: 'normal',
  rowHeight: 48,
  headerHeight: 48
}

const fetchSites = async () => {
  try {
    const response = await api.get('/api/v1/sites')
    sites.value = response.data
  } catch (error) {
    console.error('Error fetching sites:', error)
  }
}

const navigateToSite = (params) => {
  if (params.data) {
    router.push(`/sites/${params.data.domain}`)
  }
}

const handleSiteAdded = (newSite) => {
  sites.value.push(newSite)
}

const downloadSellingPoints = async (domain) => {
  try {
    const response = await api.get(`/api/v1/sites/${domain}/selling_points.excel`, {
      responseType: 'blob'
    })
    
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `${domain}_selling_points.xlsx`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Error downloading selling points:', error)
  }
}

// Make downloadSellingPoints available globally for the cell renderer
window.downloadSellingPoints = downloadSellingPoints

onMounted(() => {
  fetchSites()
})
</script>

<template>
  <div class="flex flex-col h-screen w-full">
    <Navigation 
      :breadcrumbs="[
        { text: 'Sites' }
      ]"
    >
      <template #right>
        <button class="flex items-center gap-2 p-2 px-4 bg-green-500 text-white border-none rounded cursor-pointer text-sm hover:bg-green-600" @click="showAddDialog = true">
          <Plus size="16" />
          Add New Site
        </button>
      </template>
    </Navigation>

    <div class="flex justify-between items-center p-4 px-8 w-full">
      <h1 class="m-0 text-2xl">Sites</h1>
    </div>

    <div class="flex-1 p-4 px-8 flex flex-col w-full">
      <div v-if="sites.length" class="flex-1 w-full min-w-[1280px]">
        <AgGridVue
          :rowData="sites"
          :columnDefs="columnDefs"
          :defaultColDef="defaultColDef"
          v-bind="gridOptions"
          @rowClicked="navigateToSite"
          class="w-full h-full"
        />
      </div>
      <div v-else class="flex justify-center items-center h-full text-gray-600">
        Loading sites...
      </div>
    </div>

    <AddSiteDialog
      v-model:show="showAddDialog"
      @site-added="handleSiteAdded"
    />
  </div>
</template> 