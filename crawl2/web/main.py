import os.path

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
from starlette.staticfiles import StaticFiles
from tortoise.contrib.fastapi import register_tortoise

from crawl2 import celery_app  # NOQA: import to ensure tasks are registered on celery app
from crawl2.config import settings
from crawl2.tasks.celery_wrap import create_fast_api_router
from crawl2.web.api import health, sites, knowledge_tasks, inner, selling_point, faq, products, concern, page_elements
from crawl2.web.api import llm, auth
from crawl2 import workflows
from crawl2.logger import setup_fastapi_logging_and_tracing


app = FastAPI(
    title="Shop Pupil Web API",
    description="Web API for Shop Pupil data mining platform",
    version="0.1.0",
    docs_url="/docs",
)
setup_fastapi_logging_and_tracing(app)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=['http://127.0.0.1:8000', 'http://localhost:8000'],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    allow_origin_regex=r"chrome-extension:\/\/\w*"
)

# 配置数据库
register_tortoise(
    app,
    db_url=settings.DATABASE_URL,
    modules={"models": ["crawl2.db.models"]},
    generate_schemas=False,
    add_exception_handlers=True,
)

# 包含路由
app.include_router(health.router, prefix="/api/v1")
app.include_router(auth.router, prefix="/api/v1")
app.include_router(sites.router, prefix="/api/v1")
app.include_router(products.router, prefix="/api/v1")
app.include_router(selling_point.router, prefix="/api/v1")
app.include_router(faq.router, prefix="/api/v1")
app.include_router(concern.router, prefix="/api/v1")
app.include_router(knowledge_tasks.router, prefix="/api/v1")
app.include_router(inner.router, prefix="/api/v1")
app.include_router(create_fast_api_router(), prefix="/api/v1")
app.include_router(page_elements.router, prefix="/api/v1")
app.include_router(workflows.create_fast_api_router(), prefix="/api/v1")
app.include_router(llm.router, prefix="/api/v1")


if os.path.exists('ui/dist'):
    logger.info("Serving static files from 'ui/dist'")
    app.mount('/', StaticFiles(directory='ui/dist', html=True), name='static')
