import json

from loguru import logger

from crawl2 import schema
from crawl2.clients import llm
from crawl2.clients import shopify_knowledge
from crawl2.clients.qdrant_importer import qdrant_importer_instance
from crawl2.db import operations, models
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync
from crawl2.utils import generate_point_id

PRODUCT_KNOWLEDGE_PROMPT_TEMPLATE = '''
Extract information blocks from the product detail page markdown content exactly as they appear on the page. Each block should contain a title and content that are directly copied from the source:

1. Extract the exact content from the page without any modifications, paraphrasing, or summaries
2. Filter out non-descriptive content such as navigation menus, button text, JavaScript code, etc.
3. Focus on extracting information about following topics(just for reference, you need to add more topics if the page has more information):
   - Variant Information
   - Brand Introduction
   - Product Title
   - Product Price
   - Product Description
   - Product Features
   - Feature Details
   - Technical Specifications
   - Usage Instructions
   - Product Benefits
   - Use Scenarios
   - Products Comparison Table
   - What is included
   - FAQs
   - Customer Reviews
   - Others
4. Be comprehensive - capture ALL information from the detail markdown, **DO NOT** miss any content
5. Maintain the original wording and formatting from the detail markdown
6. For FAQs and Reviews:
   - Each FAQ question and its answer should be a separate document
   - Each customer review should be a separate document
   - Keep the original question/answer or review text intact
7. For Products Comparison Table:
   - include all the rows and columns in the table
   - format the table as a markdown table
8. Return results in valid JSON format with exact titles and content as they appear on the page
9. Note: The content of the extracted block should not be blank

example output:
[
  {{
    "topic": "Product Features",
    "documents": [
      {{
        "title": "Feature name",
        "content": "feature content"
      }}
    ]
  }},
  {{
    "topic": "FAQs",
    "documents": [
      {{
        "title": "question",
        "content": "answer"
      }}
    ]
  }},
  {{
    "topic": "Customer Reviews",
    "documents": [
      {{
        "title": "review title",
        "content": "review content"
      }}
    ]
  }}
]

# Current Task
Process the following markdown document:
```markdown
{markdown_content}
```

## Output in Json format as previous example, keep the schema, do not include any content that cannot be parsed by Json.
'''


llm_conf = llm.LLMConfig('usq', 'qwen2.5-32b-chat-fp8-offline', 0, 5000)


async def extract_product_page_knowledge(markdown: str, extra_headers: dict | None = None) -> list[schema.ProductKnowledgeGroupByTopic] | None:
    """
    Extract product knowledge from the product page markdown content using a large language model.

    :param product_page_markdown: product page markdown data.
    :return: product knowledge documents.
    """
    prompt = PRODUCT_KNOWLEDGE_PROMPT_TEMPLATE.format(markdown_content=markdown)
    result = await llm.call_llm(llm_conf, prompt, extra_headers=extra_headers)
    if result is None:
        return None
    logger.info(f"crawled product knowledge:  {result}")
    valid_result = []
    for item in result:
        try:
            valid_result.append(schema.ProductKnowledgeGroupByTopic.model_validate(item, strict=False))
        except Exception as e:
            logger.error(f"Failed to validate product knowledge: {item}, error: {e}")
            continue
    return valid_result


async def _process_single_product(product_url: str, raw_page_markdown: str, source: str = "product_page"):
    knowledge_topics = await extract_product_page_knowledge(raw_page_markdown)
    if knowledge_topics is None:
        return
    await operations.save_product_knowledge(product_url, knowledge_topics, source)


@celery_task
async def extract_single_product_knowledge(product_url: str, clear_existing: bool = False):
    """
    挖掘单个商品的知识点
    """
    existed = await operations.get_product_knowledge(product_url)
    if existed and not clear_existing:
        if not existed.documents:
            logger.warning(f'Clearing invalid product knowledge for {product_url}: {existed}')
            await operations.delete_product_knowledge(product_url)
        else:
            logger.info(f"Product knowledge already exists for {product_url}, skipping extraction.")
            return
    elif existed and clear_existing:
        logger.info(f"Clearing existing product knowledge for {product_url}.")
        await operations.delete_product_knowledge(product_url)
    page = await operations.get_product_raw_page(product_url)
    if not page:
        logger.warning(f"No product page found for URL: {product_url}, skipping extraction.")
        return
    await _process_single_product(product_url, page.markdown_content)


@celery_task
async def extract_all_product_knowledge(domain: str):
    """
    为指定站点的所有产品页面提取产品知识点
    """
    pages = await operations.list_product_raw_pages(domain)
    tasks = []
    for page in pages:
        metadata = await page.metadata
        existed = await operations.get_product_knowledge(metadata.url)
        if existed:
            logger.info(f"Product knowledge already exists for {metadata.url}, skipping extraction.")
            continue
        tasks.append(_process_single_product(metadata.url, page.markdown_content))
    await CeleryTaskTqdmAsync.gather(*tasks, desc="Extracting product knowledge")


async def parse_product_knowledge_point(
        metadata: models.ProductMetadata,
        product_knowledge: models.ProductKnowledge
) -> list[shopify_knowledge.ProductKnowledgePointItem]:
    product_id = metadata.product_id
    documents = product_knowledge.documents
    product_knowledge_point_list = []
    for document in documents:
        point_id = generate_point_id(product_id, document)
        product_knowledge_point_list.append(shopify_knowledge.ProductKnowledgePointItem(
            productId=product_id,
            pointId=point_id,
            title=document.get("title", ""),
            content=document.get("content", ""),
            label=document.get("topic", ""),
            source=product_knowledge.source if product_knowledge.source else "product_page"
        ))
    return product_knowledge_point_list


async def parse_product_metadata(
        metadata: models.ProductMetadata,
        product_knowledge: models.ProductKnowledge
) -> shopify_knowledge.ProductMetaItem:
    product_id = metadata.product_id
    property_and_value = await metadata.product_property_and_value
    summary = await metadata.summary
    if len(property_and_value) == 0:
        property_value = {}
    else:
        property_value = property_and_value[0].property_values
    if len(summary) == 0:
        summary_value = ""
    else:
        summary_value = summary[0].summary
    return shopify_knowledge.ProductMetaItem(
        productUrl=metadata.url,
        productType=metadata.product_type if metadata.product_type else "",
        productId=product_id,
        productTitle=metadata.title if metadata.title else "",
        productTags=metadata.tags if metadata.tags else "",
        productPage=product_knowledge.markdown_content,
        variantInfo=json.dumps(metadata.variant_info, ensure_ascii=False) if metadata.variant_info else "",
        propertyValues=json.dumps(property_value, ensure_ascii=False) if property_value else "",
        productSummary=summary_value
    )


@celery_task
async def sync_all_product_knowledge_to_knowledge_base(
    domain: str,
    batch_size: int = 500,
    delete_previous_shop_domain_data: bool = True,
):
    """
    同步指定站点的所有产品知识到知识库
    """
    knowledge_client = shopify_knowledge.ShopifyKnowledgeClient.get_client()
    site = await operations.get_site(domain)
    if not site:
        logger.error(f"No site found for {domain}")
        return
    store_domain = site.shopify_domain
    if delete_previous_shop_domain_data:
        await knowledge_client.delete_all_product_metadata(store_domain)
        await knowledge_client.delete_all_product_knowledge_point(store_domain)
    product_knowledge_list = await operations.list_product_knowledge(domain)
    logger.info(
        f"Found {len(product_knowledge_list)} product knowledge for {domain}")
    product_knowledge_point_list = []
    product_metadata_list = []
    for product_knowledge in product_knowledge_list:
        metadata = await product_knowledge.metadata
        product_metadata_list.append(await parse_product_metadata(metadata, product_knowledge))
        product_knowledge_point_list.extend(await parse_product_knowledge_point(metadata, product_knowledge))

    # sync product metadata
    for i in range(0, len(product_metadata_list), batch_size):
        batch = product_metadata_list[i:i+batch_size]
        req = shopify_knowledge.ImportProductMetaReq(
            storeDomain=store_domain,
            items=batch
        )
        result = await knowledge_client.import_product_meta(req)
        logger.info(
            f"Synced product metadata to knowledge base batch {i} for {store_domain}, result: {result}")
    logger.info(f"Synced all product metadata to knowledge base for {store_domain}, total: {len(product_metadata_list)}")  # NOQA
    # sync product knowledge point
    for i in range(0, len(product_knowledge_point_list), batch_size):
        batch = product_knowledge_point_list[i:i+batch_size]
        req = shopify_knowledge.ImportProductKnowledgePointReq(
            storeDomain=store_domain,
            items=batch
        )
        result = await knowledge_client.import_product_knowledge_point(req)
        logger.info(
            f"Synced product knowledge to knowledge base batch {i} for {store_domain}, result: {result}")
    logger.info(f"Synced all product knowledge to knowledge base for {store_domain}, total: {len(product_knowledge_point_list)}")  # NOQA


@celery_task
async def sync_single_product_knowledge_to_knowledge_base(product_url: str):
    """
    同步单个产品知识到知识库
    """
    knowledge_client = shopify_knowledge.ShopifyKnowledgeClient.get_client()
    all_product_knowledge = await operations.get_product_knowledge_all_source(product_url)
    if not all_product_knowledge:
        logger.error(f"No product knowledge found for {product_url}")
        return
    metadata = await all_product_knowledge[0].metadata
    site = await metadata.site
    store_domain = site.shopify_domain
    await knowledge_client.delete_single_product_metadata(store_domain, metadata.product_id)
    await knowledge_client.delete_single_product_knowledge_point(store_domain, metadata.product_id)

    product_metadata = await parse_product_metadata(metadata, all_product_knowledge[0])
    product_knowledge_point_list = []
    for product_knowledge in all_product_knowledge:
        product_knowledge_point_list.extend(await parse_product_knowledge_point(metadata, product_knowledge))
    req = shopify_knowledge.ImportProductKnowledgePointReq(
        storeDomain=store_domain,
        items=product_knowledge_point_list
    )
    result = await knowledge_client.import_product_knowledge_point(req)
    logger.info(f"Synced product knowledge to knowledge base for {product_url}, result: {result}")
    req = shopify_knowledge.ImportProductMetaReq(
        storeDomain=store_domain,
        items=[product_metadata]
    )
    result = await knowledge_client.import_product_meta(req)
    logger.info(f"Synced product metadata to knowledge base for {product_url}, result: {result}")

    req = shopify_knowledge.ImportProductKnowledgePointReq(
        storeDomain=store_domain,
        items=product_knowledge_point_list
    )
    result = await knowledge_client.import_product_knowledge_point(req)
    logger.info(f"Synced product knowledge to knowledge base for {product_url}, result: {result}")


async def _process_single_product_all_pipeline(
        product_url: str,
        raw_page_markdown: str,
        source: str = "product_page"
):
    await _process_single_product(product_url, raw_page_markdown, source)
    await sync_single_product_knowledge_to_knowledge_base(product_url)
    await sync_single_product_knowledge_to_qdrant(product_url)


async def _process_single_knowledge_extracting_task(domain: str, task: schema.ExtractFileKnowledgeTaskRequest):
    if not task.knowledge_type == 'PRODUCT':
        logger.error(f"Task with ID {task.task_id} is not a product knowledge extraction task.")
        return
    if not task.markdown:
        logger.error(f"Task with ID {task.task_id} has no markdown content.")
        return
    last_error = None
    for product_id in getattr(task, 'product_ids', []) or []:
        product = await operations.get_product_metadata_by_product_id(domain, product_id)
        if not product:
            logger.error(f"Product with product_id {product_id} not found in domain {domain}."
                         f" Skipping extract knowledge from file {task.file_key}.")
            continue
        try:
            await _process_single_product_all_pipeline(product.url, task.markdown, "uploaded_file")
        except Exception as e:
            last_error = e
            logger.opt(exception=e).error(f"Failed to process product_id {product_id} "
                                          f"in task {task.task_id} for domain {domain}: {str(e)}")
    # 只要有一个失败就算失败
    if last_error:
        await operations.update_parse_file_task_status(
            task.task_id, models.FileProcessStatus.KNOWLEDGE_EXTRACT_FAILED, None
        )
    else:
        await operations.update_parse_file_task_status(task.task_id,
                                                       models.FileProcessStatus.KNOWLEDGE_EXTRACTED, None)


@celery_task
async def extract_product_knowledge_from_uploaded_files(domain: str):
    """
    处理指定站点的所有产品类文件知识抽取任务
    """
    tasks = await operations.list_tasks_for_knowledge_extracting(domain, 'PRODUCT')
    async_tasks = [_process_single_knowledge_extracting_task(domain, task) for task in tasks]
    await CeleryTaskTqdmAsync.gather(*async_tasks, desc="Extracting product knowledge from uploaded files")


@celery_task
async def extract_product_knowledge_from_uploaded_file(task_id: str):
    """
    处理单个产品类文件知识抽取任务
    :param task_id: 来自 shopify-knowledge 的 task_id
    """
    with logger.contextualize(knowledge_task_id=task_id):
        task = await operations.get_extract_file_knowledge_task(task_id)
        if not task:
            logger.error(f"Task with ID {task_id} not found.")
            return
        site = await operations.get_site_by_myshopify_main(task.store_domain)
        if not site:
            logger.error(f"No site found for {task.store_domain}")
            return
        await _process_single_knowledge_extracting_task(site.domain, task)


@celery_task
async def sync_all_product_knowledge_to_qdrant(domain: str):
    """
    将指定站点的所有产品知识同步到 Qdrant
    """
    await qdrant_importer_instance.import_all_product_knowledge_points(domain)


@celery_task
async def sync_single_product_knowledge_to_qdrant(product_url: str):
    """
    将单个产品知识同步到 Qdrant
    """
    await qdrant_importer_instance.import_single_product_knowledge_points(product_url)
