from typing import List, Dict, Optional, Any, <PERSON><PERSON>
from typing_extensions import override
from loguru import logger
from tqdm import tqdm
from qdrant_client.models import (
    Distance,
    VectorParams,
    SparseVectorParams
)
import uuid
from crawl2.db import operations
from crawl2.db import models
from pydantic import BaseModel
from crawl2.qdrant_index.base_qdrant_indexer import BaseQdrantIndexer, IndexConfig


class ProductKnowledgePoint(BaseModel):
    url: str
    product_id: str
    product_title: str
    product_type: str
    product_tags: str
    product_page: str
    variant_info: Optional[Dict[str, Any]] = None
    property_values: Optional[Dict[str, Any]] = None
    product_summary: Optional[str] = None
    store_domain: str = ""
    title: str = ""
    content: str = ""
    label: str = ""
    source: str = ""


async def convert_product_knowledge_to_product_knowledge_point(
    product_knowledge: models.ProductKnowledge,
) -> Tuple[str, List[ProductKnowledgePoint]]:
    documents = product_knowledge.documents
    product_knowledge_points = []
    metadata = await product_knowledge.metadata
    if not metadata:
        logger.warning(
            f"No metadata found for product_knowledge {product_knowledge.id}"
        )
        return "", []
    url = metadata.url
    site = await metadata.site
    store_domain = site.shopify_domain
    logger.info(
        f"store_domain: {store_domain}, url: {url}, documents size: {len(documents)}"
    )
    for document in documents:
        property_and_value = await metadata.product_property_and_value
        summary = await metadata.summary
        if len(property_and_value) == 0:
            property_value = {}
        else:
            property_value = property_and_value[0].property_values
        if len(summary) == 0:
            summary_value = ""
        else:
            summary_value = summary[0].summary
        variant_info = metadata.variant_info
        if not isinstance(variant_info, dict):
            logger.warning(f"variant_info is not a dict: {type(variant_info)} {variant_info}")
            variant_info = {}
        product_knowledge_points.append(
            ProductKnowledgePoint(
                url=url,
                product_id=metadata.product_id,
                product_title=metadata.title,
                product_type=metadata.product_type,
                product_tags=metadata.tags,
                product_page=product_knowledge.markdown_content,
                variant_info=variant_info,
                property_values=property_value,
                product_summary=summary_value,
                store_domain=store_domain,
                title=document.get("title", ""),
                content=document.get("content", ""),
                label=document.get("topic", ""),
                source=product_knowledge.source,
            )
        )
    return store_domain, product_knowledge_points


async def load_all_product_knowledge_points(
    domain: str,
) -> Tuple[str, List[ProductKnowledgePoint]]:
    """从数据库中加载指定domain的product_knowledge_points"""
    product_knowledge_points = await operations.list_product_knowledge(domain)
    if not product_knowledge_points:
        return None, []
    results = []
    for product_knowledge in product_knowledge_points:
        store_domain, points = (
            await convert_product_knowledge_to_product_knowledge_point(
                product_knowledge
            )
        )
        results.extend(points)
    return store_domain, results


async def load_single_product_knowledge_points(
    url: str,
) -> Tuple[str, List[ProductKnowledgePoint]]:
    all_product_knowledge = await operations.get_product_knowledge_all_source(url)
    if not all_product_knowledge:
        return None, []
    results = []
    for product_knowledge in all_product_knowledge:
        store_domain, points = await convert_product_knowledge_to_product_knowledge_point(product_knowledge)
        if not points:
            continue
        results.extend(points)
    return store_domain, results


class ProductKnowledgePointIndex(
    BaseQdrantIndexer[ProductKnowledgePoint]
):
    def __init__(self, config: IndexConfig):
        super().__init__(config)

    @override
    async def generate_vectors(self, docs: List[ProductKnowledgePoint]) -> List[Dict[str, Any]]:
        document_texts = [f"{doc.title} {doc.content}" for doc in docs]
        product_titles = [doc.product_title for doc in docs]
        product_title_embeddings = await self.to_dense_embedding(product_titles)
        product_title_sparse_embeddings = await self.to_sparse_embedding(product_titles)
        document_embeddings = await self.to_dense_embedding(document_texts)
        document_sparse_embeddings = await self.to_sparse_embedding(document_texts)
        return [
            {
                "document_dense": doc_dense,
                "document_sparse": doc_sparse,
                "product_title_dense": title_dense,
                "product_title_sparse": title_sparse,
            } for doc_dense, doc_sparse, title_dense, title_sparse in zip(
                document_embeddings,
                document_sparse_embeddings,
                product_title_embeddings,
                product_title_sparse_embeddings
            )
        ]

    @override
    def schema_to_payload(self, schema: ProductKnowledgePoint) -> Dict[str, Any]:
        meta_dict = schema.model_dump()
        if meta_dict.get("product_type"):
            meta_dict["product_type"] = meta_dict["product_type"].lower()
        return meta_dict

    @override
    def get_id(self, schema: ProductKnowledgePoint) -> str:
        return str(uuid.uuid4())

    @property
    @override
    def vectors_config(self) -> Dict[str, Any]:
        return {
            "document_dense": VectorParams(
                size=self.config.vector_size, distance=Distance.COSINE
            ),
            "product_title_dense": VectorParams(
                size=self.config.vector_size, distance=Distance.COSINE
            ),
        }

    @property
    @override
    def sparse_vectors_config(self) -> Dict[str, Any]:
        return {
            "document_sparse": SparseVectorParams(),
            "product_title_sparse": SparseVectorParams(),
        }

    @property
    @override
    def payload_indexes(self) -> Dict[str, str]:
        return {
            "store_domain": "keyword",
            "product_id": "keyword",
            "product_type": "keyword",
            "label": "keyword",
            "source": "keyword",
        }

    async def import_all_knowledge_points(
        self,
        domain: str,
        batch_size: int = 10,
        force_recreate_collection: bool = False,
        delete_previous_shop_domain_data: bool = False
    ):
        logger.info(
            f"import domain {domain} knowledge points to index for {self.config.collection_name}"
        )
        store_domain, documents = await load_all_product_knowledge_points(domain)
        if not store_domain or not documents or len(documents) == 0:
            logger.warning(f"No documents found for domain {domain}, skip")
            return
        source_list = list(set([document.source for document in documents]))
        await self.create_collection(force_recreate=force_recreate_collection)
        if delete_previous_shop_domain_data:
            await self.delete_store_domain_knowledge(store_domain, source_list=source_list)

        batches = [
            documents[i:i + batch_size] for i in range(0, len(documents), batch_size)
        ]

        logger.info("Start indexing with async batches")
        for batch in tqdm(batches, total=len(batches), desc="Indexing batches"):
            await self.index_batch(batch)
        await self.check_document_count(store_domain, len(documents))

    async def import_single_product_knowledge_points(self, url: str):
        store_domain, product_knowledge_points = (
            await load_single_product_knowledge_points(url)
        )
        if (
            not store_domain
            or not product_knowledge_points
            or len(product_knowledge_points) == 0
        ):
            logger.warning(f"No product_knowledge_points found for url {url}, skip")
            return
        source_list = list(set([document.source for document in product_knowledge_points]))
        await self.delete_single_product_knowledge(
            store_domain, product_knowledge_points[0].product_id,
            source_list=source_list
        )
        await self.index_batch(product_knowledge_points)
