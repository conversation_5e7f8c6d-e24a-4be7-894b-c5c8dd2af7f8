#!/usr/bin/env python
import logging
import json
import os
import subprocess
import sys

try:
    # For Python 3.x
    from urllib.request import urlopen, Request
    from urllib.parse import urlencode
except ImportError:
    # For Python 2.x
    from urllib2 import urlopen, Request
    from urllib import urlencode

test_db_url = os.environ.get('TEST_DB_URL', 'http://*************:28080/db')


def create_db(engine):
    payload = {'engine': engine}
    request = Request(url=test_db_url, method='POST', data=json.dumps(payload).encode('utf-8'),
                      headers={'Content-Type': 'application/json'})
    with urlopen(request) as resp:
        # example response:
        # {'id': '3f2a781d4159496e8e09bfc619e0c69f', 'host': 'localhost', 'port': 1234,
        # 'name': 'FAaUxXPGXj', 'user': 'deuvpQjIjW', 'password': 'mFGWEwPayE',
        # 'valid_until': '2023-04-29T14:25:52.329904+08:00'}
        return json.load(resp)


def delete_db(id):
    url = '%s?%s' % (test_db_url, urlencode({'id': id}))
    request = Request(url=url, method='DELETE')
    with urlopen(request) as resp:
        if resp.getcode() != 204:
            raise RuntimeError('failed to delete %s' % id)


migrate_guide = """
==================================================================================================
请参考如下文档生成 migrate 脚本：

- 如果当前 MR 中没有提交 migrate 脚本，则在本地执行 `aericch migrate` 生成迁移脚本，并提交到 gitlab。
- 如果当前 MR 中提交过 migrate 脚本，**则删除他们**，然后在本地执行 `aericch migrate` 重新生成，并提交到 gitlab。
==================================================================================================
"""


if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s: %(message)s')
    engine = 'postgres-14'
    resp = create_db(engine)
    db_url = f'postgres://{resp["user"]}:{resp["password"]}@{resp["host"]}:{resp["port"]}/{resp["name"]}'
    config_env = os.environ.copy()
    config_env['DATABASE_URL'] = db_url
    try:
        logging.info('created %s db: %s', engine, db_url)
        cur_sha = subprocess.check_output(['git', 'rev-parse', 'HEAD']).decode('utf-8').strip()
        base_sha = os.environ['CI_MERGE_REQUEST_DIFF_BASE_SHA']
        subprocess.check_call(['git', 'checkout', base_sha])
        subprocess.check_call(['aerich', 'init-db'], env=config_env)
        subprocess.check_call(['aerich', 'upgrade'], env=config_env)
        subprocess.check_call(['git', 'checkout', cur_sha])
        subprocess.check_call(['aerich', 'upgrade'], env=config_env)
        output = subprocess.check_output(['aerich', 'migrate', '--no-input'], env=config_env).decode('utf-8')
        if 'No changes detected' not in output:
            logging.error(f'aerich 发现需要生成新的 migrate 脚本: {output} {migrate_guide}')
            sys.exit(1)
    finally:
        delete_db(resp['id'])
        logging.info('deleted db %s', resp['id'])
