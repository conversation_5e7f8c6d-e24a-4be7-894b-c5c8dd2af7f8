<template>
  <div v-if="show" class="fixed inset-0 flex items-center justify-center z-50 transition-all duration-300">
    <div class="bg-white rounded-lg p-6 max-w-md mx-4 shadow-2xl border border-gray-200 transform transition-all duration-300 scale-100">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">
          批量标注状态
        </h3>
        <button
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X class="h-5 w-5" />
        </button>
      </div>
      
      <div class="mb-6">
        <p class="text-gray-600 mb-4">
          已选择 <span class="font-semibold text-blue-600">{{ selectedCount }}</span> 个卖点，请选择新的状态：
        </p>
        
        <div class="space-y-3">
          <label 
            v-for="status in statusOptions" 
            :key="status.value"
            class="flex items-center gap-3 cursor-pointer"
          >
            <input
              type="radio"
              :value="status.value"
              v-model="selectedStatus"
              class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
            />
            <span class="text-gray-700">{{ status.label }}</span>
            <span 
              class="inline-block px-2 py-1 text-xs rounded-full"
              :style="{
                color: status.color,
                backgroundColor: status.bgColor
              }"
            >
              {{ status.value }}
            </span>
          </label>
        </div>
      </div>
      
      <div class="flex justify-end gap-3">
        <button
          @click="handleClose"
          class="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
        >
          取消
        </button>
        <button
          @click="handleConfirm"
          :disabled="!selectedStatus"
          class="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          确认更新
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { X } from 'lucide-vue-next'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  selectedCount: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['close', 'confirm'])

const selectedStatus = ref('')

const statusOptions = [
  { value: 'fresh', label: '待处理', color: '#0d9488', bgColor: '#ccfbf1' },
  { value: 'editing', label: '编辑中', color: '#0d9488', bgColor: '#ccfbf1' },
  { value: 'ready', label: '已完成', color: '#16a34a', bgColor: '#dcfce7' },
  { value: 'disabled', label: '已禁用', color: '#6b7280', bgColor: '#f3f4f6' },
  { value: 'archived', label: '已归档', color: '#9ca3af', bgColor: '#f9fafb' }
]

const handleClose = () => {
  selectedStatus.value = ''
  emit('close')
}

const handleConfirm = () => {
  if (selectedStatus.value) {
    emit('confirm', selectedStatus.value)
    selectedStatus.value = ''
  }
}
</script>
