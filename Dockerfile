# Stage 1: Build UI
FROM registry.leyantech.com/leyan/pnpm:18-v8 AS ui-builder
WORKDIR /app
COPY ui/package.json ui/pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile
COPY ui/ ./
RUN pnpm build

# Stage 2: Final application
FROM registry.leyantech.com/base-images/crawl4ai:0.7.2.post1

ADD requirements.txt /lain/app/

WORKDIR /lain/app

RUN pip install --no-cache-dir -r /lain/app/requirements.txt

# Copy the built UI from the first stage
COPY --from=ui-builder /app/dist /lain/app/ui/dist

ADD . /lain/app/