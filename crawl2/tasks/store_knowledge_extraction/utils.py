from loguru import logger
from pydantic import BaseModel
from crawl2 import schema
from crawl2.db import models
from typing import Tuple


def convert_label_to_markdown(data):
    markdown_lines = ['| label | detailed_label |', '| --- | --- |']
    detailed_label_dict = {}
    for item in data:
        label = item['label']
        sub_labels = item['sub_labels']
        description = item['description']
        detailed_label = ''
        if sub_labels:
            detailed_label = ', '.join([sub['label'] for sub in sub_labels])
            detailed_label_dict[label] = detailed_label
        elif description:
            detailed_label = description
        markdown_lines.append(f'| {label} | {detailed_label} |')
    return '\n'.join(markdown_lines), detailed_label_dict


def validate_label_value(label, labels):
    """校验 label 是否在 labels 定义中；否则返回 '其他'。"""
    if not isinstance(label, str) or not label:
        return "其他"
    allowed = {item.get('label') for item in labels}
    return label if label in allowed else "其他"


def validate_detailed_label_value(label, detailed_label, detailed_label_dict):
    """校验 detailed_label 是否属于对应 label 的子标签范围；否则返回 '其他'。"""
    if not detailed_label:
        return detailed_label
    allowed_str = detailed_label_dict.get(label)
    if not allowed_str:
        return "其他"
    allowed = [s.strip() for s in allowed_str.split(',')]
    return detailed_label if detailed_label in allowed else "其他"


def fix_label_and_source(knowledge: BaseModel):
    """
    主要是为了解决离线挖掘的标签与线上产品定义的标签不一致的问题，需要将离线挖掘的标签映射为线上产品定义的标签，
    同时如果是需要展示给 B 端用户看的，需要将 source 设置为 'filter_to_review'，在用户没有确认之前，线上的 agent不会使用这些数据.
    """
    # SIMPLE_LABEL_MAPPINGS
    SIMPLE_LABEL_MAPPINGS = {
        "品牌介绍": "Brand Introduction",
        "品牌荣誉": "Awards & Recognitions",
        "购物保障计划": "Purchase Protection Plan",
        "保修政策": "Warranty",
        "退换政策": "Return & Exchange Policy",
        "运费": "Shipping Fees",
        "发货与配送": "Shipping & Delivery",
        "取消订单": "Order Cancellation",
        "税费": "Taxes & Fees",
    }
    SIMPLE_DETAILED_LABEL_MAPPINGS = {
        "品牌历史": "Brand History",
        "产品认证": "Product Certifications",
        "所获奖项": "Awards & Honors",
        "合作伙伴": "Our Partners",
        "媒体报道": "Press & Media",
        "保价政策": "Price Match Guarantee",
        "退货险": "Return Protection",
        "退货运费险": "Return Protection",
        "保修期": "Warranty Period",
        "保修免责条款": "Warranty Exclusions",
        "申请保修方式": "Warranty Claim Process",
        "无理由退换保证": "Hassle-Free Returns",
        "无理由退换条件": "Hassle-Free Return Eligibility",
        "商品问题处理方式": "Defective Product Resolution",
        "退换发起方式": "Return Request Process",
        "退换费用": "Return & Exchange Fees",
        "免费退换条件": "Free Return Eligibility",
        "订单运费": "Order Shipping Cost",
        "免运费条件": "Free Shipping Threshold",
        "发货时间": "Processing Time",
        "配送时间": "Estimated Delivery Time",
        "可送达地区": "Serviceable Areas",
        "可选运输方式": "Available Shipping Methods",
        "地址错误处理方式": "Address Error Resolution",
        "取消订单政策": "Order Cancellation Policy",
        "关税/进口税": "Customs/Import Duties",
        "销售税": "Sales Tax",
    }
    # 拷贝原始字段
    label = knowledge.label
    detailed_label = knowledge.detailed_label
    source = knowledge.source
    # label映射
    if isinstance(label, str) and label:
        mapped_label_en = SIMPLE_LABEL_MAPPINGS.get(label)
        if mapped_label_en:
            label = mapped_label_en
    # detailed_label映射
    if isinstance(detailed_label, str) and detailed_label:
        mapped_detailed_label_en = SIMPLE_DETAILED_LABEL_MAPPINGS.get(detailed_label)
        if mapped_detailed_label_en and source != "uploaded_file":
            # 客户上传的文件，不进行映射
            detailed_label = mapped_detailed_label_en
            source = "filter_to_review"
            logger.info(
                f"知识点 {knowledge.point_id}: detailed_label '{knowledge.detailed_label}' "
                f"映射为 '{mapped_detailed_label_en}', source 更新为 'filter_to_review'."
            )
    new_knowledge = knowledge.model_copy()
    new_knowledge.label = label if label else ""
    new_knowledge.detailed_label = detailed_label if detailed_label else ""
    new_knowledge.source = source
    return new_knowledge


async def convert_store_knowledge_to_store_knowledge_point(
    store_knowledge_point: models.StoreKnowledgePoint,
) -> Tuple[str, schema.StoreKnowledgePoint]:
    site = await store_knowledge_point.site
    store_domain = site.shopify_domain
    point = schema.StoreKnowledgePoint(
        point_id=store_knowledge_point.point_id,
        topic=store_knowledge_point.topic,
        question=store_knowledge_point.question,
        answer=store_knowledge_point.answer,
        store_domain=store_domain,
        source=store_knowledge_point.source,
        source_detail=store_knowledge_point.source_detail,
        quality=store_knowledge_point.quality,
        label=store_knowledge_point.label,
        detailed_label=store_knowledge_point.detailed_label or "",
        extra_questions=store_knowledge_point.extra_questions or [],
    )
    point = fix_label_and_source(point)
    return store_domain, point
