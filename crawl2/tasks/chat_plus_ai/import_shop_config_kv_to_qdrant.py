import httpx
import os
import asyncio
import json
import pycountry
from typing import List
from loguru import logger
from crawl2.config import settings
from crawl2.tasks.celery_wrap import celery_task
from crawl2.qdrant_index.lechat_product_knowledge_point_index import (ProductDetailPoint, ProductDetailPointIndex,
                                                                      IndexConfig)
from crawl2.tasks.chat_plus_ai.utils import get_item_info, get_item_url_only, get_item_name_only, batch_index_points
from crawl2.clients.llm import LLMConfig, call_llm
from crawl2.tasks.chat_plus_ai.prompts import EXTRA_QUESTIONS_PROMPT

# 全局常量：数据源标识
SOURCE_SHOP_CONFIG_KV = 'shop_config_kv'
ENV = os.environ.get('env', 'stq').lower()
BASE_API_URL = f"https://{ENV}-api.leyanbot.com/oversea-knowledge/spu_knowledges/v3"

# 全局并发限制：限制同时执行的update_shop_config_kv_by_knowledge_id任务数量
UPDATE_SHOP_CONFIG_KV_SEMAPHORE = asyncio.Semaphore(settings.TASK_CONCURRENCY)

# 限制extra_questions生成的并发数量
EXTRA_QUESTIONS_SEMAPHORE = asyncio.Semaphore(4)  # 最多同时生成10个extra_questions

# LLM配置
llm_conf = LLMConfig('usq', 'qwen2.5-32b-chat-fp8-offline', 0, 2048)


def get_qdrant_indexer() -> ProductDetailPointIndex:
    """获取 Qdrant 索引器实例"""
    index_config = IndexConfig(
        collection_name=settings.LECHAT_PRODUCT_KV_KNOWLEDGE_COLLECTION,
        embedding_client_url=settings.EMBEDDING_API_URL,
        sparse_embedding_client_url=settings.SPARSE_EMBEDDING_API_URL,
        qdrant_url=settings.QDRANT_URL,
        vector_size=settings.QDRANT_VECTOR_SIZE
    )
    return ProductDetailPointIndex(index_config)


async def fetch_knowledge_by_id(knowledge_id: str):
    """
    根据knowledge_id获取知识详情
    :param knowledge_id: 知识ID
    :return: 知识字典
    """
    try:
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {settings.LECHAT_KNOWLEDGE_API_KEY}'
        }
        url = f"{BASE_API_URL}/query_all_knowledge_by_knowledge/{knowledge_id}"

        async with httpx.AsyncClient() as client:
            resp = await client.get(url, headers=headers, timeout=30)
            resp.raise_for_status()
            response_data = resp.json()
            return response_data.get("data", {})
    except Exception as ex:
        logger.error(f"knowledge_id={knowledge_id}没数据, {ex}")
        return {}


@celery_task
async def delete_shop_config_kv_by_knowledge_id(knowledge_id: str):
    """
    根据knowledge_id删除shop_config_kv来源的知识点
    :param knowledge_id: 知识点ID
    """
    knowledge_id = str(knowledge_id)
    logger.info(f"开始delete_shop_config_kv_by_knowledge_id任务: {knowledge_id}")
    indexer = get_qdrant_indexer()
    await indexer.delete_by_knowledge_id(knowledge_id, SOURCE_SHOP_CONFIG_KV)
    logger.info(f"已删除knowledge_id={knowledge_id}, source={SOURCE_SHOP_CONFIG_KV}对应的数据点")


def parse_knowledge_dict_for_spu(knowledge_dict, spu_info, item_url, item_name):
    """
    解析知识字典为单个SPU，返回ProductDetailPoint列表
    对相同title+content的知识点进行聚合，将sku_id合并到sku_id_list中
    """
    results = []

    org_id = str(knowledge_dict.get("orgId", ""))
    knowledge_id = str(knowledge_dict.get("knowledgeId", ""))
    title = knowledge_dict.get("knowledgeName", "")
    content = "\n".join(seg.get("content", "") for seg in knowledge_dict.get("segments", []))
    is_direct_answer = 1 if knowledge_dict.get("valueIsDirectAnswer", False) else 0

    platform = spu_info.get("platform", "")
    region = spu_info.get("region", "")
    shop_id = str(spu_info.get("shopId", ""))
    product_id = str(spu_info.get("spuId", ""))
    level = spu_info.get("knowledgeLevel", "SPU")
    skus = spu_info.get("skus", [])

    store_domain = f"{platform}-{region}-{shop_id}".lower()

    base_kwargs = dict(
        org_id=str(org_id),
        url=item_url,
        product_id=str(product_id),
        product_title=item_name,
        product_type="",
        product_tags="",
        product_page="",
        variant_info=None,
        property_values=None,
        product_summary=None,
        store_domain=store_domain,
        title=title,
        content=content,
        label="",
        source=SOURCE_SHOP_CONFIG_KV,
        level=level,
        is_direct_answer=is_direct_answer,
        knowledge_id=str(knowledge_id),
    )

    if level == "SPU":
        results.append(ProductDetailPoint(**base_kwargs, sku_id_list=[]))
    elif level == "SKU":
        for sku in skus:
            sku_id = str(sku.get("skuId", ""))
            results.append(ProductDetailPoint(**base_kwargs, sku_id_list=[sku_id]))
    else:
        logger.error(f"未知knowledgeLevel: {level}, spu_info: {spu_info}")
        return []

    return results


def aggregate_points_by_title_content(points):
    """
    对相同title+content的知识点进行聚合，将sku_id合并到sku_id_list中
    过滤掉title或content为空的知识点
    :param points: ProductDetailPoint列表
    :return: 聚合后的ProductDetailPoint列表
    """
    if not points:
        return points

    # 过滤掉title或content为空的知识点
    filtered_points = []
    for point in points:
        if point.title and point.title.strip() and point.content and point.content.strip():
            filtered_points.append(point)
        else:
            logger.warning(
                f"过滤掉title或content为空的知识点: product_id={point.product_id}, "
                f"title='{point.title}', content='{point.content}'")

    if not filtered_points:
        logger.warning("过滤后没有有效的知识点")
        return []

    # 使用(title, content)作为聚合键
    aggregated_dict = {}

    for point in filtered_points:
        key = (point.product_id, point.store_domain, point.title, point.content)
        if key not in aggregated_dict:
            # 第一个点，直接添加
            aggregated_dict[key] = point
        else:
            # 已存在相同title+content的点，需要聚合sku_id_list
            existing_point = aggregated_dict[key]

            # 处理sku_id_list的聚合
            existing_sku_ids = set(existing_point.sku_id_list)
            new_sku_ids = set(point.sku_id_list)

            # 合并并去重
            all_sku_ids = existing_sku_ids.union(new_sku_ids)

            # 更新sku_id_list
            aggregated_dict[key].sku_id_list = sorted(list(all_sku_ids))

    return list(aggregated_dict.values())


def get_country_name(alpha2_code):
    """
    根据ISO 3166-1两位字母代码获取国家名称
    优先级：common_name > name > official_name > 原输入值
    :param alpha2_code: 两位大写国家代码 (如 "MY", "US")
    :return: 国家全称或原始输入（如果未找到）
    """
    try:
        country = pycountry.countries.get(alpha_2=alpha2_code)
        if not country:
            return alpha2_code

        # 按优先级返回名称
        if hasattr(country, 'common_name') and country.common_name:
            return country.common_name
        if hasattr(country, 'name') and country.name:
            return country.name
        if hasattr(country, 'official_name') and country.official_name:
            return country.official_name

        return alpha2_code
    except (KeyError, LookupError):
        return alpha2_code


async def generate_extra_questions_for_point(point: ProductDetailPoint) -> List[str]:
    """
    为单个知识点生成extra_questions
    :param point: ProductDetailPoint实例
    :return: extra_questions列表
    """
    extra_body_extra_question = {
        "guided_json": {
            "type": "object",
            "properties": {
                "question_list": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            },
            "required": ["question_list"],
            "additionalProperties": False
        }
    }
    async with EXTRA_QUESTIONS_SEMAPHORE:
        try:
            # 从store_domain中提取国家信息
            # store_domain格式: "platform-region-shop_id"
            parts = point.store_domain.split('-')
            if len(parts) >= 2:
                region = parts[1].upper()
            else:
                region = "US"  # 默认使用US

            # 构建知识点信息字符串
            knowledge_info = json.dumps({"title": point.title, "content": point.content}, ensure_ascii=False)

            # 格式化prompt
            prompt = EXTRA_QUESTIONS_PROMPT.format(
                region=get_country_name(region),
                knowledge_info=knowledge_info
            )

            # 调用LLM生成extra_questions
            extra_result = await call_llm(
                llm_conf,
                prompt,
                parse_json=True,
                extra_body=extra_body_extra_question,
                extra_headers={"x-litellm-tags": f"tenant={point.org_id}"}
            )
            extra_questions = []

            if extra_result and isinstance(extra_result, dict):
                extra_questions = extra_result.get('question_list', [])

            logger.info(
                f"为知识点生成extra_questions: title={point.title}, content={point.content}, "
                f"extra_questions={extra_questions}")
            return extra_questions

        except Exception as e:
            logger.error(f"生成extra_questions失败: title={point.title}, content={point.content}, error={e}")
            return []


async def generate_extra_questions_for_points(points: List[ProductDetailPoint]) -> None:
    """
    为知识点列表生成extra_questions，避免重复生成相同的region+title+content
    :param points: ProductDetailPoint列表
    """
    logger.info("开始为知识点生成extra_questions")

    # 创建region+title+content的唯一键，避免重复生成
    unique_questions_map = {}
    unique_points = []

    for point in points:
        # 从store_domain中提取region
        parts = point.store_domain.split('-')
        region = parts[1].upper() if len(parts) >= 2 else "US"

        # 创建唯一键：region+title+content
        unique_key = (region, point.title, point.content)

        if unique_key not in unique_questions_map:
            unique_questions_map[unique_key] = point
            unique_points.append(point)

    logger.info(f"去重后有{len(unique_points)}个唯一的知识点需要生成extra_questions")

    # 只为唯一的知识点生成extra_questions
    tasks = [generate_extra_questions_for_point(point) for point in unique_points]
    extra_questions_results = await asyncio.gather(*tasks, return_exceptions=True)

    # 创建结果映射，使用唯一键而不是对象作为键
    unique_results_map = {}
    for i, (point, result) in enumerate(zip(unique_points, extra_questions_results)):
        parts = point.store_domain.split('-')
        region = parts[1].upper() if len(parts) >= 2 else "US"
        unique_key = (region, point.title, point.content)

        if isinstance(result, Exception):
            logger.error(f"生成extra_questions失败: title={point.title}, error={result}")
            unique_results_map[unique_key] = []
        else:
            unique_results_map[unique_key] = result

    # 为所有知识点分配extra_questions（包括重复的）
    for point in points:
        parts = point.store_domain.split('-')
        region = parts[1].upper() if len(parts) >= 2 else "US"
        unique_key = (region, point.title, point.content)

        # 直接从结果映射中获取对应的extra_questions
        point.extra_questions = unique_results_map[unique_key]

    logger.info(f"已完成{len(points)}个知识点的extra_questions分配（其中{len(unique_points)}个唯一生成）")


@celery_task
async def update_shop_config_kv_by_knowledge_id(knowledge_id: str):
    """
    根据knowledge_id获取知识字典并导入到Qdrant
    :param knowledge_id: 知识ID
    """
    async with UPDATE_SHOP_CONFIG_KV_SEMAPHORE:
        try:
            # 1. 根据knowledge_id获取知识字典
            knowledge_id = str(knowledge_id)
            logger.info(f"开始update_shop_config_kv_by_knowledge_id任务: {knowledge_id}")
            knowledge_dict = await fetch_knowledge_by_id(knowledge_id)
            logger.info(f"knowledge_dict={knowledge_dict}")
            knowledge_id_str = str(knowledge_dict.get("knowledgeId", ""))

            logger.info(
                f"获取到知识字典: knowledge_id={knowledge_id_str}, name={knowledge_dict.get('knowledgeName', '')}")

            # 2. 先删除现有的knowledgeId对应的数据
            indexer = get_qdrant_indexer()
            await indexer.delete_by_knowledge_id(knowledge_id_str, SOURCE_SHOP_CONFIG_KV)
            logger.info(f"已删除knowledge_id={knowledge_id_str}, source={SOURCE_SHOP_CONFIG_KV}对应的现有数据")

            all_points = []
            spus = knowledge_dict.get("spus", [])

            # 3. 检查spus是否为空
            if not spus:
                logger.warning(f"knowledge_id={knowledge_id_str} 对应的spus为空，无法创建知识点")
                return

            # 4. 遍历每个SPU，创建ProductDetailPoint
            for spu_info in spus:
                platform = spu_info.get("platform", "")
                region = spu_info.get("region", "")
                shop_id = str(spu_info.get("shopId", ""))
                product_id = str(spu_info.get("spuId", ""))

                # 获取商品信息
                item_info = get_item_info(platform, region, shop_id, product_id, need_refresh=False)
                item_url = get_item_url_only(item_info)
                item_name = get_item_name_only(item_info)

                # 解析单个SPU的知识点
                spu_points = parse_knowledge_dict_for_spu(knowledge_dict, spu_info, item_url, item_name)
                all_points.extend(spu_points)

            if not all_points:
                logger.warning("未生成任何知识点，跳过导入")
                return

            # 5. 对相同title+content的知识点进行聚合
            logger.info(f"聚合前有{len(all_points)}条知识点")
            aggregated_points = aggregate_points_by_title_content(all_points)
            logger.info(f"聚合后有{len(aggregated_points)}条知识点")

            # 6. 为每个知识点生成extra_questions
            await generate_extra_questions_for_points(aggregated_points)

            # 7. 批量导入到Qdrant
            # await indexer.create_collection()
            await batch_index_points(indexer, aggregated_points)
            logger.info(f"已导入{len(aggregated_points)}条知识到Qdrant，knowledge_id={knowledge_id_str}")

        except Exception as e:
            logger.error(f"update_shop_config_kv_by_knowledge_id任务执行失败: knowledge_id={knowledge_id}, error={e}")
            raise


@celery_task
async def delete_shop_config_kv_by_org_id_and_source(org_id: str, source: str = None):
    """
    根据org_id和可选的source删除知识点
    :param org_id: 组织ID
    :param source: 数据源标识，如果为None则删除org_id对应的所有数据
    """
    org_id = str(org_id)
    if source is not None:
        source = str(source)
        logger.info(f"开始delete_shop_config_kv_by_org_id_and_source任务: org_id={org_id}, source={source}")
    else:
        logger.info(f"开始delete_shop_config_kv_by_org_id_and_source任务: org_id={org_id} (删除所有数据源)")

    indexer = get_qdrant_indexer()
    await indexer.delete_by_org_id_and_source(org_id, source)

    if source is not None:
        logger.info(f"已删除org_id={org_id}, source={source}对应的数据点")
    else:
        logger.info(f"已删除org_id={org_id}对应的所有数据点")


@celery_task
async def create_collection_with_knowledge_kv(collection_name: str):
    """
    创建指定名称的Qdrant collection
    :param collection_name: 要创建的collection名称
    """
    try:
        logger.info(f"开始创建collection: {collection_name}")

        # 获取默认索引器并修改collection名称
        indexer = get_qdrant_indexer()
        indexer.config.collection_name = collection_name

        # 创建collection
        await indexer.create_collection()

        logger.info(f"成功创建collection: {collection_name}")

    except Exception as e:
        logger.error(f"创建collection失败: collection_name={collection_name}, error={e}")
        raise
