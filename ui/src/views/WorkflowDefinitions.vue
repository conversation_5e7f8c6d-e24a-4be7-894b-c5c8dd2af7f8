<script setup>
import { ref, onMounted, computed } from 'vue'
import { api } from '@/api/api.js'
import { AgGridVue } from 'ag-grid-vue3'
import { themeAlpine } from 'ag-grid-community'

import Navigation from '../components/common/Navigation.vue'
import TruncatedCellRenderer from '../components/common/TruncatedCellRenderer.vue'
import WorkflowTriggerWrapper from '../components/workflow/WorkflowTriggerWrapper.vue'
import WorkflowEditor from '../components/workflow/WorkflowEditor.vue'

const workflowFactories = ref([])
const loading = ref(true)
const error = ref(null)
const searchKeyword = ref('')

const paramsFormatter = (value) => {
  if (value && value.properties) {
    return Object.keys(value.properties).join(', ');
  }
  return '';
}

const columnDefs = [
  { field: 'name', headerName: 'Name', sortable: true, filter: false, flex: 2, cellClass: 'ag-left-aligned-cell' },
  { field: 'description', headerName: 'Description', sortable: true, filter: false, flex: 3, cellRenderer: TruncatedCellRenderer, cellClass: 'ag-left-aligned-cell' },
  {
    field: 'params',
    headerName: 'Parameters',
    flex: 1,
    filter: false,
    cellRenderer: TruncatedCellRenderer,
    cellRendererParams: {
        formatter: paramsFormatter
    }
  },
  {
    headerName: '操作',
    field: 'actions',
    flex: 1,
    filter: false,
    cellRenderer: WorkflowTriggerWrapper,
    cellRendererParams: params => ({
      workflowName: params.data.name,
      displayName: 'Trigger'
    })
  }
]

const defaultColDef = {
  resizable: true,
  sortable: true,
  filter: false,
  wrapText: true,
}

const gridOptions = {
  animateRows: true,
  domLayout: 'normal',
  rowHeight: 48,
  headerHeight: 48,
  theme: themeAlpine
}

const fetchWorkflowFactories = async () => {
  try {
    loading.value = true
    const response = await api.get('/api/v1/workflows/factories')
    workflowFactories.value = response.data
  } catch (err) {
    error.value = err.message
    console.error('Error fetching workflow factories:', err)
  } finally {
    loading.value = false
  }
}

const filteredWorkflowFactories = computed(() => {
  if (!searchKeyword.value.trim()) {
    return workflowFactories.value
  }
  
  const keywords = searchKeyword.value.toLowerCase().trim().split(/\s+/).filter(k => k.length > 0)
  return workflowFactories.value.filter(workflow => {
    const workflowName = workflow.name?.toLowerCase() || ''
    const workflowDescription = workflow.description?.toLowerCase() || ''
    
    return keywords.every(keyword => 
      workflowName.includes(keyword) || workflowDescription.includes(keyword)
    )
  })
})

onMounted(() => {
  fetchWorkflowFactories()
})
</script>

<template>
  <div class="flex flex-col h-screen w-full pt-0">
    <Navigation
        :breadcrumbs="[
        { text: 'Sites', path: '/' },
        { text: 'Workflow Definitions' }
      ]"
    />

    <div class="flex justify-center items-center p-4 px-8 w-full bg-white shadow sticky top-16 z-10">
        <div>
          <WorkflowEditor display-name="创建自定义工作流" />
        </div>
    </div>

    <div class="flex-1 p-4 px-8 flex flex-col w-full bg-gray-50">
      <div v-if="loading" class="flex justify-center items-center h-full text-gray-600 bg-white rounded-lg shadow">
        Loading workflow definitions...
      </div>
      <div v-else-if="error" class="flex justify-center items-center h-full text-red-500 bg-white rounded-lg shadow">
        Error: {{ error }}
      </div>
      <div v-else-if="workflowFactories.length" class="flex-1 w-full min-w-[1280px] bg-white rounded-lg shadow">
        <ag-grid-vue
            style="width: 100%;"
            :columnDefs="columnDefs"
            :rowData="filteredWorkflowFactories"
            :defaultColDef="defaultColDef"
            :gridOptions="gridOptions"
            class="w-full h-full"
            :frameworkComponents="{ WorkflowTriggerWrapper }"
        >
        </ag-grid-vue>
      </div>
       <div v-else class="flex justify-center items-center h-full text-gray-600 bg-white rounded-lg shadow">
        No workflow definitions found.
      </div>
    </div>
  </div>
</template> 