from loguru import logger

from crawl2 import utils
from crawl2.clients import http
from crawl2.db import operations
from crawl2.schema import ProductMetaData
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync


async def _crawl_product_metadata(url: str) -> ProductMetaData | None:
    """
    Crawl product metadata from a single product URL.

    :param url:  The product URL to crawl.
    :return: A ProductMetaData object containing the product metadata or None if an error occurs.
    """
    try:
        response = await http.http_get(url + ".json")
        response.raise_for_status()
        json = response.json()
        logger.info(f"get product meta from {url}")
        return ProductMetaData.from_dict(url, json)
    except Exception as e:
        logger.opt(exception=e).error(f"Error crawling product meta from {url}: {e}")
        return None


@celery_task
async def crawl_single_product_metadata(product_url: str, clear_existing: bool = False):
    """
    爬取单个商品页面的元数据
    """
    if not clear_existing:
        existed = await operations.get_product_metadata(product_url)
        if existed:
            logger.info(f"Product metadata for {product_url} already exists, skipping crawl.")
            return
    product = await _crawl_product_metadata(product_url)
    if product:
        domain = utils.extract_domain_from_shopify_site_url(product_url)
        await operations.save_products_metadata(domain, product)


@celery_task
async def crawl_multiple_products_metadata(product_urls: list[str], clear_existing: bool = False):
    """
    爬取多个商品页面的元数据
    """
    tasks = [crawl_single_product_metadata(url, clear_existing) for url in product_urls]
    await CeleryTaskTqdmAsync.gather(*tasks, desc="Crawling product metadata")


@celery_task
async def crawl_all_products_metadata(domain: str, clear_existing: bool = False):
    """
    爬取指定站点所有商品页面的元数据
    """
    site = await operations.get_site(domain)
    await crawl_multiple_products_metadata(site.product_urls, clear_existing=clear_existing)
