import json
from uuid import uuid4

from loguru import logger

from crawl2 import schema
from crawl2.clients import llm
from crawl2.clients.qdrant_importer import qdrant_importer_instance
from crawl2.db import operations
from crawl2.tasks.celery_wrap import celery_task
from .labels import LABELS
from .prompts import COLLECTION_KNOWLEDGE_EXTRA_PROMPT, TAGGING_QAPAIR_LABEL_PROMPT
from .utils import convert_label_to_markdown, validate_label_value
from ..task_tqdm import CeleryTaskTqdmAsync

llm_conf = llm.LLMConfig('usq', 'qwen2.5-32b-chat-fp8-offline', 0, 1024)


async def extract_collection_knowledge_from_content(collection: schema.CollectionMetaData, source: str) \
        -> list[schema.CollectionKnowledgePoint] | None:
    prompt = COLLECTION_KNOWLEDGE_EXTRA_PROMPT.substitute(title=collection.title, description=collection.description,
                                                          markdown_content=collection.raw_content)
    extra_body = {
        "guided_json": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "topic": {"type": "string"},
                    "title": {"type": "string"},
                    "content": {"type": "string"}
                },
                "required": ["topic", "title", "content"],
                "additionalProperties": False
            }
        }
    }
    result = await llm.call_llm(llm_conf, prompt, parse_json=True, extra_body=extra_body)
    if result is None:
        return None
    logger.info(f"crawled collection knowledge for {collection.url}: {result}")
    knowledge_points = []
    for item in result:
        if type(item) is not dict:
            logger.info(f"extract_collection_knowledge_from_content不是dict: {item}")
            continue
        if item.get('content', '').strip() == '':
            continue
        point_id = str(uuid4())
        topic = item.get("topic", "")
        title = item.get("title", "")
        content = item.get("content")
        logger.info(f"{point_id} topic: {topic}, title: {title}, content: {content}")
        # label打标
        qa_str = json.dumps({"title": title, "content": content}, ensure_ascii=False)
        label_list_str, _ = convert_label_to_markdown(LABELS)
        label_prompt = TAGGING_QAPAIR_LABEL_PROMPT.format(label_list_str, qa_str)
        label = await llm.call_llm(llm_conf, label_prompt, parse_json=False)
        label = str(label).split('\n')[0].split(' ')[0]
        label = validate_label_value(label, LABELS)
        logger.info(f"{point_id} label: {label}")
        knowledge_points.append(schema.CollectionKnowledgePoint(
            point_id=point_id,
            topic=topic,
            title=title,
            content=content,
            collection_id=collection.collection_id,
            source=source,
            source_detail=collection.url,
            label=label
        ))
    return knowledge_points


async def _process_single_collection(
        domain: str,
        collection: schema.CollectionMetaData,
        source: str,
        clear_existing: bool = False
):
    existed = await operations.get_collection_knowledge_point(source, collection.url)
    if existed and not clear_existing:
        logger.info(f"Collection knowledge point already exists for {collection.url} and source {source}, skipping extraction.")
        return
    elif existed and clear_existing:
        logger.info(f"Clearing existing collection knowledge point for {collection.url} and source {source}.")
    knowledge_points = await extract_collection_knowledge_from_content(collection, source)
    if knowledge_points:
        await operations.save_collection_knowledge_points(domain, knowledge_points)
    return knowledge_points


@celery_task
async def extract_all_collection_knowledge(domain: str, clear_existing: bool = False):
    """
    为指定站点的所有商品列表页挖掘知识点
    """
    logger.info(f"开始extract_all_collection_knowledge任务: {domain}")
    collections = await operations.list_collection_metadata(domain)
    tasks = []
    for collection in collections:
        tasks.append(_process_single_collection(domain, collection, "collections", clear_existing=clear_existing))
    await CeleryTaskTqdmAsync.gather(*tasks, desc="Extracting collection knowledge")


@celery_task
async def sync_all_collection_knowledge_to_qdrant(domain: str):
    """
    同步站点所有商品列表页知识点到 Qdrant
    """
    await qdrant_importer_instance.import_all_collection_knowledge_points(domain)
