from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "page_elements" DROP CONSTRAINT IF EXISTS "uid_page_elemen_page_id_377133";
        ALTER TABLE "page_elements" RENAME COLUMN "element" TO "target";
        CREATE UNIQUE INDEX IF NOT EXISTS "uid_page_elemen_page_id_4ba707" ON "page_elements" ("page_id", "target");"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX IF EXISTS "uid_page_elemen_page_id_4ba707";
        ALTER TABLE "page_elements" RENAME COLUMN "target" TO "element";
        CREATE UNIQUE INDEX IF NOT EXISTS "uid_page_elemen_page_id_377133" ON "page_elements" ("page_id", "element");"""
