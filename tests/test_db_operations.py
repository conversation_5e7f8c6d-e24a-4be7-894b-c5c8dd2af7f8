import asyncio

import celery
import pytest
from loguru import logger
from tortoise import Tortoise

from crawl2.db import operations, models
from crawl2.tasks.demo import simple_demo
from crawl2.tasks.task_tqdm import CeleryTaskTqdm, CeleryTaskTqdmAsync


@pytest.fixture(scope="module", autouse=True)
async def db_init():
    # initialize the in-memory SQLite database for testing
    await Tortoise.init(
        db_url="sqlite://:memory:",
        modules={"models": ["crawl2.db.models"]}
    )
    await Tortoise.generate_schemas()
    logger.info("Database initialized.")
    yield
    await Tortoise.close_connections()


async def test_celery_task_workflow_operations():
    chain = celery.chain(simple_demo.si(domain='https://example.com'))
    workflow_id = await operations.create_workflow('test', chain)
    workflow = await operations.get_workflow(workflow_id)
    assert workflow.status == 'PENDING'
    task = chain.tasks[0]
    await operations.mark_celery_task_start(task_id='first_task_id', workflow_id=workflow_id,
                                            name=task.name, params={})
    workflow = await operations.get_workflow(workflow_id)
    assert workflow.status == 'RUNNING'
    await operations.mark_celery_task_failure(task_id='first_task_id', error='Test error message')
    workflow = await operations.get_workflow(workflow_id)
    assert workflow.status == 'FAILED'
    await operations.mark_celery_task_success(task_id='first_task_id', result='Test result')
    workflow = await operations.get_workflow(workflow_id)
    assert workflow.status == 'SUCCESS'


async def test_celery_task_progress_operations():
    CeleryTaskTqdm.mark_task_started(1)
    for i in CeleryTaskTqdm(range(5), desc='Processing'):
        pass
    await CeleryTaskTqdm.mark_task_finished()
    record = await models.TqdmRecord.get_or_none(relate_id=1, relate_type='celery_task')
    assert record is not None
    assert record.n == record.total == 5
    assert record.desc == 'Processing'


async def test_celery_task_async_progress_operations():
    CeleryTaskTqdmAsync.mark_task_started(2)
    async_tasks = [asyncio.sleep(0.1) for _ in range(5)]
    await CeleryTaskTqdmAsync.gather(*async_tasks, desc='Processing Async')
    await CeleryTaskTqdm.mark_task_finished()
    record = await models.TqdmRecord.get_or_none(relate_id=2, relate_type='celery_task')
    assert record is not None
    assert record.n == record.total == 5
    assert record.desc == 'Processing Async'
