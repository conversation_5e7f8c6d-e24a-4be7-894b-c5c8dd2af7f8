import time

from bs4 import BeautifulSoup
from loguru import logger
from pocketflow import As<PERSON>N<PERSON>, AsyncBatchNode

from crawl2 import utils
from crawl2.clients import http, browser, cos
from crawl2.db import operations, models


class NormalizePageUrl(AsyncNode):
    async def prep_async(self, shared):
        domain, path = utils.split_domain_and_path(self.params["page_url"])
        return domain, domain + path

    async def post_async(self, shared, prep_res, exec_res):
        domain, page_url = prep_res
        shared["domain"] = domain
        shared["page_url"] = page_url
        return "default"


class LoadInjectJsCode(AsyncNode):
    """加载注入到 shopify product page 的 js 代码"""

    async def prep_async(self, shared):
        from .utils import load_js_script

        self.params["js_code"] = [
            load_js_script(js_name)
            for js_name in [
                "markBoundingBoxAndXPath.js",
                "markHiddenElement.js",
                "removeOverlayElements.js"
            ]
        ]


class CrawlShopifyProductMetadata(AsyncNode):
    """通过 product.json 获取 product id"""

    def __init__(self):
        super().__init__(max_retries=10, wait=1)

    async def prep_async(self, shared):
        page_url = shared["page_url"]
        return f"https://{page_url}.json"

    async def exec_async(self, product_metadata_url: str):
        response = await http.http_get(product_metadata_url)
        response.raise_for_status()
        product_metadata = response.json()
        return product_metadata["product"]["id"]

    async def post_async(self, shared, prep_res, exec_res):
        shared["product_id"] = exec_res
        return "default"


class CrawlShopifyProductWebPage(AsyncNode):
    """使用无头浏览器抓取单个 shopify 页面的原始 html"""

    async def prep_async(self, shared):
        from crawl4ai import CrawlerRunConfig, CacheMode

        page_url = f"https://{shared['page_url']}"
        crawl_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            wait_until="domcontentloaded",
            remove_overlay_elements=True,
            exclude_external_links=True,
            exclude_social_media_links=True,
            js_code=self.params["js_code"],
            scan_full_page=True,
            screenshot=True,
            wait_for_images=True,
            delay_before_return_html=10,
        )
        return page_url, crawl_config

    async def exec_async(self, prep_res):
        page_url, crawl_config = prep_res
        async with browser.get_web_crawler("mobile") as crawler:
            result = await crawler.arun(page_url, config=crawl_config)
        return result.html, result.screenshot

    async def post_async(self, shared, prep_res, exec_res):
        html, screenshot = exec_res
        shared["html"] = html
        shared["screenshot"] = screenshot
        return "default"


class PreprocessHTML(AsyncNode):
    """对原始 html 进行预处理，过滤掉黑名单标签和页面不可见的 DOM"""

    async def prep_async(self, shared):
        from bs4 import BeautifulSoup
        from .utils import HIDDEN_MARK_ATTRIBUTE, HIDDEN_MARK_VALUE

        soup = BeautifulSoup(shared["html"], "html.parser")
        for tag in soup.find_all(["script", "link", "style", "svg", "path", "iframe"]):
            tag.decompose()
        for tag in soup.find_all(attrs={HIDDEN_MARK_ATTRIBUTE: HIDDEN_MARK_VALUE}):
            tag.decompose()
        return "".join(line.strip() for line in soup.prettify().split("\n"))

    async def post_async(self, shared, prep_res, exec_res):
        shared["html"] = prep_res
        return "default"


class HTMLToLLMFormat(AsyncNode):

    async def prep_async(self, shared):
        from bs4 import BeautifulSoup
        from lxml import etree

        soup = BeautifulSoup(shared["html"], "html.parser")
        if self.params["allow_img"]:
            llm_format_node = self.build_node(soup.body)
        else:
            llm_format_node = self.build_node_without_img(soup.body)
        return etree.tostring(llm_format_node, pretty_print=True).decode("utf-8")

    async def post_async(self, shared, prep_res, exec_res):
        shared["html_llm_format"] = prep_res
        return "default"

    def build_node_without_img(self, tag):
        from lxml import etree
        from bs4 import NavigableString, Tag

        if not isinstance(tag, Tag):
            return None

        node = etree.Element("node")
        try:
            node.set("id", tag.attrs["data-shop-genius-id"])
        except KeyError:
            return None
        child_nodes = []
        text_parts = []
        for child in tag.children:
            if isinstance(child, NavigableString) and (txt := child.strip()):
                text_parts.append(txt)
            elif isinstance(child, Tag):
                if (child_node := self.build_node_without_img(child)) is not None:
                    child_nodes.append(child_node)
        # 合并逻辑：父无文本且只有一个子节点 -> 将子节点内容合并到父节点（保留父的 id，丢弃子节点这层）
        if not text_parts and len(child_nodes) == 1:
            only = child_nodes[0]
            if only.text:
                node.text = only.text
            for gc in list(only):  # 合并子节点的子元素
                node.append(gc)
        else:
            if text_parts:
                node.text = " ".join(text_parts)
            for cn in child_nodes:
                node.append(cn)

        # 既无文本也无子元素则丢弃
        if (node.text is None or node.text.strip() == "") and len(node) == 0:
            return None
        return node

    def build_node(self, tag):
        from lxml import etree
        from bs4 import NavigableString, Tag

        if not isinstance(tag, Tag):
            return None

        node = etree.Element("node")
        try:
            node.set("id", tag.attrs["data-shop-genius-id"])
            if tag.name == "img":
                node.set("src", tag.attrs["src"])
        except KeyError:
            return None
        child_nodes = []
        text_parts = []
        for child in tag.children:
            if isinstance(child, NavigableString) and (txt := child.strip()):
                text_parts.append(txt)
            elif isinstance(child, Tag):
                if (child_node := self.build_node(child)) is not None:
                    child_nodes.append(child_node)

        # 合并逻辑：父无文本且只有一个子节点 -> 将子节点内容合并到父节点（保留父的 id，丢弃子节点这层）
        if not text_parts and len(child_nodes) == 1 and child_nodes[0].get("src") is None:
            only = child_nodes[0]
            if only.text:
                node.text = only.text
            for gc in list(only):  # 合并子节点的子元素
                node.append(gc)
        else:
            if text_parts:
                node.text = " ".join(text_parts)
            for cn in child_nodes:
                node.append(cn)

        # 既无文本也无子元素则丢弃
        if (node.text is None or node.text.strip() == "") and len(node) == 0 and node.get("src") is None:
            return None
        return node


class NER(AsyncNode):
    """使用 LLM 对 HTML DOM 进行商品实体类型分类"""

    async def prep_async(self, shared):
        from .utils import load_general_prompt, tag_to_llm_content

        html_content = tag_to_llm_content(self.params["tag"])
        prompt = load_general_prompt(html_content)
        return {
            "prompt": prompt,
            "llm_conf": self.params["llm_conf"],
            "start_at": time.time()
        }

    async def exec_async(self, prep_res):
        from crawl2.clients import llm

        llm_output = await llm.call_llm(conf=prep_res["llm_conf"], prompt=prep_res["prompt"])
        match llm_output:
            case {"entity": str() as entity}:
                return entity  # 返回完整的实体标签
            case _:
                return None

    async def post_async(self, shared, prep_res, exec_res):
        """根据实体标签决定流转分支。"""
        self.params["element_tag"] = exec_res
        self.params["unmatched"] = exec_res is None
        shared.setdefault("page_elements", [])
        shared.setdefault("debug", []).append({
            "start_at": prep_res["start_at"],
            "end_at": time.time(),
            "entity": exec_res
        })

        if exec_res is None:
            return "continue"

        if exec_res == "主图":
            return "main_image"
        # 商品卖点和功能列表都先走卖点指示器来做二次判断
        elif exec_res in ("商品卖点", "商品功能列表"):
            return "selling_point_indicator"
        else:
            # 其他所有实体类型（如SKU区域, 评论区等）都直接创建 PageElement
            return "default"


class SellingPointIndicator(AsyncNode):

    async def prep_async(self, shared):
        from .utils import tag_to_llm_content, load_theme_coherence_prompt

        html_content = tag_to_llm_content(self.params["tag"])
        prompt = load_theme_coherence_prompt(html_content)
        return {
            "prompt": prompt,
            "llm_conf": self.params["llm_conf"],
        }

    async def exec_async(self, prep_res):
        from crawl2.clients import llm

        llm_output = await llm.call_llm(conf=prep_res["llm_conf"], prompt=prep_res["prompt"])
        match llm_output:
            case {"confidence": confidence}:
                try:
                    is_selling_point = float(confidence) > 0.6
                except Exception as e:
                    logger.error(f"failed with {llm_output} {e}")
                    is_selling_point = False
            case _:
                is_selling_point = False
        if is_selling_point:
            return "selling_point"
        else:
            return "product_features"

    async def post_async(self, shared, prep_res, exec_res):
        # exec_res is 'selling_point' or 'product_features'
        if exec_res == "product_features":
            self.params["element_tag"] = "商品功能列表"
        return exec_res


class SellingPointSummary(AsyncNode):
    async def prep_async(self, shared):
        from .utils import tag_to_llm_content, load_selling_point_summary_prompt

        html_content = tag_to_llm_content(self.params["tag"])
        page_content = self.params["tag"].get_text(strip=True)
        prompt = load_selling_point_summary_prompt(html_content)
        return {
            "domain": self.params["domain"],
            "prompt": prompt,
            "llm_conf": self.params["llm_conf"],
            "page_content": page_content,
        }

    async def exec_async(self, prep_res):
        from crawl2.db import operations
        from crawl2.clients import llm

        if await operations.exists_page_content_summary(domain=prep_res["domain"], content=prep_res["page_content"]):
            summary = await operations.get_page_content_summary(domain=prep_res["domain"],
                                                                content=prep_res["page_content"])
        else:
            summary = await llm.call_llm(conf=prep_res["llm_conf"], prompt=prep_res["prompt"])
        return summary

    async def post_async(self, shared, prep_res, exec_res):
        if exec_res:
            self.params["selling_point_summary"] = exec_res
            self.params["element_tag"] = "商品卖点"
            return "default"
        else:
            return "end"


class ToMainImagePageElement(AsyncBatchNode):
    """主图需要根据不同的 pattern 选择不同的策略处理"""

    async def prep_async(self, shared):
        from lxml import etree
        from crawl2 import schema
        from .utils import ProductGalleryPartitioner, norm_img_src, extract_xpath, extract_bounding_box

        tag = self.params["tag"]
        dom = etree.HTML(str(self.params["html"]))
        partitioner = ProductGalleryPartitioner(area_ratio_threshold=4, percentile=70, ratio_tolerance=0.3)

        # 策略 1: 主图区域为主图 + 缩略图
        main_node, thumb_node = partitioner.partition(tag)
        strategy_1_matched = main_node is not None

        if strategy_1_matched:
            img_nodes = main_node.find_all("img")

        # 策略 2: 主图区域为主图
        elif partitioner.is_same_size_group(tag):
            img_nodes = tag.find_all("img")

        # 未匹配
        else:
            img_nodes = []

        # img node 转换为 PageElement
        # 将图片进行预处理，按照 img.src 进行分组编号
        image_xpath_map = {}
        image_index_map = {}
        for img in img_nodes:
            if not set(img.attrs).intersection(["src", "srcset"]):
                continue
            if "srcset" in img.attrs:
                src = img.attrs["srcset"].split(",")[0]
            else:
                src = img.attrs["src"]
            main_image_url = norm_img_src(src)
            try:
                image_xpath_map.setdefault(main_image_url, [])
                for xpath in extract_xpath(img):
                    # 一个 xpath 匹配到多个 dom 被任务是无效的 xpath
                    if len(dom.xpath(xpath)) > 1:
                        continue
                    image_xpath_map[main_image_url].append(xpath)
                if main_image_url not in image_index_map:
                    image_index_map[main_image_url] = len(image_index_map) + 1
            except:
                pass

        image_page_elements = []
        try:
            bounding_box = extract_bounding_box(self.params["tag"])
        except ValueError:
            return image_page_elements

        # 将相同 img.src 的图片作为一个主图实体
        for main_image_url, selectors in image_xpath_map.items():
            selectors = list(set(selectors))
            if not selectors:
                continue
            page_element = schema.PageElementEditable(
                selectors=selectors,
                data=schema.PageElementData(
                    element_tag=self.params["element_tag"],
                    product_id=self.params["product_id"],
                    main_image_url=main_image_url,
                    main_image_index=image_index_map[main_image_url],
                    selling_point_summary=None,
                    # 使用匹配的 DOM 的 bounding box 而不是每个 img DOM 的 bounding box
                    bounding_box=bounding_box
                )
            )
            image_page_elements.append(page_element)
        return image_page_elements

    async def post_async(self, shared, prep_res, exec_res):
        shared.setdefault("page_elements", []).extend(prep_res)
        return "default"


class ToPageElement(AsyncNode):
    async def prep_async(self, shared):
        from lxml import etree
        from crawl2 import schema
        from .utils import extract_bounding_box, extract_xpath

        dom = etree.HTML(str(self.params["html"]))
        selectors = []
        try:
            for selector in set(extract_xpath(self.params["tag"])):
                # 一个 xpath 匹配到多个 dom 被任务是无效的 xpath
                if len(dom.xpath(selector)) > 1:
                    continue
                selectors.append(selector)
            if not selectors:
                return None
        except ValueError:
            return None

        try:
            bounding_box = extract_bounding_box(self.params["tag"])
        except ValueError:
            return None

        return schema.PageElementEditable(
            selectors=selectors,
            data=schema.PageElementData(
                element_tag=self.params["element_tag"],
                product_id=self.params["product_id"],
                main_image_url=None,
                main_image_index=None,
                selling_point_summary=self.params.get("selling_point_summary"),
                bounding_box=bounding_box,
            )
        )

    async def post_async(self, shared, prep_res, exec_res):
        if prep_res:
            shared.setdefault("page_elements", []).append(prep_res)
        return "default"


class NeedRecursive(AsyncNode):
    async def post_async(self, shared, prep_res, exec_res):
        return "continue"


class Start(AsyncNode):
    """Node that does nothing, used to properly start the branch flow."""


class End(AsyncNode):
    """Node that does nothing, used to properly end the flow."""


class ShopifyPageStatus(AsyncNode):
    """检查 Shopify 页面是否已初始化"""

    async def prep_async(self, shared):
        return self.params["page_url"]

    async def exec_async(self, page_url):
        if await operations.exists_page(page_url):
            page = await operations.get_page(page_url)
            if page.crawl_status == models.CrawlStatus.SUCCESS:
                return "inited"
            else:
                return "not init"
        else:
            return "not init"


class UpdateShopifyPageCrawlStatus(AsyncNode):
    """将 Shopify 页面标记为已完成初始化"""

    async def prep_async(self, shared):
        return {
            "page_url": shared["page_url"],
            "product_id": shared["product_id"],
            "html": shared["html"],
            "screenshot": shared["screenshot"],
        }

    async def exec_async(self, prep_res):
        cos_client = cos.CosClient.get_client()
        cos_client.upload_page_html(prep_res["page_url"], prep_res["html"])
        cos_client.upload_page_screenshot(prep_res["page_url"], prep_res["screenshot"])
        await operations.update_page_data(page_url=prep_res["page_url"], product_id=prep_res["product_id"])
        await operations.update_page_crawl_status(prep_res["page_url"], models.CrawlStatus.SUCCESS)


class ExistsShopifyPageElements(AsyncNode):
    async def prep_async(self, shared):
        return self.params["page_url"]

    async def exec_async(self, prep_res):
        if await operations.exists_page_element_by_page_url(prep_res):
            return "inited"
        else:
            return "not init"


class ClearShopifyPageElements(AsyncNode):
    async def prep_async(self, shared):
        return shared["page_url"]

    async def exec_async(self, prep_res):
        await operations.clear_page_elements(prep_res)


class SaveShopifyPageElementToDB(AsyncNode):
    """将识别结果保存到数据库中"""

    async def prep_async(self, shared):
        return {
            "page_url": shared["page_url"],
            "page_elements": shared["page_elements"],
        }

    async def exec_async(self, prep_res):
        for page_element in prep_res["page_elements"]:
            await operations.create_page_element(prep_res["page_url"], page_element)


class CrawlAndDetectionBridge(AsyncNode):
    """html 为 crawl page flow 的产出，需要由 bridge node 将 shared 数据转换为 params 传入后续流程"""

    async def prep_async(self, shared):
        self.params["html"] = shared["html"]
        self.params["tag"] = BeautifulSoup(shared["html"], "html.parser").body

    async def post_async(self, shared, prep_res, exec_res):
        shared.setdefault("page_elements", [])
        shared.setdefault("debug", [])
