from io import BytesIO

import pandas as pd
from fastapi import APIRouter, HTTPException, Query
from starlette.responses import StreamingResponse

from crawl2 import schema
from crawl2.db import operations
from crawl2.db.ops import product as product_ops
from crawl2.web.i18n import i18n

router = APIRouter(prefix="/sites", tags=["selling_points"])


@router.get("/{domain}/selling_points.excel")
async def download_selling_points(domain: str):
    """下载站点的所有产品卖点数据为 Excel 文件"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    # 获取所有产品及其卖点
    selling_points = await operations.list_domain_selling_points(domain)

    # 准备数据
    data = []
    for sp in selling_points:
        for (marketing_copy, marketing_copy_chn) in zip(sp.selling_point_marketing_copies or [],
                                                        sp.selling_point_marketing_copies_chn or []):
            data.append({
                i18n('selling_point_id'): sp.id,
                i18n('product_url'): sp.product_url,
                i18n('product_id'): sp.product_id,
                i18n('product_title'): sp.product_title,
                i18n('product_price'): sp.product_price,
                i18n('product_type'): sp.product_type,
                i18n('selling_point_name'): sp.selling_point_name,
                i18n('selling_point_name_chn'): sp.selling_point_name_chn or '',
                i18n('selling_point_value'): sp.selling_point_value or '',
                i18n('selling_point_value_chn'): sp.selling_point_value_chn or '',
                i18n('selling_point_marketing_copies'): marketing_copy,
                i18n('selling_point_marketing_copies_chn'): marketing_copy_chn,
                i18n('status'): sp.status.value,
                i18n('last_edited_at'): sp.last_edited_at.isoformat() if sp.last_edited_at else '',
                i18n('version_id'): sp.version_id or '',
            })

    # 创建 DataFrame
    df = pd.DataFrame(data)

    # 创建 Excel 文件
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Selling Points')

    output.seek(0)

    # 返回文件
    return StreamingResponse(
        output,
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers={
            'Content-Disposition': f'attachment; filename="{domain}_selling_points.xlsx"'
        }
    )


@router.get("/{domain}/selling_points")
async def list_domain_selling_points(
    domain: str,
    page: int = Query(1, ge=1, description="页码，从1开始"),
    size: int = Query(50, ge=1, le=2000, description="每页数量，最大2000")
) -> schema.PaginatedResponse[schema.ProductMetaAndSellingPoint]:
    """
    获取指定域名的所有产品卖点信息，支持分页。
    """
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    return await operations.list_domain_selling_points_paginated(domain, page, size)


@router.put("/{domain}/selling_points/{selling_point_id}")
async def update_selling_point_with_version(
    domain: str,
    selling_point_id: int,
    update_request: schema.SellingPointUpdateRequest
):
    """更新卖点数据并创建版本记录"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    success = await operations.update_selling_point_with_version(
        selling_point_id, update_request
    )

    if not success:
        raise HTTPException(status_code=404, detail="Selling point not found")

    return {"message": "Selling point updated successfully with version tracking"}


@router.get("/{domain}/selling_points/{selling_point_id}/versions")
async def get_selling_point_versions(
    domain: str,
    selling_point_id: int
) -> list[schema.DataVersion]:
    """获取卖点的版本历史"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    return await operations.get_selling_point_versions(selling_point_id)


@router.get("/{domain}/selling_points/edit_stats")
async def get_selling_point_edit_stats(domain: str)-> schema.EditStatusStats:
    """根据状态筛选卖点"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    return await operations.get_selling_point_edit_stats(domain)


@router.get("/{domain}/selling_points/status/{status}")
async def list_selling_points_by_status(
    domain: str,
    status: schema.SellingPointStatus
) -> list[schema.ProductMetaAndSellingPoint]:
    """根据状态筛选卖点"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    return await operations.list_selling_points_by_status(domain, status)


@router.get("/{domain}/selling_points/status")
async def list_all_selling_points_by_status(
    domain: str
) -> list[schema.ProductMetaAndSellingPoint]:
    """获取所有卖点（按状态分组）"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    return await operations.list_selling_points_by_status(domain, None)


@router.get("/{domain}/products/{product_id}/selling_points")
async def get_product_selling_points(
    domain: str,
    product_id: str
) -> list[schema.ProductMetaAndSellingPoint]:
    """获取指定产品的卖点信息"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    # 获取产品元数据
    product = await operations.get_product_metadata_by_product_id(domain, product_id)
    if not product:
        raise HTTPException(status_code=404, detail=f"Product {product_id} not found")

    # 获取卖点信息
    return await product_ops.get_product_selling_points_by_url(product.url)
