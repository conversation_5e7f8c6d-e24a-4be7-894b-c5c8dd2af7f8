from fastapi import APIRouter, HTTPException
from loguru import logger

from crawl2 import schema, utils, workflows
from crawl2.db import operations


router = APIRouter(prefix="/sites", tags=["sites"])


@router.get("")
async def list_sites() -> list[schema.SiteBrief]:
    """获取所有站点列表"""
    return await operations.list_site_briefs()


@router.get("/{domain}")
async def get_site_details(domain: str) -> schema.SiteDetails:
    """获取站点详情"""
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    products = await operations.list_product_metadata(domain)
    products_pydantic = [
        schema.ProductMetaData(
            url=product.url,
            product_id=product.product_id,
            title=product.title,
            product_type=product.product_type,
            price=product.price,
            tags=product.tags,
            vendor=product.vendor,
        )
        for product in products
    ]
    aspects = await operations.list_all_expanded_product_type_aspects(domain)
    concerns = await operations.list_all_expanded_product_type_concerns(domain)

    return schema.SiteDetails(
        domain=site.domain,
        shopify_domain=site.shopify_domain,
        crawl_status=site.crawl_status,
        products=products_pydantic,
        product_type_aspects=aspects,
        product_type_concerns=concerns,
    )


@router.post("")
async def create_site(req: schema.SiteCreateRequest) -> schema.SiteBrief:
    """创建新站点并启动数据挖掘流程, 如果站点已存在则执行重新爬取流程"""
    # 从 URL 中提取域名
    domain = utils.extract_domain_from_shopify_site_url(req.site_url)
    with logger.contextualize(domain=domain):
        # 检查站点是否已存在
        existing_site = await operations.get_site(domain)
        if existing_site:
            logger.info(f"站点 {domain} 已存在, 开始重新爬取流程")
            workflow_name = f'recrawl_site({domain})'
        else:
            # 创建站点记录
            workflow_name = f'create_site({domain})'
            await operations.init_site_metadata(domain)
            logger.info(f"已创建新站点 {domain} 的元数据记录")

        # 如果指定了爬取策略，则设置策略
        if req.crawl_strategy:
            await operations.set_site_crawl_strategy(
                domain,
                req.crawl_strategy,
                "创建站点时指定的初始策略"
            )
            logger.info(f"已为站点 {domain} 重置爬取策略 {req.crawl_strategy}")

        # 创建并启动数据挖掘任务链
        task_chain = workflows.create_crawl_site_workflow(domain, shopify_knowledge_task_id=req.task_id)
        await workflows.trigger_workflow(workflow_name, task_chain)

        return await operations.get_site_brief(domain)


@router.put("/{domain}/crawl")
async def re_crawl_site(domain: str, req: schema.ReCrawlSiteRequest | None = None) -> schema.SiteBrief:
    """重新爬取站点数据"""
    # 检查站点是否存在
    site = await operations.get_site_brief(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    # 如果指定了新的爬取策略，则更新策略
    if req and req.crawl_strategy:
        await operations.set_site_crawl_strategy(
            domain,
            req.crawl_strategy,
            "重新爬取时更新策略"
        )
        logger.info(f"已为站点 {domain} 更新爬取策略")

    # 创建并启动数据挖掘任务链
    task_chain = workflows.create_crawl_site_workflow(domain, begin_with=req and req.begin_with)
    await workflows.trigger_workflow('re_crawl_site', task_chain)
    return site


@router.put("/{domain}/sync")
async def re_sync_site(domain: str) -> schema.SiteBrief:
    """重新同步指定站点已挖掘数据"""
    # 检查站点是否存在
    site = await operations.get_site_brief(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    # 创建并启动数据挖掘任务链
    task_chain = workflows.create_knowledge_sync_workflow(domain)
    await workflows.trigger_workflow('re_sync_site', task_chain)
    return site


@router.get("/{domain}/crawl_strategy")
async def get_site_crawl_strategy(domain: str) -> schema.SiteCrawlStrategyResponse:
    """获取站点的挖掘策略"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    strategy = await operations.get_site_crawl_strategy(domain)
    is_using_default = strategy is None

    return schema.SiteCrawlStrategyResponse(
        domain=domain,
        strategy=strategy,
        strategy_updated_at=site.strategy_updated_at,
        is_using_default=is_using_default
    )


@router.post("/{domain}/crawl_strategy")
async def set_site_crawl_strategy(domain: str,
                                  req: schema.SiteCrawlStrategyUpdateRequest) -> schema.SiteCrawlStrategyResponse:
    """设置站点的挖掘策略"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    # 设置策略
    await operations.set_site_crawl_strategy(domain, req.strategy, req.comment)

    # 返回更新后的策略
    updated_site = await operations.get_site(domain)
    return schema.SiteCrawlStrategyResponse(
        domain=domain,
        strategy=req.strategy,
        strategy_updated_at=updated_site.strategy_updated_at,
        is_using_default=False
    )


@router.delete("/{domain}/crawl_strategy")
async def delete_site_crawl_strategy(domain: str) -> schema.SiteCrawlStrategyResponse:
    """删除站点的挖掘策略，恢复为默认策略"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    # 删除策略
    await operations.delete_site_crawl_strategy(domain)

    # 返回更新后的状态
    updated_site = await operations.get_site(domain)
    return schema.SiteCrawlStrategyResponse(
        domain=domain,
        strategy=None,
        strategy_updated_at=updated_site.strategy_updated_at,
        is_using_default=True
    )


@router.get("/{domain}/crawl_strategy/history")
async def get_site_crawl_strategy_history(domain: str) -> schema.SiteCrawlStrategyHistoryResponse:
    """获取站点挖掘策略的历史版本"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    history = await operations.get_site_crawl_strategy_history(domain)

    return schema.SiteCrawlStrategyHistoryResponse(
        domain=domain,
        history=history
    )
