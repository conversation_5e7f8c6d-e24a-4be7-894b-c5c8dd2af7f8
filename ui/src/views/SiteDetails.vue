<script setup>
import { ref, onMounted, provide } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { BadgeCheck, BadgeQuestionMark, Download, TvMinimalPlay, History } from 'lucide-vue-next'
import { api } from '@/api/api.js'
import { Button } from '@/components/ui/button'
import ProductTypeAspects from '../components/product/ProductTypeAspects.vue'
import ProductTypeConcerns from '../components/product/ProductTypeConcerns.vue'
import ProductList from '../components/product/ProductList.vue'
import Navigation from '../components/common/Navigation.vue'

const route = useRoute()
const router = useRouter()
const site = ref(null)
const loading = ref(true)
const error = ref(null)
const activeTab = ref('products')

const fetchSiteDetails = async () => {
  try {
    loading.value = true
    const response = await api.get(`/api/v1/sites/${route.params.domain}`)
    site.value = response.data
  } catch (err) {
    error.value = err.message
    console.error('Error fetching site details:', err)
  } finally {
    loading.value = false
  }
}

// 提供刷新数据的回调函数
provide('refreshData', fetchSiteDetails)

const downloadSellingPoints = async () => {
  try {
    const response = await api.get(`/api/v1/sites/${route.params.domain}/selling_points.excel`, {
      responseType: 'blob'
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `${route.params.domain}_selling_points.xlsx`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Error downloading selling points:', error)
  }
}

onMounted(() => {
  fetchSiteDetails()
})
</script>

<template>
  <div class="p-8 max-w-[1200px] mx-auto pt-0">
    <div v-if="loading" class="text-center p-8 text-gray-600">
      Loading site details...
    </div>
    <div v-else-if="error" class="text-center p-8 text-red-500">
      Error: {{ error }}
    </div>
    <div v-else-if="site" class="flex flex-col gap-8">
      <Navigation 
        :breadcrumbs="[
          { text: 'Sites', path: '/' },
          { text: 'Site Details' }
        ]"
      >
        <template #right>
          <div class="flex items-center gap-2">
            <RouterLink :to="`/task-runs?domain=${encodeURIComponent(route.params.domain)}`" v-slot="{ navigate }">
              <Button @click="navigate"><History />查看任务历史</Button>
            </RouterLink>

            <RouterLink :to="`/sites/${route.params.domain}/session-replay`" v-slot="{ navigate }">
              <Button @click="navigate"><TvMinimalPlay />Session Replay</Button>
            </RouterLink>
          </div>
        </template>
      </Navigation>

      <div class="flex justify-between items-center mb-8">
        <h1 class="m-0 text-gray-800">{{ site.domain }}</h1>
      </div>

      <div class="bg-white rounded-lg shadow overflow-hidden min-w-[800px]">
        <div class="flex bg-gray-50 border-b border-gray-200 p-2 gap-2">
          <button 
            v-for="tab in ['products', 'aspects', 'concerns']" 
            :key="tab"
            :class="{ 'bg-white text-gray-800 shadow-sm': activeTab === tab, 'bg-transparent text-gray-700 hover:bg-gray-100': activeTab !== tab }"
            class="p-2 px-4 border-none cursor-pointer rounded font-medium transition-all duration-200 flex items-center gap-2"
            @click="activeTab = tab"
          >
            {{ tab.charAt(0).toUpperCase() + tab.slice(1) }}
            <span v-if="tab === 'products'" class="text-sm text-gray-500">({{ site.products.length }})</span>
          </button>
        </div>
        
        <div class="p-6 min-h-[400px] w-full min-w-[800px]">
          <!-- Products Tab -->
          <div v-if="activeTab === 'products'" class="w-full max-w-full min-w-[800px]">
            <ProductList 
              :products="site.products || []"
              :domain="site.domain"
            />
          </div>

          <!-- Aspects Tab -->
          <div v-if="activeTab === 'aspects'" class="w-full max-w-full min-w-[800px]">
            <div class="flex flex-col gap-6 w-full">
              <ProductTypeAspects 
                v-for="productType in [...new Set(site.products.map(p => p.product_type))]" 
                :key="productType"
                :productType="productType"
                :aspectList="site.product_type_aspects[productType] || []"
                :domain="site.domain"
              />
            </div>
          </div>

          <!-- Concerns Tab -->
          <div v-if="activeTab === 'concerns'" class="w-full max-w-full min-w-[800px]">
            <div class="flex flex-col gap-6 w-full">
              <ProductTypeConcerns 
                v-for="productType in [...new Set(site.products.map(p => p.product_type))]" 
                :key="productType"
                :productType="productType"
                :concernList="site.product_type_concerns[productType] || []"
                :domain="site.domain"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template> 