<template>
  <div class="w-full">
    <div v-if="loading" class="text-center p-4 text-gray-600">
      Loading concerns...
    </div>
    <div v-else-if="error" class="text-center p-4 text-red-500">
      Error: {{ error }}
    </div>
    <div v-else-if="concerns && concerns.length > 0" class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-900">Product Concerns({{ concerns.length }})</h3>
      
      <div class="space-y-3">
        <div 
          v-for="concern in concerns" 
          :key="concern.id"
          class="bg-gray-50 rounded-lg p-4 border border-gray-200"
        >
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <h4 class="font-medium text-gray-900">Concern:</h4>
              <span 
                :class="{
                  'bg-blue-100 text-blue-800': concern.status === 'fresh',
                  'bg-yellow-100 text-yellow-800': concern.status === 'editing',
                  'bg-green-100 text-green-800': concern.status === 'ready',
                  'bg-gray-100 text-gray-800': concern.status === 'disabled',
                  'bg-red-100 text-red-800': concern.status === 'archived'
                }"
                class="px-2 py-1 text-xs font-medium rounded-full capitalize"
              >
                {{ concern.status }}
              </span>
            </div>
            
            <div>
              <p class="text-gray-700">{{ concern.concern || 'No concern description' }}</p>
              <p v-if="concern.concern_chn" class="text-gray-600 text-sm mt-1">{{ concern.concern_chn }}</p>
            </div>
            
            <div v-if="concern.marketing_copies && concern.marketing_copies.length > 0">
              <h5 class="font-medium text-gray-900">Marketing Copies:</h5>
              <div class="space-y-2">
                <div 
                  v-for="(copy, index) in concern.marketing_copies" 
                  :key="index"
                  class="bg-white rounded p-2 border border-gray-200"
                >
                  <p class="text-gray-700">{{ copy }}</p>
                  <p 
                    v-if="concern.marketing_copies_chn && concern.marketing_copies_chn[index]" 
                    class="text-gray-600 text-sm mt-1"
                  >
                    {{ concern.marketing_copies_chn[index] }}
                  </p>
                </div>
              </div>
            </div>
            
            <div v-if="concern.last_edited_at" class="text-xs text-gray-500 text-right">
              Last edited: {{ new Date(concern.last_edited_at).toLocaleString() }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="text-center p-8 text-gray-500">
      No concerns available for this product.
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useProduct } from '@/composables/useProduct.js'

const props = defineProps({
  domain: {
    type: String,
    required: true
  },
  productId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['dataCountChange'])

const { getProductConcerns } = useProduct()
const concerns = ref([])
const loading = ref(false)
const error = ref(null)

const fetchConcerns = async () => {
  try {
    loading.value = true
    error.value = null
    concerns.value = await getProductConcerns(props.domain, props.productId)
    
    // 发送数据量变化事件
    emit('dataCountChange', concerns.value.length)
  } catch (err) {
    error.value = err.message
    console.error('Error fetching product concerns:', err)
    emit('dataCountChange', 0)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchConcerns()
})

// 暴露刷新方法给父组件
defineExpose({
  refresh: fetchConcerns
})
</script>
