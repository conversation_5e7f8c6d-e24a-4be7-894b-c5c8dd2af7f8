from io import BytesIO

import pandas as pd
from fastapi import APIRouter, HTTPException
from starlette.responses import StreamingResponse

from crawl2 import schema
from crawl2.db import operations
from crawl2.db.ops import product as product_ops
from crawl2.web.i18n import i18n


router = APIRouter(prefix="/sites", tags=["faqs"])


@router.get("/{domain}/faqs")
async def list_domain_faqs(domain: str,
                           page: int = 1, size: int = 2000) -> schema.PaginatedResponse[schema.ProductMetaAndFaq]:
    """
    获取指定域名的所有产品FAQ信息（分页）。
    """
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")
    return await operations.list_domain_faqs_paginated(domain, page, size)


@router.get("/{domain}/faqs.excel")
async def download_faqs(domain: str):
    """下载站点的所有产品FAQ数据为 Excel 文件"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    # 获取所有产品及其FAQ
    faqs = await operations.list_domain_faqs(domain)

    # 准备数据
    data = []
    for faq_record in faqs:
        if faq_record.faqs:
            for faq_item in faq_record.faqs:
                data.append({
                    i18n('faq_id'): faq_record.id,
                    i18n('product_url'): faq_record.product_url,
                    i18n('product_id'): faq_record.product_id,
                    i18n('product_title'): faq_record.product_title,
                    i18n('product_price'): faq_record.product_price,
                    i18n('product_type'): faq_record.product_type,
                    i18n('question'): faq_item.question,
                    i18n('question_chn'): faq_item.question_chn or '',
                    i18n('answer'): faq_item.answer,
                    i18n('answer_chn'): faq_item.answer_chn or '',
                    i18n('status'): faq_record.status.value,
                    i18n('last_edited_at'): faq_record.last_edited_at.isoformat() if faq_record.last_edited_at else '',
                    i18n('version_id'): faq_record.version_id or '',
                })
        else:
            # 如果没有FAQ项，也要添加一行产品信息
            data.append({
                i18n('faq_id'): faq_record.id,
                i18n('product_url'): faq_record.product_url,
                i18n('product_id'): faq_record.product_id,
                i18n('product_title'): faq_record.product_title,
                i18n('product_price'): faq_record.product_price,
                i18n('product_type'): faq_record.product_type,
                i18n('question'): '',
                i18n('question_chn'): '',
                i18n('answer'): '',
                i18n('answer_chn'): '',
                i18n('status'): faq_record.status.value,
                i18n('last_edited_at'): faq_record.last_edited_at.isoformat() if faq_record.last_edited_at else '',
                i18n('version_id'): faq_record.version_id or '',
            })

    # 创建 DataFrame
    df = pd.DataFrame(data)

    # 创建 Excel 文件
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='FAQs')

    output.seek(0)

    # 返回文件
    return StreamingResponse(
        output,
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers={
            'Content-Disposition': f'attachment; filename="{domain}_faqs.xlsx"'
        }
    )


@router.put("/{domain}/faqs/{faq_id}")
async def update_faq_with_version(
    domain: str,
    faq_id: int,
    update_request: schema.FaqUpdateRequest
):
    """更新FAQ数据并创建版本记录"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    success = await operations.update_faq_with_version(
        faq_id, update_request
    )

    if not success:
        raise HTTPException(status_code=404, detail="FAQ not found")

    return {"message": "FAQ updated successfully with version tracking"}


@router.get("/{domain}/faqs/{faq_id}/versions")
async def get_faq_versions(
    domain: str,
    faq_id: int
) -> list[schema.DataVersion]:
    """获取FAQ的版本历史"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    return await operations.get_faq_versions(faq_id)


@router.get("/{domain}/faqs/edit_stats")
async def get_faqs_edit_stats(domain: str)-> schema.EditStatusStats:
    """根据状态筛选卖点"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    return await operations.get_faqs_edit_stats(domain)


@router.get("/{domain}/faqs/status/{status}")
async def list_faqs_by_status(
    domain: str,
    status: schema.SellingPointStatus
) -> list[schema.ProductMetaAndFaq]:
    """根据状态筛选FAQ"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    return await operations.list_faqs_by_status(domain, status)


@router.get("/{domain}/faqs/status")
async def list_all_faqs_by_status(
    domain: str
) -> list[schema.ProductMetaAndFaq]:
    """获取所有FAQ（按状态分组）"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    return await operations.list_faqs_by_status(domain, None)


@router.get("/{domain}/products/{product_id}/faqs")
async def get_product_faqs(
    domain: str,
    product_id: str
) -> schema.ProductMetaAndFaq | None:
    """获取指定产品的FAQ信息"""
    # 检查站点是否存在
    site = await operations.get_site(domain)
    if not site:
        raise HTTPException(status_code=404, detail=f"Site {domain} not found")

    # 获取产品元数据
    product = await operations.get_product_metadata_by_product_id(domain, product_id)
    if not product:
        raise HTTPException(status_code=404, detail=f"Product {product_id} not found")

    # 获取FAQ信息
    return await product_ops.get_product_faqs_by_url(product.url)
