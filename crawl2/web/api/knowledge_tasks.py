"""
这个模板定义的 API 接口，供 shopify-knowledge 调用，用于发起知识挖掘任务，注意和 celery_wrap.py 中提供的 celery task 相关 api 做区分。
"""
import tortoise.exceptions
from fastapi import APIRouter, HTTPException
from loguru import logger

from crawl2 import schema
from crawl2.db import operations
from crawl2 import workflows


router = APIRouter(prefix="/knowledge_tasks", tags=["health"])


@router.post("/parse_file")
async def parse_file(req: schema.ParseFileTaskRequest) -> schema.MessageOnlyResponse:
    """
    接口用于解析文件，file_url 是文件的 URL 地址。
    """
    with logger.contextualize(knowledge_task_id=req.task_id, store_domain=req.store_domain):
        try:
            site = await operations.get_site_by_myshopify_main(req.store_domain)
            if not site:
                raise HTTPException(status_code=404, detail="Site not found.")
            existed_task = await operations.get_parse_file_task(req.task_id)
            if existed_task:
                if any([existed_task.file_key != req.file_key,
                       existed_task.knowledge_type != req.knowledge_type,
                       existed_task.product_ids != req.product_ids]):
                    raise HTTPException(status_code=400,
                                        detail=f"Task ID already exists with different parameters: {existed_task}.")
                else:
                    celery_chain = workflows.create_file_parse_workflow(req.task_id, req.knowledge_type)
                    await workflows.trigger_workflow('parse_file', celery_chain)
                    return schema.MessageOnlyResponse(message="re-trigger parsed task successfully.")
            else:
                await operations.create_parse_file_task(req, site)
                celery_chain = workflows.create_file_parse_workflow(req.task_id, req.knowledge_type)
                await workflows.trigger_workflow('parse_file', celery_chain)
                return schema.MessageOnlyResponse(message="File parsing task created successfully.")
        except tortoise.exceptions.IntegrityError:
            raise HTTPException(status_code=409, detail="Task with this ID already exists.")
