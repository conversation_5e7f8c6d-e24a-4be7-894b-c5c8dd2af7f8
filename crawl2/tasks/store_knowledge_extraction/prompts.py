import string

SHOP_KNOWLEDGE_PROMPT_TEMPLATE = '''
# Task Description
You are an e-commerce information extraction assistant. Your task is to extract QA pairs from the provided documents that are helpful to customers in shopping. Requirements:

1. **Input Processing**:
    - Thoroughly read the document while ignoring:
        - Navigation links, URLs, and technical markup
   		- Shopping cart/payment details
   		- Private information (names, addresses, phone numbers)
   - Focus on buyer-centric content

2. **Output Format**:
    Strictly generate a JSON array with objects containing:
    - "topic": classification label (2-5 characters)
    - "question": Buyer's potential inquiry
    - "answer": Document-based response

3. **QA pairs Extraction**:
    - As long as the key dimensions that are helpful to buyers' shopping need to be extracted, such as:
        - Brand mission/positioning
        - Founding background/history
        - Core products & technologies
        - After-sales policies
        - Membership benefits
        - Site credibility/Authenticity guarantees
        - How to choose the right product/Shopping Guide
        - Main product categories
        - Other platform presence (Amazon/etc)
        
    - Some examples of questions:
        - Is this site trustworthy?
        - What is [site name]? 
        - Is it a trusted brand?？
        - What are your main product categories?
        - Is there a quality guarantee for your products?
        - Are these products authentic？
        - Have your products received any certifications or awards?
        - How long has your business been operating, and how many customers have purchased so far?
        - Do you also sell on major platforms like Amazon?
        - Do you offer price protection if prices drop after purchase?
        - What is your warranty policy for after-sales support?
        - What is your return or exchange policy?
        - How to choosing a [aspect category]?

4. **QA Generation Rules**:
    - Present knowledge as complete (question, answer) pairs
    - Ensure self-contained expressions:
        - Avoid pronouns ("they/this") in both Q&A
        - State exact dates for time-sensitive content (e.g., "May 1st-7th")
        - Preserve key technical terms (brand names/certifications)
    - One distinct topic per QA pair
    - Use more natural and personified questions, and make the answers as rich and comprehensive as possible.
    - Questions and answers must come only from the input document and should not introduce external information
    - Do NOT generate QA pairs for dimensions absent in document
    - The question must inherit the limiting conditions in the original text (such as gender, population, and usage scenario). It is forbidden to generate generalized questions (such as deleting key qualifiers such as "ladies" and "winter")
    - You need to be able to summarize. If a document is about how to select products, you need to summarize the characteristics of different products and generate QA pairs on how to select suitable products. such as:

        ```
         {{
            "topic": "Buying Guide",
            "question": "How to choose a women's warm winter coat?",
            "answer": "Consider these key factors:1. Warmth Performance: Prioritize heated coats with carbon fiber heating elements. Check filling materials - down for dry cold climates, synthetic fibers for humidity. 2. Usage Scenarios: Original Parka for extreme cold, lightweight puffer coats for daily commute, synthetic heated styles for humid regions. 3. Fabric Technology: Use waterproof/windproof materials like nylon with tape-sealed seams. 4. Fit: Opt for slightly loose cuts for layering, with elastic cuffs/hem to block wind. 5. Additional Features**: Detachable fur trim, USB charging ports, etc.\nTip: For heated coats, verify battery life (5-10 hours) and temperature settings (minimum 3 levels).
       }}
       ```
    - The references in the QA pair must be consistent and clear. If the question mentions a specific product, the answer should also include the reference of the question. Similarly, if the answer only refers to a specific product, the question must also indicate what product it is. The following example is a bad QA pair. The question is after-sales guarantee, but the answer is only for the two products BFWS2401 and BFWS2402. 

       ``` 
       {{
        "topic": "Product Guarantee",
        "question": "What after-sales service guarantees does Lefton Home provide?",
        "answer": "Lefton Home's BFWS2401 and BFWS2402 faucets feature ceramic disc valves known for their durability and long-lasting performance, ensuring the faucet will stay drip-free for years.",
       }}
       ``` 
    - QA pairs related to promotions or discounts are time-sensitive and should not be extracted. such as:

        ``` 
       {{
        "topic": "Time-Sensitive Offer",
        "question": "What is the current time-sensitive offer from Lefton?",
        "answer": "Lefton is offering a 15% discount with the code EA15 for Easter Sale.",
       }}
       ``` 


# Example Reference
- Input Document : 

``` 
[Store introduction text...]

``` 

- Output:

``` 
[
    {{
    "topic": "Brand Mission",
    "question": "What is ororo’s brand mission?",
    "answer": "To empower everyone to challenge the climate with functional yet fashionable apparel."
    }},
    {{
    "topic": "Product Guarantee",
    "question": "What after-sales service guarantees does ororo provide?",
    "answer": "We offer a 30-day no-questions-asked return guarantee and a 3-year warranty on the heating elements of all our products."
    }}
]
``` 

# Current Task
Process the following document (within triple quotes):

**document title**: {}

``` 
{} 
```

**Output in Json format, output a list, do not include any content that cannot be parsed by Json, do not include markdown syntax**

'''

CHECK_QA_QUALITY_PROMPT = """
## 任务
你是一个电商领域的专家大模型，擅长对知识点的质量进行评判。知识点由 QA 形式组成，请你结合我提供的不合适原因判断知识点是否合适（注意只关注给定的不适合原因）

## 输出格式
合适/不合适

## 不合适原因（请严格按照以下规则判断返回结果，如果不符合以下不合适原因，只返回合适不要做额外的判断）
- 回复中包含个人信息：A 回复涉及买家的姓名、联系方式、用户 ID 等隐私数据。（卖家的联系方式不符合此规则）
- 引导至其他资源：A 回复通过链接、非文本载体（如图像、二维码）引导至外部信息，未在正文直接呈现答案。
- 语言表达问题：A 回复存在影响理解的错别字、语法错误或语义歧义。
- 包含商品时效性属性信息：A 回复明确提到具有时效性的商品属性，（例如颜色，可能因库存变化，导致一些颜色没货，注意：具体的产品名称不是时效性属性）
- 未提供具体答案：A 回复未提供任何实质性信息或具体解答，（例如：details are not provided in the document.）

## 知识点
{}

## 输出, 注意：只输出合适或者不合适，不要带其他格式

"""

TAGGING_QAPAIR_LABEL_PROMPT = '''
## 任务
你是一个电商领域的专家大模型，擅长对知识点进行总结。我会提供给你一批标签列表以及一条问答形式的知识点，请你判断该知识点所属的标签类型

## 标签列表
- 标签列表由两部分组成：label和detailed_label。detailed_label是对label的补充说明，标签类型应该只从label中选择
{}

## 知识点
{}

## 判断规则
1. 输出的标签类型应该只来源于标签列表中的label
2. 如果当前知识点和标签列表中的label都不匹配，返回其他
3. 仅返回label，不要带其他格式

## 输出, 注意：只输出标签列表中存在的label，且只返回标签结果
'''

TAGGING_QAPAIR_DETAILED_LABEL_PROMPT = '''
## 任务
你是一个电商领域的专家大模型，擅长对知识点进行总结。我会提供给你一批标签定义以及一条问答形式的知识点，请你判断该知识点所属的标签类型，从已定义的标签列表中选择最符合的一项并直接输出标签结果:
xxx（标签类型）

## 标签列表，如果知识点没有在标签列表中，就返回其他
{}

## 知识点
{}


## 输出, 注意：只输出标签列表中存在的标签，且只返回标签结果
'''

QAPAIR_VALUE_PROMPT = '''
## 任务
你是一个电商领域的专家大模型，擅长总结知识。我会提供你一批关于【{}】的知识(知识以问答对的形式)，你需要对这些知识进行总结归纳，最终返回针对【{}】的知识值归纳

## 注意：
1. 归纳后的结果不应该有重复的内容，且内容尽可能精简全面
2. 归纳后的结果只应该从给定的知识总结，不应该参杂其他来源的知识
3. 归纳后的结果使用英文
4. 归纳后的结果会长期使用，不要包含具体的时间长度，例如“创立七年”
5. 归纳的知识应该是店铺通用知识，如果是跟产品相关的知识不需要归纳
6. 如果不同情况下的知识值不同，可以使用1、2、3结构化的格式归纳知识点，且知识值中要突出不同情况的说明
7. 需要以店铺自身的口吻归纳知识

## 知识：
{}


## 归纳后的结果（不要参杂其余格式）：

'''

QAPAIR_GUIDE_VALUE_PROMPT = '''
## 任务
你是一个电商领域的专家大模型，擅长生成导购话术。我会提供你一批关于【{}】的知识，你需要根据知识生成关于【{}】的导购话术

## 注意：
1. 导购话术不要过于冗长，尽可能精简全面，不要超过200字符
2. 导购话术只应该从给定的知识生成，不应该参杂其他来源的知识
3. 导购话术使用英文
4. 导购话术会长期使用，不要包含具体的时间长度，例如“创立七年”
5. 导购话术语气需要使用电商风格

## 知识：
{}


## 输出, 注意：只输出导购话术，不要参杂其余格式

'''

ANSWER_GENERATE_QUESTION_PROMPT = '''
## Role & Task
You are a seasoned e-commerce expert specializing in simulating authentic buyer questions across different shopping stages. Based on given Q&A pairs, generate consumer questions that are semantically equivalent but diversely phrased to the original question, ensuring all generated questions can be fully answered by the original response.

## Generation Rules
1. Language Requirements:
   - Colloquial expressions (e.g.: "Does this have...", "Is free shipping available?")
   - Incorporate typical interrogative patterns (how, whether, which, why, what models)
   - Avoid technical terms (use "charger" instead of "power adapter")
   - The questions generated are in English
2. Content Control:
   - Each question must contain explicit information points covered in the answer
   - Strictly prohibit introducing new information not covered in the original answer (e.g., fabricated specifications)
   - Preserve key qualifiers (e.g., "wireless charging", "Type-C port")
3. Diversity Requirements: Ensure varied phrasing without repetition while maintaining focus
4. Natural Language: Use conversational expressions and avoid jargon
5. Quantity Requirement: Generate 5 distinct questions

## Input Q&A Pair
{}

## Output Format
{{"question_list": ["Question1", "Question2", "Question3", "Question4", "Question5"]}}
// Strict Compliance: 1. Pure JSON object 2. No Markdown 3. No explanatory comments
'''


class CustomTemplate(string.Template):
    delimiter = "$$"


COLLECTION_KNOWLEDGE_EXTRA_PROMPT = CustomTemplate("""
## 任务
从页面的**公共段落**中抽取知识，排除所有商品段落（含商品名称、价格、链接、参数的段落），包括**通用知识**和**特定商品的功能描述**。通用知识指适用于多个商品的共享信息(例如FAQs)，排除单个商品独有的信息；**特定商品的功能描述**的内容总必须包含商品型号，且需确保内容为功能/特性描述（非纯参数）

## 知识结构
每个知识条目必须为 JSON 对象，包含以下字段：
- `topic`：知识分类（如 "Product Features", "FAQs"等）
- `title`：知识标题（必须直接复制页面中的原始文本,层级标题符号需要剥离(例如 ##),允许为空字符串）
- `content`：知识内容（必须直接复制页面中的原始文本,**不允许为空字符串**）

## 抽取规则
- 标题行识别：
  • 标题需为能概括后续内容主题的句子或短语（如 "选购指南"、"功能特点"），而非具体细节描述。
  • 优先选择包含"如何选择"、"特点"、"指南"等语义关键词的段落作为标题
  • 允许将以`#`开头的行作为标题候选，但需结合语义判断（如`## 1. 加热区域`可能为内容而非标题）
  • 排除包含商品型号、价格、链接的标题行（如 "## BF2204 Product Details" 视为商品段落）
  • 自动识别**无格式标记的问答结构**（即使没有Q:、A:标记)，自动归类为 "FAQs" 主题, Q对应标题,A对应内容,且都不能是空字符串
- 标题 - 内容关联：
  • 标题对应**后续所有与该主题相关的连续段落**，直至出现语义主题转换或商品段落。
  • 若标题后接分点说明（如"1. 加热区域"、"2. 绝缘材料"），无论是否有格式符号，均自动聚合为同一知识条目的内容。
  • 无标题行的段落仅当包含商品型号且为功能描述时抽取（如 "BF2204 支持 360° 旋转"）
- 内容有效性判断：
  • 内容必须包含标题所概括的所有子主题段落，避免仅抽取部分内容。
  • 标题行与内容段落需存在逻辑关联（如标题为功能主题，内容为具体描述）

## 抽取注意
- **必须抽取**：
  • 通用知识  
  • **特定商品的功能描述**（内容中必须明确指明是哪一个商品）  
  • FAQs知识
- **必须排除**：
  • 所有导航元素（顶部/底部菜单、跳过链接、购物车状态）
  • 所有表单内容（登录/注册表单、字段标签、必填提示、条款同意文本）
  • 会员计划/注册推广内容
  • 促销广告和营销口号
  • 按钮文本、链接文本（如 [Join Now], [Learn More]）
  • 碎片化短语（<10个单词的孤立文本）
  • 购物流程说明（如 "Choosing a selection results..."）
  • 页面功能说明（如 "This is a carousel. Use Next and Previous buttons to navigate"）
  • JS 脚本代码、HTML 标签等非内容元素
  • HTML标签
- **实质性判断标准**：
  • 内容必须是完整句子
  • 不能是用户界面元素（表单、按钮、导航）
  • 不能是账户相关的文本（登录、注册、会员）
- **特别注意**：
  • 任何包含以下关键词的文本自动排除："sign in", "create account", "required field", "newsletter", "terms of service", "join now"
  • 当page_description不为空字符串时，page_title和page_description(完全剥离HTML标签保留纯文本)自动组成一个知识，page_title对应title,page_description对应content
- **文本处理**：严格保留原文格式，禁止修改

## 输出样例
 ```
[
    {
        "topic": "Product Features",
        "title": "feature title",
        "content": "feature content"
    },
    {
        "topic": "Product Description",
        "title": "page_title",
        "content": "page_description"
    },
    {   
        "topic": "FAQs",
        "title": "question",
        "content": "answer"
    },
    // ... other entries
]
```

## 页面信息
```
page_title: $${title}

page_description: $${description}

page_content:

$${markdown_content}
```

## 输出要求：
**仅返回JSON数组内容，如果没有任何知识吗，返回空list，不要加额外的解释**
""")
