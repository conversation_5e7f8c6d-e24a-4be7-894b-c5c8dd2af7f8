import asyncio
from typing import List, Dict, <PERSON>, <PERSON><PERSON>
from typing_extensions import override
from loguru import logger
from tqdm import tqdm
from qdrant_client.models import (
    Distance,
    VectorParams,
    SparseVectorParams,
    MultiVectorConfig,
    MultiVectorComparator,
)
from datetime import datetime, timezone
from crawl2.db import operations
from pydantic import BaseModel
from crawl2.qdrant_index.base_qdrant_indexer import BaseQdrantIndexer, IndexConfig


class StoreKnowledgePoint(BaseModel):
    point_id: str
    topic: str
    question: str
    answer: str
    store_domain: str
    source: str
    source_detail: str
    quality: int
    label: str
    detailed_label: str
    extra_questions: list[str]


async def load_all_store_knowledge_points(
    domain: str,
) -> Tuple[str, List[StoreKnowledgePoint]]:
    """从数据库中加载指定domain的store_knowledge_points"""
    # 延迟导入避免循环依赖
    from crawl2.tasks.store_knowledge_extraction.utils import convert_store_knowledge_to_store_knowledge_point

    store_knowledge_points = await operations.list_store_knowledge(domain)
    if not store_knowledge_points or len(store_knowledge_points) == 0:
        return None, []
    result = []
    for store_knowledge_point in store_knowledge_points:
        store_domain, point = await convert_store_knowledge_to_store_knowledge_point(
            store_knowledge_point
        )
        data = point.model_dump()
        data["store_domain"] = store_domain
        point_to_qdrant = StoreKnowledgePoint(**data)
        result.append(point_to_qdrant)
    return store_domain, result


class StoreKnowledgePointIndex(
    BaseQdrantIndexer[StoreKnowledgePoint]
):
    def __init__(self, config: IndexConfig):
        super().__init__(config)

    @override
    async def generate_vectors(self, docs: List[StoreKnowledgePoint]) -> List[Dict[str, Any]]:
        q_vectors, a_vectors, q_sparse_vectors, a_sparse_vectors = await asyncio.gather(
            self.to_dense_embedding([doc.question for doc in docs]),
            self.to_dense_embedding([doc.answer for doc in docs]),
            self.to_sparse_embedding([doc.question for doc in docs]),
            self.to_sparse_embedding([doc.answer for doc in docs]),
        )

        extra_questions_vectors = await asyncio.gather(*[self.to_dense_embedding(doc.extra_questions) for doc in docs])

        res = []

        for q_vector, a_vector, q_sparse_vector, a_sparse_vector, extra_questions_vectors in zip(
                q_vectors,
                a_vectors,
                q_sparse_vectors,
                a_sparse_vectors,
                extra_questions_vectors
        ):
            if len(extra_questions_vectors) > 0:
                res.append(
                    {
                        "question-dense": q_vector,
                        "answer-dense": a_vector,
                        "extra-questions-dense": extra_questions_vectors,
                        "question-sparse": q_sparse_vector,
                        "answer-sparse": a_sparse_vector,
                    }
                )
            else:
                res.append(
                    {
                        "question-dense": q_vector,
                        "answer-dense": a_vector,
                        "question-sparse": q_sparse_vector,
                        "answer-sparse": a_sparse_vector,
                    }
                )
        return res

    @override
    def schema_to_payload(self, schema: StoreKnowledgePoint) -> Dict[str, Any]:
        now_iso = datetime.now(timezone.utc).isoformat()
        meta_dict = schema.model_dump()
        meta_dict["create_time"] = now_iso
        meta_dict["update_time"] = now_iso
        return meta_dict

    @override
    def get_id(self, schema: StoreKnowledgePoint) -> str:
        return schema.point_id

    @property
    @override
    def vectors_config(self) -> Dict[str, Any]:
        return {
            "question-dense": VectorParams(
                size=self.config.vector_size, distance=Distance.COSINE, on_disk=True
            ),
            "answer-dense": VectorParams(
                size=self.config.vector_size, distance=Distance.COSINE, on_disk=True
            ),
            "extra-questions-dense": VectorParams(
                size=self.config.vector_size,
                distance=Distance.COSINE,
                multivector_config=MultiVectorConfig(
                    comparator=MultiVectorComparator.MAX_SIM
                ),
                on_disk=True,
            ),
        }

    @property
    @override
    def sparse_vectors_config(self) -> Dict[str, Any]:
        return {
            "question-sparse": SparseVectorParams(),
            "answer-sparse": SparseVectorParams(),
        }

    @property
    @override
    def payload_indexes(self) -> Dict[str, str]:
        return {
            "topic": "keyword",
            "store_domain": "keyword",
            "source": "keyword",
            "source_detail": "keyword",
            "label": "keyword",
            "detailed_label": "keyword",
            "quality": "keyword"
        }

    async def import_all_knowledge_points(
        self,
        domain: str,
        batch_size: int = 10,
        force_recreate_collection: bool = False,
        delete_previous_shop_domain_data: bool = False
    ):
        logger.info(
            f"import domain {domain} store knowledge points to index for {self.config.collection_name}"
        )
        store_domain, documents = await load_all_store_knowledge_points(domain)
        if not store_domain or not documents or len(documents) == 0:
            logger.warning(f"No documents found for domain {domain}, skip")
            return
        await self.create_collection(force_recreate=force_recreate_collection)
        source_list = list(set([document.source for document in documents]))
        if delete_previous_shop_domain_data:
            await self.delete_store_domain_knowledge(store_domain, source_list=source_list)

        batches = [
            documents[i: i + batch_size] for i in range(0, len(documents), batch_size)
        ]
        for batch in tqdm(batches, desc="Importing store knowledge points"):
            await self.index_batch(batch)
        await self.check_document_count(store_domain, len(documents))
