def create_llm_format_html_flow():
    from pocketflow import AsyncFlow
    from crawl2.tasks.page_element import nodes
    from crawl2.tasks.page_element.flow.crawl_shopify_product_page import create_crawl_shopify_product_page_flow

    crawl_shopify_product_page_flow = create_crawl_shopify_product_page_flow()
    preprocess_html = nodes.PreprocessHTML()
    html_to_xml = nodes.HTMLToLLMFormat()

    crawl_shopify_product_page_flow >> preprocess_html >> html_to_xml

    class LLMFormatHTML(AsyncFlow): pass

    flow = LLMFormatHTML(start=crawl_shopify_product_page_flow)
    return flow
