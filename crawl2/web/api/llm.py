from fastapi import APIRouter
from openai import BaseModel

from crawl2.clients.llm import LLM_SERVICE_NAME, llm_providers, DOUBAO_DEEPSEEK, get_llm_sdk_and_concurrency_limit


router = APIRouter(prefix="/llm", tags=["llm"])


class LLMModels(BaseModel):
    provider_name: LLM_SERVICE_NAME
    models: list[str]


@router.get("/models")
async def list_available_llm_models() -> list[LLMModels]:
    """
    列出所有可用的 LLM 模型
    """
    res = []
    for provider_name, provider in llm_providers.items():
        if provider_name == 'doubao':
            res.append(LLMModels(
                provider_name=provider_name,
                models=[DOUBAO_DEEPSEEK]
            ))
        else:
            llm, _ = await get_llm_sdk_and_concurrency_limit(provider_name)
            resp = await llm.get('/models', cast_to=object)
            model_names = [model['id'] for model in resp['data'] if model['object'] == 'model']
            res.append(LLMModels(
                provider_name=provider_name,
                models=model_names
            ))
    return res
