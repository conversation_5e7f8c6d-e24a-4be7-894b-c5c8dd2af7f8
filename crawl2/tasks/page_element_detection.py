"""对 shopify 页面元素命名实体识别"""
import asyncio
import json
import textwrap
from copy import deepcopy
from typing import Callable
from uuid import uuid4
from urllib.parse import urlparse

import numpy as np
from bs4 import BeautifulSoup
from bs4 import Tag
from crawl4ai import CrawlerRunConfig, CacheMode
from jinja2 import Template
from loguru import logger
from lxml import etree

from crawl2 import schema
from crawl2 import utils
from crawl2.clients import browser, llm, http, shopify_knowledge, cos
from crawl2.db import operations, models
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync

llm_conf = llm.LLMConfig('usq', 'gpt-4o-mini', 0.4, 16384)


@celery_task
async def crawl_domain_raw_web_pages(domain: str, clear_existing: bool = False):
    """抓取 shopify 页面，保存 html, screenshot 到 oss"""
    site = await operations.get_site(domain)
    product_urls = site.product_urls
    await CeleryTaskTqdmAsync.gather(
        *[crawl_single_raw_web_page(url=url, clear_existing=clear_existing) for url in product_urls])


@celery_task
async def crawl_domain_page_elements(domain: str, clear_existing: bool = False):
    """分析 shopify 页面，并对页面元素进行商品实体类型标注"""
    pages = await operations.list_shopify_domain_pages(domain)

    semaphore = asyncio.Semaphore(1)  # 串行

    async def limited_task(coroutine, *args, **kwargs):
        async with semaphore:
            await coroutine(*args, **kwargs)

    await CeleryTaskTqdmAsync.gather(*[
        limited_task(crawl_page_elements, url=page.page_url, clear_existing=clear_existing)
        for page in pages
    ])


@celery_task
async def sync_page_elements_to_knowledge(domain: str):
    """将标注好的 shopify 页面发布到 knowledge 中，供业务侧使用"""
    knowledge_client = shopify_knowledge.ShopifyKnowledgeClient.get_client()
    site = await operations.get_site(domain)
    pages = await operations.list_shopify_domain_pages(domain)
    for page in pages:
        page_elements = await operations.list_page_elements(page.page_url)
        payload = shopify_knowledge.SyncPageElementsReq(
            storeDomain=site.shopify_domain,
            pageUrl=page.page_url,
            pageElements=[
                shopify_knowledge.SyncPageElementsItem(
                    target=page_element.target,
                    selectors=page_element.selectors,
                    data=page_element.data.model_dump()
                )
                for page_element in page_elements
            ]
        )
        await knowledge_client.sync_page_elements(payload)


@celery_task
async def dump_page_elements(domain: str, version: str):
    """将当前站点的商品实体类型标注结果保存到 oss 中作为 {version} 版本"""
    cos_client = cos.CosClient.get_client()
    pages = await operations.list_shopify_domain_pages(domain)
    dump_data = []
    for page in pages:
        page_dump = page.model_dump()
        page_elements = await operations.list_page_elements(page.page_url)
        page_dump["elements"] = [
            page_element.model_dump()
            for page_element in page_elements
        ]
        dump_data.append(page_dump)
    cos_client.dump_page_elements(domain=domain, dump_data=dump_data, version=version)


@celery_task
async def load_page_elements(domain: str, version: str):
    """加载 oss 中 {version} 版本的站点商品实体类型标注结果"""
    cos_client = cos.CosClient.get_client()
    dump_data = cos_client.load_page_elements(domain=domain, version=version)
    for page_dump in dump_data:
        elements_dump = page_dump.pop("elements")
        page = schema.ShopifyPage.model_validate(page_dump)
        if not await operations.exists_page(page.page_url):
            await operations.create_page(domain=domain, page_url=page.page_url)
        if product_id := page.data.get("product_id"):
            await operations.update_page_data(page.page_url, product_id=product_id)
        for element_dump in elements_dump:
            page_element = schema.PageElement.model_validate(element_dump)
            editable = schema.PageElementEditable(
                target=page_element.target,
                selectors=page_element.selectors,
                data=page_element.data
            )
            await operations.create_or_update_page_element(page.page_url, editable=editable)


def get_xpath_checker(html_content):
    dom = etree.HTML(str(html_content))

    def check_valid(selector):
        # 一个 xpath 匹配到多个 dom 被任务是无效的 xpath
        return len(dom.xpath(selector)) <= 1

    return check_valid


async def crawl_single_raw_web_page(url: str, clear_existing: bool = False):
    """使用无头浏览器抓取单个 shopify 页面的原始 html"""
    domain, path = utils.split_domain_and_path(url)
    page_url = domain + path
    if await operations.exists_page(page_url):
        page = await operations.get_page(page_url)
        if page.crawl_status == models.CrawlStatus.SUCCESS:
            if not clear_existing:
                logger.info(f"Page {url} already crawled successfully, skipping.")
                return
    else:
        if not await operations.get_site(domain):
            logger.error(f"Site not found for domain: {domain}")
            return
        await operations.create_page(domain, page_url)

    cos_client = cos.CosClient.get_client()
    response = await http.http_get(f"https://{page_url}.json")
    if response.status_code != 200:
        logger.error(f"Failed to fetch JSON for {page_url}: {response.status_code}")
        return
    page_metadata = response.json()
    if "product" in page_metadata and (product_id := page_metadata["product"].get("id")):
        await operations.update_page_data(page_url, product_id=product_id)
    crawl_config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        remove_overlay_elements=True,
        exclude_external_links=True,
        exclude_social_media_links=True,
        excluded_tags=["header", "footer", "nav", "iframe"],
        js_code=[
            MARK_HIDDEN_ELEMENT_JS,
            GET_ELEMENT_BOUND_BOX_JS,
        ],
        scan_full_page=True,
        screenshot=True,
        wait_for_images=True
    )
    try:
        async with browser.get_web_crawler("mobile") as crawler:
            result = await crawler.arun(url, config=crawl_config)
        cos_client.upload_page_html(page_url=page_url, html_content=result.html)
        if result.screenshot:
            cos_client.upload_page_screenshot(page_url=page_url, screenshot=result.screenshot)
    except Exception as e:
        logger.opt(exception=e).error(f"Crawl page failed for {url}: {str(e)}")
        return
    await operations.update_page_crawl_status(page_url, models.CrawlStatus.SUCCESS)


async def crawl_page_elements(url: str, clear_existing: bool = False):
    domain, path = utils.split_domain_and_path(url)
    page_url = domain + path
    if not await operations.exists_page(page_url):
        logger.error(f"Page not init for url: {url}")
        return
    page = await operations.get_page(page_url)
    if await operations.exists_page_element_by_page_url(page_url):
        if not clear_existing:
            logger.info(f"Page {url} already has elements, skipping.")
            return
        await operations.clear_page_elements(page_url)
    cos_client = cos.CosClient.get_client()
    html_content = cos_client.read_page_html(page_url=page_url)
    if html_content is None:
        logger.error(f"Page {url} has no HTML content, please crawl it first.")
        return
    soup = BeautifulSoup(html_content, "html.parser")
    xpath_checker = get_xpath_checker(str(soup))
    await page_element_ner(page, soup.find(id="MainContent") or soup.body, xpath_checker=xpath_checker)


async def page_element_ner(page: schema.ShopifyPage, node: Tag, xpath_checker: Callable[[str], bool]):
    """对页面元素进行命名实体识别，识别出页面元素的类型并创建 PageElement 实例。"""
    if not isinstance(node, Tag):
        return
    if not AttrHelper.is_node_need_process(node):
        return
    prompt = PromptHelper.provide_general_prompt_zh(node)
    llm_output = await llm.call_llm(llm_conf, prompt)
    match llm_output:
        # 识别到实体类型后，就不再继续递归处理子节点
        case {"entity": str() as entity}:
            if entity == "主图":
                await create_main_image_page_element(page, node, xpath_checker=xpath_checker)
            # 大模型处理商品卖点和商品功能列表会有问题，引入第二步判断
            elif entity in ("商品卖点", "商品功能列表"):
                await seller_point_ner(page=page, node=node, xpath_checker=xpath_checker)
            else:
                await create_page_element(page, node, entity, xpath_checker=xpath_checker)
            return
        case _:
            await asyncio.gather(*[
                page_element_ner(page, child, xpath_checker) for child in node.children if isinstance(child, Tag)
            ])


async def seller_point_ner(page: schema.ShopifyPage, node: Tag, xpath_checker: Callable[[str], bool]):
    """大模型处理商品卖点和商品功能列表会有问题，引入第二步判断"""
    if not isinstance(node, Tag):
        return
    if not AttrHelper.is_node_need_process(node):
        return
    prompt = PromptHelper.provide_one_theme_prompt_zh(node)
    llm_output = await llm.call_llm(llm_conf, prompt)
    match llm_output:
        case {"confidence": _}:
            try:
                is_seller_point = float(llm_output["confidence"]) > 0.6
            except Exception as e:
                logger.error(f"failed with {llm_output} {e}")
                is_seller_point = False
        case _:
            is_seller_point = False
    if is_seller_point:
        await create_seller_point_page_element(page, node, xpath_checker=xpath_checker)
    else:
        await create_page_element(page, node, "商品功能列表", xpath_checker=xpath_checker)


async def create_main_image_page_element(page: schema.ShopifyPage, node: Tag, xpath_checker: Callable[[str], bool]):
    """创建主图实体类型的 PageElement 实例。主图类型需要特殊处理，所以单独方法"""
    # 识别到的是主图容器，需要提取所有图片并为每个图片创建主图实体类型的 PageElement 实例

    partitioner = ProductGalleryPartitioner(area_ratio_threshold=4, percentile=70)
    main_node, thumb_node = partitioner.partition(node)
    if not main_node:
        if partitioner.is_same_size_group(node):
            main_node = node
        else:
            logger.warning("未识别的主图区域的主图")
            return

    try:
        image_xpath_map = {}
        image_index_map = {}
        for img in main_node.find_all("img"):
            if not set(img.attrs).intersection(["src", "srcset"]):
                continue
            if "srcset" in img.attrs:
                src = img.attrs["srcset"].split(",")[0]
            else:
                src = img.attrs["src"]
            if src.startswith("//"):
                src = f"https:{src}"
            parsed = urlparse(src)
            src = parsed.scheme + "://" + parsed.hostname + parsed.path
            src_path = parsed.path
            if not src_path:
                continue
            try:
                image_xpath_map.setdefault(src, [])
                for xpath in AttrHelper.extract_xpath(img):
                    if xpath_checker(xpath):
                        image_xpath_map[src].append(xpath)
                if src_path not in image_index_map:
                    image_index_map[src_path] = len(image_index_map) + 1
            except:
                pass

        # 相同连接的图片视为一个主图实体，并且保存所有的 xpath
        for src, xpath_list in image_xpath_map.items():
            xpath_list = list(set(xpath_list))
            if not xpath_list:
                continue
            main_image_index = image_index_map[urlparse(src).path]
            editable = schema.PageElementEditable(
                target=uuid4().hex,
                selectors=list(set(xpath_list)),
                data=schema.PageElementData(
                    element_tag="主图",
                    product_id=page.data.get("product_id"),
                    main_image_url=src,
                    main_image_index=main_image_index,
                    selling_point_summary=None,
                    bounding_box=AttrHelper.extract_bounding_box(node)
                )
            )
            # 更新或创建
            await operations.create_or_update_page_element(page.page_url, editable)
            logger.info("创建 PageElement 主图")
    except Exception as e:
        logger.error(f"创建 PageElement 主图失败 {e}")


async def create_seller_point_page_element(page: schema.ShopifyPage, node: Tag, xpath_checker: Callable[[str], bool]):
    """创建商品卖点实体类型的 PageElement 实例。"""
    try:
        domain, _ = utils.split_domain_and_path(page.page_url)
        page_content = node.get_text(strip=True)
        if await operations.exists_page_content_summary(domain=domain, content=page_content):
            summary = await operations.get_page_content_summary(domain=domain, content=page_content)
        else:
            prompt = PromptHelper.provide_seller_point_summary_prompt_zh(node)
            summary = await llm.call_llm(llm_conf, prompt, parse_json=False)
            if summary is None:
                logger.info(f"创建 PageElement 商品卖点 失败，生成 summary 失败 {node}")
                return
            await operations.create_page_content_summary(domain=domain, content=page_content, summary=summary)
        update_data = schema.PageElementEditable(
            target=uuid4().hex,
            selectors=list(filter(xpath_checker, AttrHelper.extract_xpath(node))),
            data=schema.PageElementData(
                element_tag="商品卖点",
                product_id=page.data.get("product_id"),
                main_image_url=None,
                main_image_index=None,
                selling_point_summary=str(summary),
                bounding_box=AttrHelper.extract_bounding_box(node),
            )
        )
        if update_data.selectors:
            await operations.create_page_element(page_url=page.page_url, editable=update_data)
            logger.info(f"创建 PageElement 商品卖点 with summary: {summary}")
        else:
            logger.warning("无有效的 selectors ，创建 PageElement 商品卖点失败")
    except Exception as e:
        logger.error(f"创建 PageElement 商品卖点失败 {e}")


async def create_page_element(page: schema.ShopifyPage, node: Tag, element_tag: str, xpath_checker: Callable[[str], bool]):
    try:
        editable = schema.PageElementEditable(
            target=uuid4().hex,
            selectors=list(filter(xpath_checker, AttrHelper.extract_xpath(node))),
            data=schema.PageElementData(
                element_tag=element_tag,
                product_id=page.data.get("product_id"),
                main_image_url=None,
                main_image_index=None,
                selling_point_summary=None,
                bounding_box=AttrHelper.extract_bounding_box(node),
            )
        )
        if editable.selectors:
            await operations.create_page_element(page.page_url, editable)
            logger.info(f"创建 PageElement {element_tag}")
        else:
            logger.warning(f"无有效的 selectors ，创建 PageElement {element_tag} 失败")
    except Exception as e:
        logger.error(f"创建 PageElement 失败 {e}")


class PromptHelper:
    @classmethod
    def provide_general_prompt_zh(cls, node: Tag):
        return (
                cls.provide_general_instruction_zh()
                + cls.provide_general_example_zh()
                + cls.provide_general_context_zh(node)
                + cls.provide_general_output_format_zh()
        )

    @classmethod
    def provide_debug_prompt_zh(cls, node: Tag, expect):
        output_format = textwrap.dedent(f"预期返回的是 {expect} 类型，但是现在不满足，可以告诉我不满足 {expect} 类型的原因吗")
        return cls.provide_general_instruction_zh() + cls.provide_general_context_zh(node) + output_format


    @staticmethod
    def provide_general_instruction_zh():
        return textwrap.dedent("""\
        你是一个顶级的 Shopify 页面语义分析专家。你的唯一任务是识别给定的 HTML 代码片段所代表的核心功能区域。

        # 任务

        分析下方 `[输入]` 部分提供的 HTML 代码片段，并从 `[实体类型定义]` 列表中，找出最能 **完整且精确** 描述该代码片段 **整体用途** 的一个实体类型。

        # 规则

        1. **整体优先，先复合后单一**：首先，判断代码片段是否**作为一个整体**符合某个**复合型实体**（如`相关推荐列表`）的定义。这类实体天然包含多个子元素。如果整体不符合任何复合型实体的描述，再继续判断它是否符合某个**单一功能实体**（如`商品价格与折扣`）的定义。
        2. **双重参考基础**：匹配必须基于 DOM 结构（例如，标签层次结构、属性）和片段的文本内容。
        3. **严格置信要求与单一性原则**：仅当你绝对确信**整个片段**唯一且完整地、**且只**对应于 `[预定义实体类型]` 列表中**某一个单一实体类型**时，才返回该实体类型。
        4. **复合区域与模糊匹配处理**：如果代码片段的功能模糊、过于通用（例如，一个普通的 `<div>` 包裹着一些文本），或者它是一个包含多个独立预定义实体类型的**复合区域**（例如，一个片段同时包含“主图”、“商品价格与折扣”和“SKU区域”），导致无法将其**完整且精确地**归类为列表中的**单一**实体类型时，**必须返回 `null`**。严禁推断、猜测或匹配局部内容。
        5. **无推断/猜测**：不要将实体类型推广或推断到预定义列表之外，即使片段与之有松散关系。匹配必须是直接且明确的。如果代码片段的功能模糊、过于通用（例如，一个普通的 `<div>` 包裹着一些文本），或者不完全匹配任何一个实体定义，**必须返回 `null`**。严禁猜测或匹配局部内容。
        6. **整体优先原则**：你必须评估整个代码片段的功能。如果一个片段同时包含了价格、SKU 和“添加到购物车”按钮，它应该被识别为 **不匹配任何一个实体定义**，而不是 `商品价格与折扣`。

        # 预定义实体类型（**仅允许这些**）

        * 主图
        * 商品价格与折扣
        * SKU区域
        * 加购区域
        * 商品功能列表
        * 货品清单
        * 保修和退换政策
        * 商品卖点
        * 尺码表
        * FAQ列表
        * 相关推荐列表
        * 评论区

        ## 实体类型定义

        * `主图`: 应该是一个包含多个商品图片的容器，通常包含两个图片列表，用于展示大图和图片选择器。
        * `商品价格与折扣`: 显示商品的价格信息与折扣信息，通常为简单的数字加货币单位，如果有折扣信息，通常为一个百分比或金额的标识或两个价格的对比信息等。商品价格与折扣**应当且仅可以**包含价格信息，任何其它信息存在时都应该认为是商品区域，应当返回不匹配并由更小的区域进行匹配。
        * `SKU区域`: 一个包裹了**所有**商品可选规格（SKU）的容器区域。这个区域的核心功能是让用户选择商品的具体配置，如颜色、尺码、型号、材质、容量或是否添加配件等。
        * `加购区域`: 通常显示为商品的加购按钮和相关操作区域，不包含价格、SKU 等信息。通常包含一个“添加到购物车”按钮和可能的数量选择器。
        * `货品清单`: 通常以列表形式说明包装盒内包含哪些物品。通常会有 `what's included` 之类的关键词作为该区域的标题。
        * `保修和退换政策`: 一个用于展示商品相关的**客户保障服务承诺**的集合区域。其核心目的是通过一系列服务承诺来增强顾客的购买信心，打消其购物疑虑。这个区域不仅限于描述传统的保修 (warranty)、退货 (returns) 或换货 (exchange) 政策，**通常会打包展示多种类型的服务承诺**。
        * `商品卖点`: 一个卖点会有**小标题，正文，图片这样的结构**。
        * `商品功能列表`: 以列表形式介绍商品，内容为**纯文字**。
        * `尺码表`: 显示商品的尺码信息，通常为一个表格或列表，包含不同尺码的详细信息。通常服饰类的商品会提供尺码表信息，通常会有 `body size`, `chest`, `waist`, `hip` 和 `S/M/L/XL` 等关键词信息。
        * `FAQ列表`: 显示商品的常见问题解答，通常为一个列表或段落，包含用户可能提出的问题及其答案。通常包含关键词: `faq`, `frequently asked questions`, `q&a`。
        * `相关推荐列表`: 显示与当前商品相关的其他商品推荐，通常为一个列表或网格。相关推荐列表**无需满足复合区域与模糊匹配处理规则**，相关推荐列表通常包含小标题以及若干个商品相关的信息（主图、商品价格与折扣、SKU区域等）
        * `评论区`: 显示用户对商品的评论和评分，通常为一个列表或段落，包含用户的评论内容和评分信息。通常包含关键词: `reviews`, `customer feedback`, `ratings`。


        ### SKU区域关键识别规则
 
        1. 允许包含多个属性组: 一个 SKU区域 可以包含一个或多个独立的商品属性组。例如，一个区域内可能同时包含“型号”选项（如 '带温度显示'/'不带温度显示'）和“颜色”选项（如 '灰色'/'黑色'/'白色'）。这些属性组共同构成了完整的SKU选择区，应该将包含这些所有属性组的最外层容器标记为 SKU区域。
        2. 核心功能导向: 不应仅仅因为一个区域内包含了多种不同类型的选项（如型号、颜色），或者其 HTML 结构比较复杂、层次较深，就认为它是一个“复合区域”而拒绝标记。判断的关键在于，这个区域的整体核心功能是否是让用户完成商品的规格选择。
        3. 常见内容: 内部通常会包含明确的属性标题（如“Color:”、“Size:”、“型号”）和对应的可点击选项（按钮、色块、下拉菜单等）。
        
        ### 保修和退换政策关键识别规则
        
        1. **允许多种承诺并存**: 一个区域如果同时包含了保修承诺（如 2 Years Warranty）、退货政策（如 30-Day Risk-Free）以及物流服务保障（如 Free Shipping, Shipping Protection），那么这个**展示所有承诺的整体容器**就应该被标记为 `保修和退换政策`。不应因其内容多样化而判定为不匹配。
        2. **关注整体目的**: 判断的关键在于，这个区域的整体功能是否是为了向顾客提供“购物保障”或“无忧承诺”。标题中常出现的关键词，如 guarantee, worry-free, protection, our promise 等，是识别该区域的强有力信号。
        3. **忽略功能无关的辅助元素**: 在判断时，应忽略区域内包含的、但与核心信息展示无关的辅助性 HTML 元素。例如，用于实现某些后台功能而设置的 <textarea>, <input>, 或 <script> 等标签，不应影响对整个区域核心功能的判断。


        # 特别注意的 bad case

        * 如果你识别到某个区域为 `商品价格与折扣`，请重新审视整个区域的内容，确保其中不包含 `SKU区域` 或 `商品功能列表` 或 `商品卖点` 任一实体的内容。
        * 如果你识别到某个区域为 `商品功能列表` 时，请重新审视整个区域内容，如果只有简短的文字列表（通常只有6个左右的列表且每个列表项只有一句话），这个区域应该是 `商品功能列表`。否则就是`商品卖点`。

        ---

        """)  # noqa


    @classmethod
    def provide_one_theme_prompt_zh(cls, node: Tag):
        instruction = textwrap.dedent("""\
        你是一个顶级的 Shopify 页面语义分析专家。你的唯一任务是识别给定的 HTML 代码片段所代表的核心功能区域。

        # 任务\n
        分析下方 `[输入]` 部分提供的 HTML 代码片段描述的是一个主题还是多个主题，提供这段代码是 “一个主题” 的置信度。

        ---

        """)
        example = textwrap.dedent("""\
        # 示例
        **示例 1**  
        [输入]
        ```html
        <ul><li>Mossy Oak patterned</li><li><span style="background-color:transparent;color:#000">large heating area</span></li><li><span style="background-color:transparent;color:#000">all-around warmth for cold hands</span></li><li><span style="background-color:transparent;color:#000">easy-to-adjust waist strap</span></li><li><span style="background-color:transparent;color:#000">flexible ribbed cuffs</span></li><li><span style="background-color:transparent;color:#000">functional storage</span></li><li><span style="background-color:transparent;color:#000">ultra-soft fleece-lined insulation</span></li><li><span style="background-color:transparent;color:#000">water-resistant fabric</span></li><li><span style="background-color:transparent;color:#000">machine washable</span></li></ul>\n
        ```
        
        [输出]
        ```json
        {"confidence": 0.2}
        ```
        
        **示例 2**
        [输入]
        ```html
        <div class="right-item heating-item"><div class="heating-content" style="min-height:unset"><h3 class="heating-title title-16 font-bold" role="heading" aria-label="Battery Performance" title="Battery Performance">Battery Performance</h3><div class="heating-desc text-16"><ul><li>Uniquely powered by a 7.4V output, the battery provides rapid preheating capabilities that surpass other batteries using lower voltages such as 5V or 3.7V. This ensures a swift and efficient warm-up, allowing you to experience comforting heat almost instantly.</li><li><span style="color:#232323">Type-C port for battery charging and a USB port for other device charging</span></li><li><span style="color:#232323">Fully charged in 4 hours (with a</span><a href="/products/2021-new-5v3a-charger-for-mini-5k-battery" tabindex="0">5V/3A charger</a><span style="color:#232323">)</span></li><li><span style="color:#232323">UL, CUL, CE, FCC, and RoHS certified for safety and quality assurance</span></li><li><span style="color:#232323">Capacity: 4800mAh/7.4V/35.4Wh</span></li><li><span style="color:#232323">Weight: 6.53 oz (185 g)</span></li></ul></div></div><div class="heating-image"><div class="global-image-settings"><img src="//www.ororowear.ca/cdn/shop/files/B20M-UGW.jpg?v=1708658453&amp;width=900" alt="" title="" loading="lazy" width="750" height="600" data-src="//www.ororowear.ca/cdn/shop/files/B20M-UGW.jpg?v=1708658453&amp;width=900" class="lazyloaded"></div></div></div>
        ```
        
        [输出]
        ```json
        {"confidence": 0.9}
        ```

        ---

        """)  # noqa

        user_input = textwrap.dedent(f"""\
        [输入]
        ```html
        {cls.preprocess_node(node)}
        ```
        
        [输出]
        """)
        return instruction + example + user_input

    @staticmethod
    def provide_general_example_zh():
        return textwrap.dedent("""\
        # 示例
        ---
        **示例 1: 完全匹配**
        [输入]

        ```html
        <div id="shopify-product-reviews" class="spr-container"><div class="spr-header"><h2 class="spr-header-title">Customer Reviews</h2><div class="spr-summary"><span class="spr-starrating">★★★★☆</span><span>Based on 4 reviews</span></div></div><div class="spr-content">...</div></div>
        ```

        [输出]

        ```json
        {"entity": "评论区"}

        ```

        **示例 2: 忽略匹配中关键词，但是不包含内容的 tab 标签**
        [输入]

        ```html
        <li role="tab" aria-selected="false" aria-controls="you-may-also-like"><a href="#you-may-also-like" data-target-id="you-may-also-like" class="stb__item" title="You May Also Like" aria-label="You May Also Like" tabindex="0">You May Also Like</a></li>
        ```

        [输出]
        ```json
        {"entity": null}
        ```

        **示例 3:  整体优先原则 - 你必须评估整个代码片段的功能。如果一个片段同时包含了价格、SKU 和“添加到购物车”按钮，它应该被识别为不匹配任何一个实体定义，而不是 `商品价格与折扣`**
        [输入]

        ```html
        <section id="shopify-section-template--23557366907243__main"><product-info id="MainProduct-template--23557366907243__main"><div><div><div><div><div><h2>Women's Heated Softshell Vest - Black / Gray</h2></div></div><media-gallery id="MediaGallery-template--23557366907243__main"><p>This is a carousel of product gallery. Use Next and Previous buttons to navigate, or jump to a specific slide with the thumbnails.</p><div aria-hidden="true" id="GalleryStatus-template--23557366907243__main">Image 1 is now available in gallery view</div><slider-component id="GalleryThumbnails-template--23557366907243__main"></slider-component><slider-component id="GalleryViewer-template--23557366907243__main"><a tabindex="0">Skip to product information</a><div><span id="NoMediaStandardBadge--7802714030251">25% OFF</span></div></slider-component></media-gallery></div><div><section id="ProductInfo-template--23557366907243__main"><nav><ul id="custom-breadcrumb"><li><a>Home</a></li><li><a>Women</a></li></ul></nav><div><h2 title="Women's Heated Softshell Vest - Black / Gray">Women's Heated Softshell Vest - Black / Gray</h2></div><div id="price-template--23557366907243__main"><div><div><div><span>Regular price</span><span>¥25,086 JPY</span></div><div><span>Sale price</span><span>¥25,086 JPY</span><span>Regular price</span><span><s>¥25,086 JPY</s></span></div><small><span>Unit price</span><span><span></span><span aria-hidden="true">/</span><span> per </span><span></span></span></small></div></div><div><div>Now<span>¥18814.5</span></div></div><div>Save<span>¥6271</span></div></div><div></div><div></div><div><div><h3 title="Highlights"><span>Highlights</span><span>-</span></h3><div><ul><li><span style="background-color:transparent;color:#000">regular fit</span></li><li><span style="background-color:transparent;color:#000">embrace the freedom of movement with the stretchy and lightweight softshell, designed to be wind- &amp; water-resistant.</span></li><li><span style="color:#000">lower back heating plus 3 additional heating zones: left &amp; right hand pockets, upper back</span></li><li><span style="background-color:transparent;color:#000">up to 8 hours of runtime</span></li><li><span style="background-color:transparent;color:#000">machine washable</span></li></ul></div></div><div><h3 title="Warranty &amp; Return"><button aria-controls="panel-warranty" aria-expanded="true"><span>Warranty &amp; Return</span><span>-</span></button></h3><div id="panel-warranty"><ul><li><strong>3-Year Limited Warranty</strong>on heating elements</li><li>1-Year Limited Warranty on battery</li><li><strong>30-Day</strong>No Hassle Return/Exchange</li></ul><div><ul><li>Insiders get free shipping $99+<modal-opener><button tabindex="0" type="button"><label><span>Sign in</span></label></button></modal-opener></li></ul></div></div></div><div><h3 title="What's Included"><button aria-controls="panel-include" aria-expanded="false"><span>What's Included</span><span>+</span></button></h3><div id="panel-include"><ul><li><span>Women's Heated Softshell Vest</span></li><li><span>Mini 5K Rechargeable Lithium-Ion Battery (4800 mAh, 7.4V)</span></li><li><span>Battery Charging Cable</span></li><li><span>User Manual (English, German, French)</span></li></ul></div></div><div><h3 title="Extra Savings on Backup Battery"><button><span>Extra Savings on Backup Battery</span><span>-</span></button></h3><div id="panel-bundle"><div><div><div><a tabindex="0" title="Rechargeable Mini 5K Battery"><span><div><img title="Rechargeable Mini 5K Battery" width="1000"></div></span></a></div><div><h3 title="Rechargeable Mini 5K Battery">Rechargeable Mini 5K Battery</h3><div><div><span>Current price:</span><span>¥82</span><span>Original price:</span><span>¥110</span></div></div></div></div></div></div></div></div></section></div></div></div></product-info></section>
        ```

        [输出]
        ```json
        {"entity": null}
        ```

        ---

        """)  # noqa

    @classmethod
    def provide_general_context_zh(cls, node: Tag):
        return Template(textwrap.dedent("""\
        [输入]

        ```html
        {{ html }}
        ```


        """)).render(html=cls.preprocess_node(node))


    @staticmethod
    def preprocess_node(node: Tag):
        node = deepcopy(node)
        for tag in node.find_all(["script", "link", "style", "svg", "path", "iframe"]):
            tag.decompose()
        for tag in node.find_all(attrs={HIDDEN_MARK_ATTRIBUTE: HIDDEN_MARK_VALUE}):
            tag.decompose()
        for tag in node.find_all(True):
            allow = ["id", "class", "title"]
            for attr in tag.attrs:
                if attr.startswith("aria-"):
                    allow.append(attr)
            tag.attrs = {k: v for k, v in tag.attrs.items() if k in allow}
        return "".join(line.strip() for line in node.prettify().split("\n"))

    @staticmethod
    def provide_general_output_format_zh():
        return textwrap.dedent("""\
        仅返回匹配的 `预定义实体类型` 并以指定的 **JSON** 格式返回，在 JSON 中的 `entity` 字段填写匹配的 `预定义实体类型`, 
        在 JSON 中的 `confidence` 字段填写置信度。如果没有匹配的 `预定义实体类型` 请返回 {"entity":null, "confidence": 0.92}.
        [输出]
        """)  # noqa


    @classmethod
    def provide_seller_point_summary_prompt_zh(cls, node: Tag):
        return textwrap.dedent(f"""\
        # 任务

        这是一个商品卖点的 HTML 代码片段，你需要提取出商品的卖点信息，并将其以简洁的方式总结成一句极简短描述。 尽量选用 title 或者 header 原文。尽量保持简洁且保持辨识度。

        # 规则
        1. 仅返回商品卖点的总结文本，不需要其他任何内容。保持原文的语言**不要做翻译**。
        2. 保证总结文本内容为**完整的单词或句子**
        
        # 示例

        **示例 1**

        [输入]
        ```html
        <product-heating-system class="product-heating-system section-template--23557366907243__section_product_heating_system_THix3e np-section" id="heating-system" role="tabpanel"><div class="page-width product-heating-system-wrapper"><h2 aria-label="Heating System" class="np-section-title title-32 font-bold" role="heading" title="Heating System">Heating System</h2><div class="heating-system-container"><div class="left-item heating-item"><div class="heating-content" style="min-height:unset"><h3 aria-label="Heating Performance" class="heating-title title-16 font-bold" role="heading" title="Heating Performance">Heating Performance</h3><div class="heating-desc text-16"><ul><li><span style="background-color:transparent;color:#000">Enjoy efficient warmth with advanced Carbon Fiber Heating Elements. </span></li><li><span style="background-color:transparent;color:#000">Four heating zones: Left &amp; Right Hand Pockets, Upper Back, Lower Back</span></li><li><span style="background-color:transparent;color:#000">Three adjustable heating settings (high, medium, low) </span></li><li><span style="background-color:transparent;color:#000">Up to 8 working hours (3 hrs on high heating setting, 5 hrs on medium, 8 hrs on low)</span></li><li><span style="background-color:transparent;color:#000">Heat quickly in seconds with 7.4V Mini 5K battery</span></li></ul></div></div><div class="heating-image"><div class="icongif-videoplays" role="button" tabindex="0"><svg aria-hidden="true" aria-label="Pause video" aria-labelledby="newpause-icon-title-gif-play" class="icon" height="32" p-id="10166" role="img" t="1713405533408" version="1.1" viewbox="0 0 1024 1024" width="32" xmlns="http://www.w3.org/2000/svg"></svg></div><video class="gif-video" muted="trun" playsinline="" webkit-playsinline="">Your browser does not support the video tag. Video content:</video><noscript><iframe autoplay="" controls="" frameborder="0" loop="" muted="" playsinline="" src="//www.ororowear.ca/cdn/shop/videos/c/vp/2feae1a120604e9389c0f4b0dc3822d7/2feae1a120604e9389c0f4b0dc3822d7.SD-480p-0.9Mbps-37386913.mp4?v=0" type="video/mp4" webkit-playsinlinec="" width="100%"></iframe></noscript></div></div><div class="right-item heating-item"><div class="heating-content" style="min-height:unset"><h3 aria-label="Battery Performance" class="heating-title title-16 font-bold" role="heading" title="Battery Performance">Battery Performance</h3><div class="heating-desc text-16"><ul><li>7.4V output for fast preheating</li><li>Powered by LG battery cells</li><li>Type-C port for charging and USB port for device charging</li><li>Fully charges in 4 hours with a 5V/3A charger</li><li>UL, CUL, CE, FCC, and RoHS certified for safety and quality</li><li>Capacity: 4800mAh/7.4V/35.4Wh</li><li>Weight: 6.53 oz (185 g)</li></ul></div></div><div class="heating-image"><div class="global-image-settings"><img alt="" class="ls-is-cached lazyloaded" data-src="//www.ororowear.ca/cdn/shop/files/B20M_750x600_7691e9a5-e57f-4e18-9e1b-11cf9929472c.jpg?v=1723799490&amp;width=900" height="1771" loading="lazy" src="//www.ororowear.ca/cdn/shop/files/B20M_750x600_7691e9a5-e57f-4e18-9e1b-11cf9929472c.jpg?v=1723799490&amp;width=900" title="" width="2217"></div></div></div></div></div></product-heating-system>
        ```

        [输出]
        Heating System

        --- 

        [输入]
        ```html
        {cls.preprocess_node(node)}
        ```

        [输出]
        """)  # noqa


class ProductGalleryPartitioner:
    """
    通过分析 DOM 节点内图片的渲染尺寸，将一个商品主图模块的 HTML
    分割为主图展示区域和图片列表选择区域。

    依赖于在 HTML 元素上预先绑定的 'data-shop-genius-bounding-box' 属性，
    该属性包含了从浏览器 computedStyle 中获取的 width 和 height。
    """

    def __init__(self, area_ratio_threshold: float = 4.0, percentile: int = 70):
        """
        初始化分区器。

        Args:
            area_ratio_threshold (float): 面积比率阈值。当最大面积与最小面积的比率超过此值时，
                                          我们认为找到了分裂点。例如，4.0 意味着大图区域的图片
                                          面积至少是小图区域的4倍。
            percentile (int): 用于计算代表性面积的百分位数值。70 (P70) 是一个很好的选择，
                              可以忽略离群的较小值。
        """
        if not (0 < percentile <= 100):
            raise ValueError("Percentile must be between 0 and 100.")
        self.AREA_RATIO_THRESHOLD = area_ratio_threshold
        self.PERCENTILE = percentile

    def _get_bounding_box_area(self, element: Tag) -> float:
        """从元素的 data 属性中解析 width 和 height，并计算面积。"""
        try:
            bounding_box = AttrHelper.extract_bounding_box(element)
        except ValueError:
            return 0.0
        return float(bounding_box["width"]) * float(bounding_box["height"])

    def _get_image_areas_in_node(self, node: Tag) -> list[float]:
        """查找给定节点下的所有图片，并返回它们的有效面积列表。"""
        if not isinstance(node, Tag):
            return []
        imgs = node.find_all('img')
        areas = [self._get_bounding_box_area(img) for img in imgs]
        # 只返回大于0的面积，过滤掉不可见或无尺寸的图片
        return [area for area in areas if area > 0]

    def _find_split_node(self, current_node: Tag) -> tuple[Tag | None, Tag | None]:
        """
        递归地查找分裂节点。
        分裂节点是主图区域和缩略图区域的最近共同父节点。
        """
        if not isinstance(current_node, Tag):
            return None, None

        children_stats = []
        # 1. 分析所有直属子节点
        for child in current_node.children:
            if not isinstance(child, Tag):
                continue

            image_areas = self._get_image_areas_in_node(child)

            if image_areas:
                # 2. 计算每个包含图片的子节点的代表性面积 (P70)
                representative_area = np.percentile(image_areas, self.PERCENTILE)
                children_stats.append({
                    'node': child,
                    'area': representative_area
                })

        # 3. 判断当前节点是否为分裂点
        if len(children_stats) == 2:
            children_stats.sort(key=lambda x: x['area'])
            min_area_child = children_stats[0]
            max_area_child = children_stats[-1]

            # 如果最小面积为0，则无法计算比率，跳过
            if min_area_child['area'] > 0:
                ratio = max_area_child['area'] / min_area_child['area']
                # 如果面积比率超过阈值，则认为找到了分裂点
                if ratio >= self.AREA_RATIO_THRESHOLD:
                    # 大的是主图区域，小的是缩略图区域
                    print(f"   -> Main area P{self.PERCENTILE} size: {max_area_child['area']:.0f}")
                    print(f"   -> Thumb area P{self.PERCENTILE} size: {min_area_child['area']:.0f}")
                    print(f"   -> Ratio: {ratio:.2f} >= {self.AREA_RATIO_THRESHOLD}")
                    return max_area_child['node'], min_area_child['node']

        # 4. 如果当前不是分裂点，则向下一层递归
        # print(f"-> No split at <{current_node.name}>, recursing deeper...")
        for child in current_node.children:
            if isinstance(child, Tag):
                main_area, thumb_area = self._find_split_node(child)
                # 如果在更深的层级找到了，就将结果向上传递
                if main_area is not None and thumb_area is not None:
                    return main_area, thumb_area

        return None, None

    def partition(self, html_content: str | Tag) -> tuple[Tag | None, Tag | None]:
        """
        执行分区的主方法。

        Args:
            html_content (str | Tag): 包含了绑定好 bounding box 信息的 HTML 字符串。

        Returns:
            tuple[Tag | None, Tag | None]: 一个元组，第一个元素是主图展示区域的
                                           BeautifulSoup Tag，第二个是图片列表选择区域的
                                           Tag。如果找不到，则为 None。
        """
        if isinstance(html_content, str):
            soup = BeautifulSoup(html_content, 'html.parser')
        elif isinstance(html_content, Tag):
            soup = deepcopy(html_content)
        else:
            raise TypeError()

        main_area_tree, thumb_area_tree = self._find_split_node(soup)

        return main_area_tree, thumb_area_tree

    def is_same_size_group(self, html_content: str | Tag, ratio_tolerance: float = 0.3) -> bool:
        """
        执行区域检查，如果区域内图片大小基本一致（在 ratio_tolerance 内）
        """
        if isinstance(html_content, str):
            soup = BeautifulSoup(html_content, 'html.parser')
        elif isinstance(html_content, Tag):
            soup = deepcopy(html_content)
        else:
            raise TypeError()

        imgs = soup.find_all('img')
        # 只返回大于0的面积，过滤掉不可见或无尺寸的图片
        areas = [area for img in imgs if (area := self._get_bounding_box_area(img)) > 0]
        if not areas:
            return False
        if len(areas) == 1:
            return True
        ratio = max(areas) / min(areas) if min(areas) > 0 else float("inf")
        return ratio <= (1 + ratio_tolerance)


class AttrHelper:
    @staticmethod
    def is_node_need_process(node: Tag):
        """DOM 节点是否需要被处理"""
        # 根据自定义的 visibility-status 标记判断节点是否是不可见元素
        if node.attrs.get(HIDDEN_MARK_ATTRIBUTE) == HIDDEN_MARK_VALUE:
            return False
        # 根据 tag 确定是否需要被处理
        if node.name.lower() in ["script", "link", "style", "svg", "path", "iframe"]:
            return False
        return True

    @staticmethod
    def extract_bounding_box(element: Tag) -> dict:
        """根据自定义的 bounding-box 标记获取元素坐标信息"""
        if BOUNDING_BOX_ATTRIBUTE not in element.attrs:
            raise ValueError(f"缺少 {BOUNDING_BOX_ATTRIBUTE} 属性, element {element}")
        return json.loads(element.attrs[BOUNDING_BOX_ATTRIBUTE])

    @staticmethod
    def extract_xpath(element: Tag):
        """根据自定义的 xpath 标记获取元素的 xpath 信息"""
        if XPATH_ATTRIBUTE not in element.attrs:
            raise ValueError(f"缺少 {XPATH_ATTRIBUTE} 属性, element {element}")
        xpath_list = json.loads(element.attrs[XPATH_ATTRIBUTE])
        assert isinstance(xpath_list, list)
        return xpath_list


HIDDEN_MARK_ATTRIBUTE = "data-shop-genius-visibility-status"
HIDDEN_MARK_VALUE = "hidden_or_tiny"
MARK_HIDDEN_ELEMENT_JS = """
(function() {
    const MIN_DIMENSION = 2;
    const MARK_ATTRIBUTE = 'data-shop-genius-visibility-status';
    const MARK_VALUE = 'hidden_or_tiny';

    /**
     * Checks if an element is truly visible and has a meaningful size.
     * @param {Element} element The DOM element to check.
     * @returns {boolean} True if the element is visible and large enough, false otherwise.
     */
    function isElementVisibleAndSized(element) {
        if (!element.parentElement) {
            // Document or detached elements, or elements that are part of the root
            // console.log(`Skipping detached or root element: ${element.tagName}`);
            return false;
        }

        // Get computed styles for accurate visibility checks
        const computedStyle = window.getComputedStyle(element);

        // Check for common visibility properties
        if (computedStyle.display === 'none' ||
            computedStyle.visibility === 'hidden' ||
            parseFloat(computedStyle.opacity) === 0) {
            return false;
        }

        // Check for dimensions using getBoundingClientRect
        // This gives the size and position relative to the viewport
        const rect = element.getBoundingClientRect();

        // If width or height is less than MIN_DIMENSION, consider it tiny
        if (rect.width < MIN_DIMENSION || rect.height < MIN_DIMENSION) {
            return false;
        }

        // Check if the element is outside the viewport
        // This is a basic check; elements can be off-screen and still "visible" in some contexts.
        // For strict visibility, you might also check if it's within scrollable areas.
        if (rect.right < 0 || rect.bottom < 0 ||
            rect.left > window.innerWidth || rect.top > window.innerHeight) {
            // Exclude elements that are clearly off-screen, unless they have non-zero dimensions
            // and might become visible via scrolling. For this script, we're focusing on
            // actively hidden or tiny elements, so off-screen elements without active dimensions
            // are considered not effectively visible.
            if (rect.width === 0 && rect.height === 0) {
                return false;
            }
        }

        // Check if the element has no actual rendered content and is just a container with no size
        // This can happen with empty divs or spans without explicit dimensions
        if (element.children.length === 0 && element.textContent.trim() === '' && rect.width === 0 && rect.height === 0) {
            return false;
        }

        return true;
    }

    // Get all elements in the document
    const allElements = document.querySelectorAll('*');

    // Iterate through elements and mark them
    allElements.forEach(element => {
        // Skip script and style tags as they are not visual DOM elements
        if (element.tagName === 'SCRIPT' || element.tagName === 'STYLE' || element.tagName === 'NOSCRIPT' || element.tagName === 'META' || element.tagName === 'LINK' || element.tagName === 'TITLE') {
            return;
        }

        // Exclude the HTML and BODY elements themselves, as they are document containers
        if (element === document.documentElement || element === document.body) {
            return;
        }

        // Check if the element or any of its ancestors are already marked as hidden/tiny
        // This helps avoid redundant checks and marking descendants of already hidden elements
        let isAncestorMarked = false;
        let currentParent = element.parentElement;
        while (currentParent) {
            if (currentParent.hasAttribute(MARK_ATTRIBUTE) && currentParent.getAttribute(MARK_ATTRIBUTE) === MARK_VALUE) {
                isAncestorMarked = true;
                break;
            }
            currentParent = currentParent.parentElement;
        }

        if (isAncestorMarked) {
            return; // Skip if a parent is already marked as hidden
        }

        if (!isElementVisibleAndSized(element)) {
            element.setAttribute(MARK_ATTRIBUTE, MARK_VALUE);
            // Optional: console.log(`Marked element: ${element.tagName} ${element.id ? '#' + element.id : ''} ${element.className ? '.' + element.className : ''}`);
        }
    });

    // Optional: Log a message when the script completes
    // console.log('Invisible/tiny element marking script completed.');
})();
"""  # noqa: E501

XPATH_ATTRIBUTE = "data-shop-genius-xpath"
BOUNDING_BOX_ATTRIBUTE = "data-shop-genius-bounding-box"
GET_ELEMENT_BOUND_BOX_JS = r"""
(function collectAllBoundingBoxes() {
    /**
     * 为给定的 DOM 元素生成一组健壮的 XPath 定位符。
     * 列表按从最稳定到最不稳定的顺序排列。
     *
     * @param {Element} element 需要为其生成 XPath 的 DOM 元素。
     * @returns {string[]} 一个包含 XPath 字符串的数组。
     */
    function generateXPaths(element) {
        // --- 1. 输入验证 ---
        if (!(element instanceof Element)) {
            console.error("输入无效：请提供一个有效的 DOM 元素。");
            return [];
        }

        // --- 2. 内部辅助函数的声明 ---

        /**
         * 替换原有的 Step class，使用普通对象。
         * @param {string} value - XPath 步骤的值。
         * @param {boolean} optimized - 是否为优化过的步骤。
         * @returns {{value: string, optimized: boolean}}
         */
        const createStep = (value, optimized) => {
            return {
                value: value, optimized: optimized || false,
            };
        };

        /**
         * 比较两个节点是否在 XPath 中被视为相似（例如，具有相同的标签名）。
         */
        const areNodesSimilar = (left, right) => {
            if (left === right) {
                return true;
            }

            if (left.nodeType === Node.ELEMENT_NODE && right.nodeType === Node.ELEMENT_NODE) {
                return left.localName === right.localName;
            }

            if (left.nodeType === right.nodeType) {
                return true;
            }

            // XPath 将 CDATA 视为文本节点。
            const leftType = left.nodeType === Node.CDATA_SECTION_NODE ? Node.TEXT_NODE : left.nodeType;
            const rightType = right.nodeType === Node.CDATA_SECTION_NODE ? Node.TEXT_NODE : right.nodeType;
            return leftType === rightType;
        };

        /**
         * 计算节点在其同级中的 XPath 索引。
         * 返回 -1 表示错误，0 表示没有相似的同级节点，否则返回 XPath 索引（从1开始）。
         */
        const xPathIndex = (node) => {
            const siblings = node.parentNode ? node.parentNode.children : null;
            if (!siblings) {
                return 0; // 根节点没有同级。
            }

            let hasSameNamedElements = false;
            for (let i = 0; i < siblings.length; ++i) {
                if (areNodesSimilar(node, siblings[i]) && siblings[i] !== node) {
                    hasSameNamedElements = true;
                    break;
                }
            }
            if (!hasSameNamedElements) {
                return 0;
            }

            let ownIndex = 1; // XPath 索引从 1 开始。
            for (let i = 0; i < siblings.length; ++i) {
                if (areNodesSimilar(node, siblings[i])) {
                    if (siblings[i] === node) {
                        return ownIndex;
                    }
                    ++ownIndex;
                }
            }
            return -1; // 错误：在父节点的子节点中未找到当前节点。
        };

        /**
         * 为单个节点计算 XPath 值（一个步骤）。
         */
        const xPathValue = (node, optimized) => {
            let ownValue;
            const ownIndex = xPathIndex(node);
            if (ownIndex === -1) {
                return null; // 错误。
            }

            switch (node.nodeType) {
                case Node.ELEMENT_NODE:
                    if (optimized && node.getAttribute("id")) {
                        return createStep('//*[@id="' + node.getAttribute("id") + '"]', true);
                    }
                    ownValue = node.localName;
                    break;
                case Node.ATTRIBUTE_NODE:
                    ownValue = "@" + node.nodeName;
                    break;
                case Node.TEXT_NODE:
                case Node.CDATA_SECTION_NODE:
                    ownValue = "text()";
                    break;
                case Node.PROCESSING_INSTRUCTION_NODE:
                    ownValue = "processing-instruction()";
                    break;
                case Node.COMMENT_NODE:
                    ownValue = "comment()";
                    break;
                case Node.DOCUMENT_NODE:
                    ownValue = "";
                    break;
                default:
                    ownValue = "";
                    break;
            }

            if (ownIndex > 0) {
                ownValue += "[" + ownIndex + "]";
            }

            return createStep(ownValue, node.nodeType === Node.DOCUMENT_NODE);
        };

        /**
         * 生成 Chrome DevTools 风格的 XPath。
         */
        const chromeGetXPath = (node, optimized) => {
            if (node.nodeType === Node.DOCUMENT_NODE) {
                return "/";
            }

            const steps = [];
            let contextNode = node;
            while (contextNode) {
                const step = xPathValue(contextNode, optimized);
                if (!step) {
                    break; // 发生错误，提前退出。
                }
                steps.push(step);
                if (step.optimized) {
                    break;
                }
                contextNode = contextNode.parentNode;
            }

            steps.reverse();
            // 使用 .map(s => s.value) 是因为我们现在用的是普通对象而非 class 实例
            const path = steps.map(s => s.value).join("/");
            return (steps.length && steps[0].optimized ? "" : "/") + path;
        };

        /**
         * 检查 XPath 是否唯一（只匹配一个元素）。
         */
        const isXPathUnique = (xpath) => {
            try {
                return (document.evaluate(`count(${xpath})`, document, null, XPathResult.NUMBER_TYPE, null).numberValue === 1);
            } catch (e) {
                return false; // 无效的 XPath。
            }
        };

        // 使用 Set 避免重复的 XPath
        const xpaths = new Set();
        const TAG_NAME = element.tagName.toLowerCase();

        /**
         * 如果候选 XPath 是唯一的，则添加它。
         */
        const addUniqueXPath = (xpath) => {
            if (isXPathUnique(xpath)) {
                xpaths.add(xpath);
            }
        };

        // --- 3. XPath 生成策略 ---

        // 策略 1: ID 和测试专用属性 (最高稳定性)
        if (element.id) {
            addUniqueXPath(`//${TAG_NAME}[@id='${element.id}']`);
        }
        // const testAttributes = ['data-testid', 'data-cy', 'data-test'];
        // for (const attr of testAttributes) {
        //   const value = element.getAttribute(attr);
        //   if (value) {
        //     addUniqueXPath(`//${TAG_NAME}[@${attr}='${value}']`);
        //   }
        // }

        // 策略 2: 基于锚点的 XPath 轴
        // a) 查找一个稳定的父锚点 (带ID)
        let parent = element.parentElement;
        while (parent && parent !== document.body) {
            if (parent.id) {
                const parentXPath = `//${parent.tagName.toLowerCase()}[@id='${parent.id}']`;
                const nameAttr = element.getAttribute("name");
                if (nameAttr) {
                    addUniqueXPath(`${parentXPath}//${TAG_NAME}[@name='${nameAttr}']`);
                }
                const classAttr = element.className;
                if (classAttr && typeof classAttr === 'string') { // 确保 className 是字符串
                    addUniqueXPath(`${parentXPath}//${TAG_NAME}[@class='${classAttr}']`);
                }
                break; // 找到第一个带锚点的父元素即可。
            }
            parent = parent.parentElement;
        }

        // b) 查找一个稳定的同级锚点 (如 <label>)
        const precedingLabel = element.previousElementSibling;
        if (precedingLabel && precedingLabel.tagName.toLowerCase() === "label" && precedingLabel.textContent) {
            const labelText = precedingLabel.textContent.trim();
            if (labelText) {
                addUniqueXPath(`//label[normalize-space()='${labelText}']/following-sibling::${TAG_NAME}`);
            }
        }

        // 策略 3: 其他特定的、有用的属性
        const specificAttributes = ["name", "placeholder", "title", "alt", "aria-label"];
        for (const attr of specificAttributes) {
            const value = element.getAttribute(attr);
            if (value) {
                addUniqueXPath(`//${TAG_NAME}[@${attr}='${value}']`);
            }
        }

        // 策略 4: 文本内容 (适用于按钮、链接等)
        const text = (element.textContent ?? "").trim();
        if (text && !text.includes("\n")) { // 避免过长或多行的文本
            addUniqueXPath(`//${TAG_NAME}[normalize-space()='${text}']`);
            addUniqueXPath(`//${TAG_NAME}[contains(normalize-space(), '${text}')]`);
        }

        // 策略 5: Class 和属性组合
        const className = element.className;
        const type = element.getAttribute("type");
        if (className && typeof className === 'string' && type) {
            const singleClass = className.split(" ")[0];
            if (singleClass) {
                addUniqueXPath(`//${TAG_NAME}[contains(@class, '${singleClass}') and @type='${type}']`);
            }
        }

        // 策略 6: Chrome 优化版 XPath
        try {
            addUniqueXPath(chromeGetXPath(element, true));
        } catch (e) { /* 忽略错误 */
        }

        // 策略 7: Chrome 普通版 (带索引) XPath
        try {
            addUniqueXPath(chromeGetXPath(element));
        } catch (e) { /* 忽略错误 */
        }

        // 策略 8: 位置索引 (最不稳定，作为最后的备选)
        if (xpaths.size === 0) {
            try {
                const allSameTagElements = Array.from(document.getElementsByTagName(TAG_NAME));
                const indexInAll = allSameTagElements.indexOf(element);
                if (indexInAll !== -1) {
                    const xpathIndex = indexInAll + 1; // XPath 索引是1-based
                    const positionalXPath = `(//${TAG_NAME})[${xpathIndex}]`;
                    xpaths.add(positionalXPath); // 即使不唯一也添加，作为最后手段。
                }
            } catch (e) { /* 忽略错误 */
            }
        }

        return Array.from(xpaths);
    }

    function getXPath(element) {
        if (!element || element.nodeType !== Node.ELEMENT_NODE) {
            return "";
        }
        const segments = [];
        let currentElement = element;
        while (currentElement && currentElement.nodeType === Node.ELEMENT_NODE && currentElement.nodeName.toLowerCase() !== 'html') {
            let segment = currentElement.nodeName.toLowerCase();
            if (currentElement.id) {
                segment += `[@id='${currentElement.id}']`;
            } else {
                let sameTagSiblings = 0;
                let sibling = currentElement.previousElementSibling;
                while (sibling) {
                    if (sibling.nodeName.toLowerCase() === currentElement.nodeName.toLowerCase()) {
                        sameTagSiblings += 1;
                    }
                    sibling = sibling.previousElementSibling;
                }

                // XPath 索引是从 1 开始的
                const index = sameTagSiblings + 1;
                segment += `[${index}]`;
            }
            segments.unshift(segment);
            currentElement = currentElement.parentElement;
        }
        if (segments.length === 0) return "";
        return "/" + segments.join("/");
    }
    const allElements = Array.from(document.querySelectorAll("*"));
    for (const el of allElements) {
        const rect = el.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(el);
        const isVisible = (rect.width > 0 && rect.height > 0 && computedStyle.display !== "none" && computedStyle.visibility !== "hidden" && computedStyle.opacity !== "0");
        if (!isVisible) continue;
        const xpathList = Array.from(new Set([getXPath(el), ...generateXPaths(el)].filter(Boolean)));
        if (xpathList.length > 0) {
            el.setAttribute("data-shop-genius-xpath", JSON.stringify(xpathList));
        }
        el.setAttribute("data-shop-genius-bounding-box", JSON.stringify({
            x: rect.x + window.scrollX, 
            y: rect.y + window.scrollY, 
            width: rect.width, 
            height: rect.height
        }))
    }
})();
"""  # noqa
