"""
这个模块定义的 celery task，不直接参与数据挖掘或者同步，多用于 task 状态同步(shop-pupil 到 shopify-knowledge)
"""
from celery import shared_task
from loguru import logger

from crawl2.clients import shopify_knowledge
from crawl2.tasks.celery_wrap import _run_async


@shared_task
def notify_shopify_knowledge_task_succeed(_, task_id: str, *args, **kwargs):  # 第一个参数为 _ 是为了忽略 pipeline 执行返回值
    with logger.contextualize(knowledge_task_id=task_id):
        logger.info(f"Task {task_id} completed successfully, celery passed {args=} {kwargs=}.")
    client = shopify_knowledge.ShopifyKnowledgeClient.get_client()
    _run_async(client.notify_task_status(task_id))


@shared_task
def notify_shopify_knowledge_task_failed(request, exc, traceback, task_id: str='unknown'):
    with logger.contextualize(knowledge_task_id=task_id):
        logger.error(f"Task {task_id} failed, celery passed {request=} {exc=} {traceback=}.")
    client = shopify_knowledge.ShopifyKnowledgeClient.get_client()
    _run_async(client.notify_task_status(task_id, fail_reason=f'{exc} - {traceback}'[:512]))
