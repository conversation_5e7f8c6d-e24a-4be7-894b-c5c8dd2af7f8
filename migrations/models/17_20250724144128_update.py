from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "celery_task" ADD "workflow_id" INT;
        ALTER TABLE "celery_task" ALTER COLUMN "status" SET DEFAULT 'PENDING';
        CREATE TABLE IF NOT EXISTS "celery_workflow" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "name" VARCHAR(1024) NOT NULL,
    "status" VARCHAR(255) NOT NULL DEFAULT 'PENDING',
    "signature" JSONB NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "finished_at" TIMESTAMPTZ
);
CREATE INDEX IF NOT EXISTS "idx_celery_work_name_659e48" ON "celery_workflow" ("name", "status", "created_at");
COMMENT ON COLUMN "celery_task"."workflow_id" IS '关联的工作流 ID';
COMMENT ON COLUMN "celery_workflow"."name" IS '工作流名称';
COMMENT ON COLUMN "celery_workflow"."status" IS '工作流状态';
COMMENT ON COLUMN "celery_workflow"."signature" IS '这个工作流的 schema，用于描述工作流的执行步骤';
COMMENT ON COLUMN "celery_workflow"."created_at" IS '创建时间';
COMMENT ON COLUMN "celery_workflow"."updated_at" IS '最后更新时间';
COMMENT ON COLUMN "celery_workflow"."finished_at" IS '完成时间';
COMMENT ON TABLE "celery_workflow" IS 'Celery 工作流记录.';
        ALTER TABLE "celery_task" ADD CONSTRAINT "fk_celery_t_celery_w_23ba9314" FOREIGN KEY ("workflow_id") REFERENCES "celery_workflow" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "celery_task" DROP CONSTRAINT IF EXISTS "fk_celery_t_celery_w_23ba9314";
        ALTER TABLE "celery_task" DROP COLUMN "workflow_id";
        ALTER TABLE "celery_task" ALTER COLUMN "status" DROP DEFAULT;
        DROP TABLE IF EXISTS "celery_workflow";"""
