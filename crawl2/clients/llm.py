import asyncio
import base64
import contextlib
import os
from dataclasses import dataclass
from pathlib import Path
from typing import Literal, <PERSON><PERSON><PERSON>s, Dict, <PERSON>ple

import openai
from loguru import logger
from openai import AsyncOpenAI, AsyncStream, NotGiven, NOT_GIVEN
from openai.types.chat import ChatCompletionChunk
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from crawl2.clients import http
from crawl2.utils import parse_llm_json_response


@dataclass
class LLMApiProvider:
    base_url: str
    api_key: str
    concurrency_limit: int = 64


DOUBAO_DEEPSEEK = 'ep-20250205152022-nsdmb'
LLM_SERVICE_NAME: TypeAlias = Literal["pre", "prq", "doubao", "usq"]
llm_providers: Dict[LLM_SERVICE_NAME, LLMApiProvider] = {
    "usq": LLMApiProvider(
        base_url="https://usq-litellm.infra.leyantech.com/v1",
        api_key="sk-A9oLyO93YOxgQiP8csRbBw",
        concurrency_limit=32
    ),
    "pre": LLMApiProvider(
        base_url="https://pre-plato-one-api.leyanbot.com/v1",
        api_key="sk-Acw9yj3lAGfEZ2Ag6017730dFcC24cBd890b9a590d00Ed39"
    ),
    "prq": LLMApiProvider(
        base_url="https://prq-plato-one-api.leyanbot.com/v1",
        api_key="sk-1NdYFyRWIlwJuwqV0aB7F4248b0446C397A397E794B2F755"
    ),
    "doubao": LLMApiProvider(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key="23932667-123e-4f75-b2c2-d464f6efa691",
        concurrency_limit=64
    )
}
llm_services: Dict[str, tuple[AsyncOpenAI, asyncio.Semaphore]] = {}
llm_lock = asyncio.Lock()



@dataclass
class LLMConfig:
    service: LLM_SERVICE_NAME
    model: str
    temperature: float
    max_tokens: int | None | NotGiven = NOT_GIVEN
    enable_thinking: bool = True


retry_on_rate_limited = retry(
    stop=stop_after_attempt(10),
    wait=wait_exponential(multiplier=1, min=4, max=20),
    retry=retry_if_exception_type(openai.RateLimitError),
)


@retry_on_rate_limited
async def call_llm(conf: LLMConfig, prompt: str, parse_json: bool = True, extra_body=None, extra_headers=None) -> str | dict | list | None:
    try:
        async with get_llm(conf) as llm:
            extra_body = extra_body or {}
            if not conf.enable_thinking:
                extra_body["chat_template_kwargs"] = {"enable_thinking": False}
            # noinspection PyTypeChecker
            stream = await llm.chat.completions.create(
                model=conf.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=conf.temperature,
                max_tokens=conf.max_tokens,
                stream=True,
                extra_body=extra_body,
                extra_headers=extra_headers
            )
            if parse_json:
                result_text, result = await _extract_json_from_llm_stream(stream)
            else:
                result = await _extract_text_from_llm_stream(stream)
            return result
    except openai.RateLimitError:
        # 触发 @retry
        raise
    except Exception as e:
        logger.opt(exception=e).error(f"LLM API调用失败: {str(e)}")
        return None


@retry_on_rate_limited
async def call_llm_with_reasoning(conf: LLMConfig, prompt: str) -> Tuple[str | None, str | None]:
    try:
        async with get_llm(conf) as llm:
            if not conf.enable_thinking:
                extra_body = {"chat_template_kwargs": {"enable_thinking": False}}
            else:
                extra_body = None
            # noinspection PyTypeChecker
            stream = await llm.chat.completions.create(
                model=conf.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=conf.temperature,
                max_tokens=conf.max_tokens,
                stream=True,
                extra_body=extra_body
            )
            reasoning_content = ""
            content = ""
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta and hasattr(chunk.choices[0].delta, 'reasoning_content'):
                    reasoning_content += chunk.choices[0].delta.reasoning_content
                elif chunk.choices and chunk.choices[0].delta.content:
                    content += chunk.choices[0].delta.content
            return reasoning_content, content
    except openai.RateLimitError:
        # 触发 @retry
        raise
    except Exception as e:
        logger.opt(exception=e).error(f"LLM API调用失败: {str(e)}")
        return None, None


async def get_llm_sdk_and_concurrency_limit(service: LLM_SERVICE_NAME) -> Tuple[AsyncOpenAI, asyncio.Semaphore]:
    async with llm_lock:
        if service not in llm_services:
            api_provider = llm_providers[service]
            llm_services[service] = (
                AsyncOpenAI(
                    base_url=api_provider.base_url,
                    api_key=api_provider.api_key,
                ),
                asyncio.Semaphore(api_provider.concurrency_limit)
            )
        return llm_services[service]


@contextlib.asynccontextmanager
async def get_llm(conf: LLMConfig):
    llm, concurrency_limit = await get_llm_sdk_and_concurrency_limit(conf.service)
    async with concurrency_limit:
        yield llm


@retry_on_rate_limited
async def ocr_image_with_llm(conf: LLMConfig, image_url: str, encode_image: bool = True):
    logger.info(f"ocr_image: {image_url}")
    if encode_image:
        if image_url.startswith("http"):
            image_url = await _download_image_and_encode(image_url)
        else:
            image_url = await _encode_local_image(image_url)

    PROMPT = """帮忙提取下电商详情页图片里面的文案部分内容，要求保持原有的格式方便阅读，只需要图片里面的原文，不要添加任何标题或其他内容，保持原来的语种，不要翻译，不要做任何解释说明。 如果图片中没有文字内容则返回"图片中没有任何文字内容"。"""  # NOQA

    async with get_llm(conf) as llm:
        # noinspection PyTypeChecker
        response = await llm.chat.completions.create(
            model=conf.model,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "image_url", "image_url": {"url": image_url}},
                        {"type": "text", "text": PROMPT},
                    ],
                }
            ],
            max_tokens=conf.max_tokens,
            temperature=conf.temperature,
            extra_body={"stop_token_ids": [151645, 151643]},
        )
    answer = response.choices[0].message.content
    answer = answer.replace("图片中没有任何文字内容。", "")
    answer = answer.replace("图片中没有任何文字内容", "")
    answer = answer.replace("图片中的文字内容如下：", "")
    return answer


async def _download_image_and_encode(image_url: str) -> str:
    response = await http.http_get(image_url)
    return "data:image/jpeg;base64," + base64.b64encode(response.content).decode("utf-8")


async def _encode_local_image(image_path: str) -> str:
    """
    从本地文件加载图片并编码为 base64 字符串
    Args:
        image_path: 本地图片文件路径
    Returns:
        str: base64 编码的图片数据，格式为 "data:image/[type];base64,[data]"
    """
    try:
        # 确保文件存在
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image file not found: {image_path}")

        # 获取文件扩展名来确定图片类型
        ext = Path(image_path).suffix.lower().lstrip(".")
        mime_type = (
            f"image/{ext}"
            if ext in ["jpg", "jpeg", "png", "gif", "webp"]
            else "image/jpeg"
        )

        # 读取并编码图片
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode("utf-8")
            return f"data:{mime_type};base64,{encoded_string}"

    except Exception as e:
        logger.error(f"Error encoding local image {image_path}: {str(e)}")
    return ''


async def _extract_json_from_llm_stream(stream: AsyncStream[ChatCompletionChunk]) -> (str, dict):
    collected_response = []
    async for chunk in stream:
        if chunk.choices[0].delta.content is not None:
            collected_response.append(chunk.choices[0].delta.content)
    result_text = "".join(collected_response)
    try:
        return result_text, parse_llm_json_response(result_text)
    except Exception as e:
        logger.opt(exception=e).error("fail to parse llm json response: {}", result_text)
        return result_text, None


async def _extract_text_from_llm_stream(stream: AsyncStream[ChatCompletionChunk]) -> str:
    collected_response = []
    async for chunk in stream:
        if chunk.choices[0].delta.content is not None:
            collected_response.append(chunk.choices[0].delta.content)
    return "".join(collected_response)
