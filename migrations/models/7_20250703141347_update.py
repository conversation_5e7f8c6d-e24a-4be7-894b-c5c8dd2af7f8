from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "store_document_faqs" (
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_crawled_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "crawl_status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "crawl_attempts" INT NOT NULL DEFAULT 0,
    "crawl_error" VARCHAR(1024),
    "id" SERIAL NOT NULL PRIMARY KEY,
    "file_url" VARCHAR(255) NOT NULL,
    "markdown_content" TEXT NOT NULL,
    "faqs" JSONB NOT NULL,
    "site_id" INT NOT NULL REFERENCES "shopify_site" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_store_docum_site_id_cb583e" ON "store_document_faqs" ("site_id");
COMMENT ON COLUMN "store_document_faqs"."created_at" IS '创建时间';
COMMENT ON COLUMN "store_document_faqs"."last_crawled_at" IS '最后一次尝试时间';
COMMENT ON COLUMN "store_document_faqs"."crawl_status" IS 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed\nSKIPPED: skipped';
COMMENT ON COLUMN "store_document_faqs"."crawl_attempts" IS '尝试次数';
COMMENT ON COLUMN "store_document_faqs"."crawl_error" IS '最后一次尝试的错误信息';
COMMENT ON COLUMN "store_document_faqs"."file_url" IS '文件url路径';
COMMENT ON COLUMN "store_document_faqs"."markdown_content" IS '文件的 Markdown 文本';
COMMENT ON COLUMN "store_document_faqs"."faqs" IS '大模型抽取的 faqs';
COMMENT ON TABLE "store_document_faqs" IS '商家文档知识';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "store_document_faqs";"""
