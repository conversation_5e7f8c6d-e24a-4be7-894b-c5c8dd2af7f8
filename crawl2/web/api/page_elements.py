from fastapi import APIRouter, HTTPException
from fastapi.responses import RedirectResponse

from crawl2 import schema
from crawl2 import utils
from crawl2.clients import cos
from crawl2.db import operations

router = APIRouter(prefix="/behavior-analysis", tags=["behavior-analysis"])


@router.get("/static-resource/index-html")
async def get_page_raw_content(url: str):
    page_url = "".join(utils.split_domain_and_path(url))
    if not await operations.exists_page(page_url):
        raise HTTPException(status_code=404, detail=f"Page with URL {url} not found")
    cos_client = cos.CosClient.get_client()
    return RedirectResponse(cos_client.get_page_html_url(page_url))


@router.get("/static-resource/screenshot")
async def get_page_screenshot(url: str):
    page_url = "".join(utils.split_domain_and_path(url))
    if not await operations.exists_page(page_url):
        raise HTTPException(status_code=404, detail=f"Page with URL {url} not found")
    cos_client = cos.CosClient.get_client()
    return RedirectResponse(cos_client.get_page_screenshot_url(page_url))


@router.get("/page-elements")
async def list_page_elements_by_url(url: str) -> list[schema.PageElement]:
    """根据 URL 列出页面元素"""
    page_url = "".join(utils.split_domain_and_path(url))
    if not await operations.exists_page(page_url):
        raise HTTPException(status_code=404, detail=f"Page with URL {url} not found")
    return await operations.list_page_elements(page_url)


@router.put("/page-elements/{element_id}")
async def update_page_element(element_id: int, update_data: schema.PageElementEditable) -> schema.PageElement:
    """更新页面元素"""
    if not await operations.exists_page_element_by_id(element_id):
        raise HTTPException(status_code=404, detail=f"PageElement {element_id} not found")
    await operations.update_page_element(element_id, update_data)
    return await operations.get_page_element(element_id)


@router.delete("/page-elements/{element_id}")
async def delete_page_element(element_id: int) -> schema.MessageOnlyResponse:
    """删除页面元素"""
    if not await operations.exists_page_element_by_id(element_id):
        raise HTTPException(status_code=404, detail=f"PageElement {element_id} not found")
    await operations.delete_page_element(element_id)
    return schema.MessageOnlyResponse(message=f"PageElement {element_id} deleted successfully")


@router.post("/page-elements")
async def create_page_element(body: schema.PageElementCreate) -> schema.PageElement:
    if not await operations.exists_page(body.page_url):
        raise HTTPException(status_code=404, detail="Page not found")
    return await operations.create_page_element(body.page_url, body)
