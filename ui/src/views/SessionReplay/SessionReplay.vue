<template>
<div class="h-full px-2">
  <Navigation 
    :breadcrumbs="[
      { text: 'Sites', path: '/' },
      { text: 'Site Details', path: `/sites/${currentDomain}` },
      { text: 'Session Replay' }
    ]"
  />
  <div class="my-2">
    <Card class="w-1/2 mx-auto">
      <CardHeader>
        <CardTitle>Session Replay</CardTitle>
      </CardHeader>
      <CardContent class="text-center">
        <Input v-model.trim="sessionId"/>
      </CardContent>
    </Card>

    <div v-if="loading" class="flex justify-center my-2">
      <Loader class="w-12 h-12 animate-spin" />
    </div>

    <div v-if="isEmpty" class="flex justify-center my-2">
      无数据
    </div>

    <div class="flex mt-2 w-full overflow-hidden">
      <ul class="space-y-2 overflow-auto w-[120px] h-[450px] mr-1">
        <li v-for="(replay, index) in replayData" class="p-1 border bg-white text-sm"
          :class="{'border-blue-500': currentReplayId === replay.replay_id, 'border-gray-300': currentReplayId !== replay.replay_id}"
          @click="changeReplay(replay.replay_id)">
          <div>replay #{{index + 1}}</div>
          <div v-if="replay.dataValid === false" class="mt-1 text-xs text-destructive/90">回放数据不可用</div>
          <div v-else class="mt-1 text-xs">{{replay.startTime}}</div>
        </li>
      </ul>
      <RRWebPlayer v-if="replayEvents.length > 1" class="flex-1 mx-auto" :events="replayEvents" :autoplay="false" :width="playerWidth" :height="playerHeight"/>
    </div>
  </div>
</div>
</template>

<script setup>
import { computed, onMounted, ref, watchEffect, toValue, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { refDebounced } from '@vueuse/core'
import { Loader } from 'lucide-vue-next'
import { RRWebPlayer } from '@bpsmartdesign/rrweb-player-vue3'
import '@bpsmartdesign/rrweb-player-vue3/dist/style.css'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import Navigation from '@/components/common/Navigation.vue'
import { useReplayData } from './replayData'
// import mockEvents from './new.json'
// import { unpack } from '@rrweb/packer'

const route = useRoute()

const sessionId = ref('')
const debouncedSessionId = refDebounced(sessionId, 400)

// const events = ref([])

const playerHeight = ref(0)
const playerWidth = ref(0)

const currentDomain = computed(() => {
  return route.params.domain
})

onMounted(() => {
  playerHeight.value = window.innerHeight - 290
  playerWidth.value = window.innerWidth - 160

  // setTimeout(() => {
  //   const newEvents = mockEvents.map((me) => {
  //     return unpack(me.rawEvent);
  //   })
  //   events.value = newEvents;
  // }, 300)

  // const newEvents = mockEvents.map((me) => {
  //     return unpack(me.rawEvent);
  //   })
  // console.log('xxxx', newEvents)
})

const { data: replayData, error, loading } = useReplayData(debouncedSessionId)

const currentReplayId = ref(null);

watchEffect(() => {
  const newReplayData = toValue(replayData);
  currentReplayId.value = newReplayData && newReplayData.length > 0 ? newReplayData[0].replay_id : null;
})

const replayEvents = computed(() => {
  const replay = (replayData.value ?? []).find((r) => r.replay_id === currentReplayId.value);
  // console.log('rrr', replay, currentReplayId.value, replayData.value)
  if (!replay) {
    return [];
  }
  // console.log('eventok?', replay.events[0]?.type === 2, replay.events)
  return replay.events;
})

const isEmpty = computed(() => {
  return !loading.value && sessionId.value.length !== 0 && replayData.value.length === 0;
})

const changeReplay = (replayId) => {
  currentReplayId.value = null;
  nextTick(() => {
    currentReplayId.value = replayId
  })
}

</script>