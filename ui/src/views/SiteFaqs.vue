<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { api } from '@/api/api.js'
import { AgGridVue } from 'ag-grid-vue3'
import Navigation from '../components/common/Navigation.vue'
import TruncatedCellRenderer from '../components/common/TruncatedCellRenderer.vue'
import ProductFaqEditor from '../components/product/ProductFaqEditor.vue'
import CrawlTaskTrigger from '../components/task/CrawlTaskTrigger.vue'
import BatchStatusDialog from '../components/product/BatchStatusDialog.vue'
import { Download, Upload, CheckSquare, Square } from 'lucide-vue-next'
import { formatDateTime } from '@/lib/utils.js'

const route = useRoute()
const faqs = ref([])
const loading = ref(true)
const error = ref(null)

// 分页相关状态
const currentPage = ref(1)
const pageSize = ref(2000)
const totalItems = ref(0)
const totalPages = ref(0)

// 批量选择相关状态
const selectedRows = ref(new Set())
const isSelectAll = ref(false)
const showBatchStatusDialog = ref(false)
const showSuccessMessage = ref(false)
const successMessage = ref('')

const statusCounts = ref({
  fresh: 0,
  editing: 0,
  ready: 0,
  disabled: 0,
  archived: 0
})

// 状态排序优先级函数
const statusSortOrder = {
  'fresh': 1,
  'editing': 2,
  'ready': 3,
  'disabled': 4,
  'archived': 5
}

// 自定义状态排序
const customStatusSort = (valueA, valueB) => {
  const orderA = statusSortOrder[valueA] || 999;
  const orderB = statusSortOrder[valueB] || 999;
  return orderA - orderB;
}

// FAQ格式化函数
const faqsFormatter = (value) => {
  if (Array.isArray(value) && value.length > 0) {
    return value.map(faq => `<div><strong>Q:</strong> ${faq.question}<br><strong>A:</strong> ${faq.answer}</div>`).join('<hr>');
  }
  return '';
}

// FAQ值获取函数 - 用于过滤和排序
const faqsValueGetter = (params) => {
  const faqs = params.data.faqs;
  if (Array.isArray(faqs) && faqs.length > 0) {
    return faqs.map(faq => `${faq.question} ${faq.answer}`).join(' ');
  }
  return '';
}

// AG Grid column definitions
const columnDefs = [
  {
    field: 'id',
    headerName: 'ID',
    sortable: true,
    filter: true,
    width: 20,
    cellStyle: { fontWeight: 'bold' }
  },
  {
    field: 'product_title', 
    headerName: 'Product Title',
    sortable: true,
    filter: true,
    cellRenderer: TruncatedCellRenderer,
    width: 200,
    flex: 0
  },
  {
    field: 'status',
    headerName: 'Status',
    sortable: true,
    filter: true,
    width: 50,
    flex: 0,
    sort: 'asc',
    sortIndex: 0,
    comparator: customStatusSort,
    filterParams: {
      filterOptions: [
        'equals',
        'notEqual',
        'inRange'
      ],
      buttons: ['apply', 'reset'],
      closeOnApply: true
    },
    cellRenderer: (params) => {
      const status = params.value;
      let color, bgColor;
      
      switch (status) {
        case 'fresh':
        case 'editing':
          color = '#0d9488';
          bgColor = '#ccfbf1';
          break;
        case 'ready':
          color = '#16a34a';
          bgColor = '#dcfce7';
          break;
        case 'disabled':
          color = '#6b7280';
          bgColor = '#f3f4f6';
          break;
        case 'archived':
          color = '#9ca3af';
          bgColor = '#f9fafb';
          break;
        default:
          color = '#6b7280';
          bgColor = '#f3f4f6';
      }
      
      return `<span style="
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        color: ${color};
        background-color: ${bgColor};
        text-transform: capitalize;
      ">${status}</span>`;
    }
  },
  {
    field: 'created_at',
    headerName: 'Created At',
    sortable: true,
    filter: true,
    width: 120,
    flex: 0,
    valueFormatter: (params) => {
      return formatDateTime(params.value);
    },
    filterParams: {
      filterOptions: ['equals', 'notEqual', 'inRange', 'lessThan', 'greaterThan'],
      buttons: ['apply', 'reset'],
      closeOnApply: true
    }
  },
  {
    field: 'last_edited_at',
    headerName: 'Last Edited',
    sortable: true,
    filter: true,
    width: 120,
    flex: 0,
    valueFormatter: (params) => {
      if (!params.value) return 'Never';
      return formatDateTime(params.value);
    },
    filterParams: {
      filterOptions: ['equals', 'notEqual', 'inRange', 'lessThan', 'greaterThan'],
      buttons: ['apply', 'reset'],
      closeOnApply: true
    }
  },
  { 
    field: 'product_type', 
    headerName: 'Product Type',
    sortable: true,
    filter: true,
    cellRenderer: TruncatedCellRenderer,
    flex: 1
  },
  {
    field: 'faqs',
    headerName: 'FAQs',
    sortable: true,
    filter: true,
    flex: 4,
    filterValueGetter: faqsValueGetter,
    cellRenderer: TruncatedCellRenderer,
    cellRendererParams: {
      formatter: faqsFormatter
    }
  }
]

// AG Grid default column settings
const defaultColDef = {
  resizable: true,
  minWidth: 100,
  sortable: true,
  filter: true
}

// 为行添加悬停效果，提示可以双击编辑
const getRowClass = (params) => {
  let classes = []
  
  if (params.data && params.data.is_disabled) {
    classes.push('disabled-row')
  }
  
  // 添加悬停提示样式
  classes.push('cursor-pointer hover:bg-blue-50')
  
  return classes.join(' ')
}

// 批量选择相关函数
let gridApi = null

const onGridReady = (params) => {
  gridApi = params.api
}

const onSelectionChanged = () => {
  if (gridApi) {
    const selectedNodes = gridApi.getSelectedNodes()
    selectedRows.value = new Set(selectedNodes.map(node => node.data.id))
    
    // 检查是否全选
    const totalRows = gridApi.getDisplayedRowCount()
    isSelectAll.value = selectedNodes.length === totalRows && totalRows > 0
  }
}

// AG Grid grid options
const gridOptions = {
  animateRows: true,
  domLayout: 'normal',
  rowHeight: 80, // 基础行高
  headerHeight: 48,
  rowSelection: {
    mode: 'multiRow',
    selectAll: 'filtered'
  },
  getRowClass: getRowClass,
  onSelectionChanged: onSelectionChanged,
  onGridReady: onGridReady
}

const fetchFaqStats = async () => {
  try {
    const response = await api.get(`/api/v1/sites/${route.params.domain}/faqs/edit_stats`)
    if (response.data) {
      statusCounts.value = response.data
    }
  } catch (err) {
    console.error('Error fetching FAQ stats:', err)
  }
}

const fetchFaqs = async (page = 1) => {
  try {
    loading.value = true
    const [mainRes] = await Promise.all([
      api.get(`/api/v1/sites/${route.params.domain}/faqs`, {
        params: { page: page, size: pageSize.value }
      }),
      fetchFaqStats()
    ])
    if (mainRes.data && 'items' in mainRes.data) {
      faqs.value = mainRes.data.items
      totalItems.value = mainRes.data.total
      totalPages.value = mainRes.data.total_pages
      currentPage.value = mainRes.data.page
    } else {
      faqs.value = mainRes.data
      totalItems.value = mainRes.data.length
      totalPages.value = 1
      currentPage.value = 1
    }
    
    // 清空选择状态
    if (gridApi) {
      gridApi.deselectAll()
    }
    selectedRows.value.clear()
    isSelectAll.value = false
  } catch (err) {
    error.value = err.message
    console.error('Error fetching FAQs:', err)
  } finally {
    loading.value = false
  }
}

const downloadFaqs = async () => {
  try {
    const response = await api.get(`/api/v1/sites/${route.params.domain}/faqs.excel`, {
      responseType: 'blob',
      timeout: 30000  // 30 seconds timeout
    })
    
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `${route.params.domain}_faqs.xlsx`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Error downloading FAQs:', error)
  }
}

const handlePageChange = (newPage) => {
  if (newPage >= 1 && newPage <= totalPages.value) {
    fetchFaqs(newPage)
  }
}
const handlePageSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchFaqs(1)
}
const getVisiblePages = () => {
  const pages = []
  const maxVisible = 5
  let start = Math.max(1, currentPage.value - Math.floor(maxVisible / 2))
  let end = Math.min(totalPages.value, start + maxVisible - 1)
  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  return pages
}

const isPopoverVisible = ref(false)
const selectedRowData = ref(null)

const onCellClicked = (event) => {
  if (event.colDef.field !== undefined) {
    openEditPopover(event.data)
  }
}

const openEditPopover = (rowData) => {
  selectedRowData.value = rowData
  isPopoverVisible.value = true
}
const closeEditPopover = () => {
  isPopoverVisible.value = false
  selectedRowData.value = null
}

const saveFaq = async (updatedData) => {
  try {
    const payload = {
      faqs: updatedData.faqs,
      status: updatedData.status
    }
    const faqId = parseInt(updatedData.id, 10)
    await api.put(`/api/v1/sites/${route.params.domain}/faqs/${faqId}`, payload)
    const index = faqs.value.findIndex(item => item.id === updatedData.id);
    if (index !== -1) {
      faqs.value[index].faqs = updatedData.faqs
      faqs.value[index].status = updatedData.status
    }
    await fetchFaqStats();
  } catch (error) {
    console.error('Failed to save FAQ:', error);
    alert('Failed to save FAQ. Please try again.');
    return;
  }
  closeEditPopover()
}

const saveAndNextFaq = async (updatedData) => {
  try {
    const payload = {
      faqs: updatedData.faqs,
      status: updatedData.status
    }
    const faqId = parseInt(updatedData.id, 10)
    await api.put(`/api/v1/sites/${route.params.domain}/faqs/${faqId}`, payload)
    const index = faqs.value.findIndex(item => item.id === updatedData.id);
    if (index !== -1) {
      faqs.value[index].faqs = updatedData.faqs
      faqs.value[index].status = updatedData.status
    }
    await fetchFaqStats();
    // 智能跳转逻辑可按需补充
    const nextIndex = index + 1;
    if (nextIndex < faqs.value.length) {
      selectedRowData.value = faqs.value[nextIndex];
    } else {
      closeEditPopover();
    }
  } catch (error) {
    console.error('Failed to save FAQ:', error);
    alert('Failed to save FAQ. Please try again.');
    return;
  }
}

const deleteFaq = async (faqData) => {
  try {
    await api.put(`/api/v1/sites/${route.params.domain}/faqs/${faqData.id}`, {
      status: 'archived'
    });
    const index = faqs.value.findIndex(item => item.id === faqData.id);
    if (index !== -1) {
      faqs.value.splice(index, 1);
    }
  } catch (error) {
    console.error('Failed to delete FAQ:', error);
    alert('Failed to delete FAQ. Please try again.');
    return;
  }
  closeEditPopover()
}

const handleBatchStatusUpdate = () => {
  if (selectedRows.value.size > 0) {
    showBatchStatusDialog.value = true
  }
}

const handleBatchStatusConfirm = async (newStatus) => {
  try {
    const selectedIds = Array.from(selectedRows.value)
    
    // 限制批量操作数量，避免瞬时压力过大
    if (selectedIds.length > 50) {
      alert('一次最多只能批量更新50个FAQ，请减少选择数量')
      return
    }
    
    // 显示进度提示
    const progressMessage = `正在更新 ${selectedIds.length} 个FAQ状态...`
    console.log(progressMessage)
    
    // 逐个调用现有的更新API
    const updatePromises = selectedIds.map(async (id) => {
      try {
        const payload = {
          status: newStatus
        }
        await api.put(`/api/v1/sites/${route.params.domain}/faqs/${id}`, payload)
        return { id, success: true }
      } catch (error) {
        console.error(`Failed to update FAQ ${id}:`, error)
        return { id, success: false, error: error.message }
      }
    })
    
    // 等待所有更新完成
    const results = await Promise.all(updatePromises)
    
    // 统计成功和失败的数量
    const successCount = results.filter(r => r.success).length
    const failCount = results.filter(r => !r.success).length
    
    // 更新本地数据
    faqs.value.forEach(faq => {
      if (selectedIds.includes(faq.id)) {
        const result = results.find(r => r.id === faq.id)
        if (result && result.success) {
          faq.status = newStatus
        }
      }
    })
    
    // 清空选择
    if (gridApi) {
      gridApi.deselectAll()
    }
    selectedRows.value.clear()
    isSelectAll.value = false
    
    // 刷新统计数据
    await fetchFaqStats()
    
    // 简化提示，只在控制台显示详细信息
    if (failCount > 0) {
      console.warn(`批量更新完成！成功：${successCount} 个，失败：${failCount} 个。`)
      // 只在有失败的情况下显示简单提示
      alert(`更新完成，${failCount} 个失败`)
    } else {
      console.log(`批量更新成功！共更新 ${successCount} 个FAQ状态。`)
      // 显示优雅的成功提示
      successMessage.value = `成功更新 ${successCount} 个FAQ状态`
      showSuccessMessage.value = true
      setTimeout(() => {
        showSuccessMessage.value = false
      }, 3000) // 3秒后自动消失
    }
    
    showBatchStatusDialog.value = false
  } catch (error) {
    console.error('Failed to update batch status:', error)
    alert('批量更新状态失败，请重试')
  }
}

const closeBatchStatusDialog = () => {
  showBatchStatusDialog.value = false
}

const clearSelection = () => {
  if (gridApi) {
    gridApi.deselectAll()
  }
  selectedRows.value.clear()
  isSelectAll.value = false
}

onMounted(() => {
  fetchFaqs()
})
</script>

<template>
  <div class="flex flex-col h-screen w-full pt-0">
    <Navigation 
      :breadcrumbs="[
        { text: 'Sites', path: '/' },
        { text: 'Site Details', path: `/sites/${route.params.domain}` },
        { text: 'FAQs' }
      ]"
    />

    <div class="flex justify-between items-center p-4 px-8 w-full bg-white shadow sticky top-16 z-10">
      <h1 class="m-0 text-2xl">FAQs</h1>
      <div class="flex items-center gap-2">
        <!-- 批量操作按钮 -->
        <div v-if="selectedRows.size > 0" class="flex items-center gap-2 mr-4">
          <button 
            class="flex items-center gap-2 p-2 px-4 bg-blue-500 text-white border-none rounded cursor-pointer text-sm transition-colors duration-200 hover:bg-blue-600" 
            @click="handleBatchStatusUpdate"
          >
            <CheckSquare size="16" />
            批量标注 ({{ selectedRows.size }})
          </button>
          <button 
            class="flex items-center gap-2 p-2 px-3 bg-gray-500 text-white border-none rounded cursor-pointer text-sm transition-colors duration-200 hover:bg-gray-600" 
            @click="clearSelection"
            title="取消所有选择"
          >
            <Square size="16" />
            取消选择
          </button>
        </div>
        
        <button class="flex items-center gap-2 p-2 px-4 bg-green-500 text-white border-none rounded cursor-pointer text-sm transition-colors duration-200 hover:bg-green-600" @click="downloadFaqs">
          <Download size="16" />
          下载Excel
        </button>
        <CrawlTaskTrigger 
          taskName="sync_product_faqs_to_knowledge_base" 
          displayName="同步到线上" 
          :defaultParams="{ domain: route.params.domain }"
        />
      </div>
    </div>

    <!-- 统计信息展示区 -->
    <div class="flex gap-8 items-center mx-8 mt-4 mb-0 text-base text-gray-800">
      <div>FAQ总数：{{ totalItems }}</div>
      <div>
        <span v-for="(count, status) in statusCounts" :key="status" class="mr-4 capitalize">
          {{ status }}：{{ count }}
        </span>
      </div>
    </div>

    <div class="flex-1 p-4 px-8 flex flex-col w-full bg-gray-50">
      <!-- 成功提示消息 -->
      <div v-if="showSuccessMessage" class="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg transition-all duration-300">
        <div class="flex items-center gap-2 text-green-800">
          <span>✅</span>
          <span>{{ successMessage }}</span>
        </div>
      </div>
      
      <div v-if="loading" class="flex justify-center items-center h-full text-gray-600 bg-white rounded-lg shadow">
        Loading FAQs...
      </div>
      <div v-else-if="error" class="flex justify-center items-center h-full text-red-500 bg-white rounded-lg shadow">
        Error: {{ error }}
      </div>
      <div v-else-if="faqs.length" class="flex-1 w-full min-w-[1280px] bg-white rounded-lg shadow">
        <AgGridVue
          :rowData="faqs"
          :columnDefs="columnDefs"
          :defaultColDef="defaultColDef"
          @cell-clicked="onCellClicked"
          v-bind="gridOptions"
          class="w-full h-full"
        />
      </div>
      <div v-else class="flex justify-center items-center h-full text-gray-600 bg-white rounded-lg shadow">
        No FAQs found for this site.
      </div>
    </div>

    <div v-if="!loading && !error && totalPages > 1" class="flex justify-between items-center p-4 px-8 bg-white border-t">
      <div class="flex items-center gap-4">
        <span class="text-sm text-gray-600">
          显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalItems) }} 条，共 {{ totalItems }} 条
        </span>
        <div class="flex items-center gap-2">
          <label class="text-sm text-gray-600">每页显示：</label>
          <select 
            v-model="pageSize" 
            @change="handlePageSizeChange(pageSize)"
            class="border border-gray-300 rounded px-2 py-1 text-sm"
          >
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="200">200</option>
          </select>
        </div>
      </div>
      <div class="flex items-center gap-2">
        <button @click="handlePageChange(1)" :disabled="currentPage === 1" class="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">首页</button>
        <button @click="handlePageChange(currentPage - 1)" :disabled="currentPage === 1" class="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">上一页</button>
        <div class="flex items-center gap-1">
          <button v-for="page in getVisiblePages()" :key="page" @click="handlePageChange(page)" :class="['px-3 py-1 text-sm border rounded', page === currentPage ? 'bg-blue-500 text-white border-blue-500' : 'border-gray-300 hover:bg-gray-50']">{{ page }}</button>
        </div>
        <button @click="handlePageChange(currentPage + 1)" :disabled="currentPage === totalPages" class="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">下一页</button>
        <button @click="handlePageChange(totalPages)" :disabled="currentPage === totalPages" class="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">末页</button>
      </div>
    </div>

    <ProductFaqEditor
      v-if="isPopoverVisible"
      :row-data="selectedRowData"
      @close="closeEditPopover"
      @save="saveFaq"
      @delete="deleteFaq"
      @saveAndNext="saveAndNextFaq"
    />

    <!-- 批量标注对话框 -->
    <BatchStatusDialog
      :show="showBatchStatusDialog"
      :selected-count="selectedRows.size"
      @close="closeBatchStatusDialog"
      @confirm="handleBatchStatusConfirm"
    />
  </div>
</template>

<style scoped>
/* 确保行高能够自适应内容 */
:deep(.ag-row) {
  height: auto !important;
  min-height: 80px !important;
}

/* 确保单元格内容能够正确显示 */
:deep(.ag-cell-wrapper) {
  height: 100% !important;
  min-height: 100% !important;
}

/* 优化中英文单元格的显示 */
:deep(.bilingual-cell-renderer) {
  height: 100% !important;
  min-height: 100% !important;
}
</style> 