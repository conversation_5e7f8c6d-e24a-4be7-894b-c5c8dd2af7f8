import os

from pydantic_settings import BaseSettings
from loguru import logger

if os.environ.get('APOLLO_META'):
    from lepollo import get_config
    for key, value in get_config('application')._properties.items():
        os.environ[key] = value
    logger.info('loaded application config from Apollo')


class Settings(BaseSettings):
    """这个 class 内声明的默认配置，和部署在 office 服务器上的测试环境相同，便于开发时本地做调试。"""
    DATABASE_URL: str = "************************************************/shopify_knowledge"
    REDIS_URL: str = "redis://192.168.1.99:6379/0"
    CRAWL_PROXY: str = "http://192.168.4.52:3128"
    DOCLING_ENDPOINT: str = "http://180.76.239.97:8888/markdown"
    SHOPIFY_KNOWLEDGE_BASE_URL: str = "https://stq-api.leyanbot.com"
    SHOPIFY_KNOWLEDGE_API_KEY: str = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsZXlhbl91c2VyIjp7Im5hbWUiOiLmnY7lm5sifSwiaXNzIjoic2hvcGlmeS1zdG9yZS1jZW50ZXIiLCJzdG9yZUluZm8iOnsic3RvcmVJZCI6Mi4wLCJkb21haW4iOiJzaG9wLWdlbml1cy1kZW1vLXN0cS5teXNob3BpZnkuY29tIiwiZXh0ZXJuYWxTdG9yZUlkIjoiOTE2OTU4NDE2MDIifSwiZXhwIjoyNjE1MjcwNzE0LCJpYXQiOjE3NTEyNzA3MTQsInVzZXJJZCI6MTgsImp0aSI6ImMwNzgzNTZkLWQxNDAtNGFlZC1iMTE5LTk0NGE0NGE4ZTZiNyIsIm9yZ0lkIjoxNX0.dK3HAdEn8yZHIEUfm8oe7ZKbiFXsAEDcNbTJrFDw7YU"  # NOQA
    STORE_CENTER_BASE_URL: str = "https://stq-api.leyanbot.com/shopify-store-center/api"
    STORE_CENTER_API_KEY: str = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsZXlhbl91c2VyIjp7Im5hbWUiOiJxaW5nc29uZyJ9LCJpc3MiOiJzaG9waWZ5LXN0b3JlLWNlbnRlciIsInN0b3JlSW5mbyI6eyJzdG9yZUlkIjoyLjAsImRvbWFpbiI6InNob3AtZ2VuaXVzLWRlbW8tc3RxLm15c2hvcGlmeS5jb20iLCJleHRlcm5hbFN0b3JlSWQiOiI5MTY5NTg0MTYwMiJ9LCJleHAiOjI2MTQzMzEyMDQsImlhdCI6MTc1MDMzMTIwNCwidXNlcklkIjoxOCwianRpIjoiMTI5MDAzNjktODFjOS00ODRmLWJlN2MtNzczOGMwYmViZjBlIiwib3JnSWQiOjE1fQ.NOpt0icyWXaGMUU77EWzx2uWxjZl_rRkT54H1Z_QsB0"  # NOQA
    EMBEDDING_API_URL: str = "http://180.76.239.97:8002/embed"
    SPARSE_EMBEDDING_API_URL: str = "http://180.76.239.97:8002/sparse_embed"
    QDRANT_URL: str = 'https://usq-qdrant.infra.leyantech.com:443'
    QDRANT_VECTOR_SIZE: int = 1024
    QDRANT_PRODUCT_KNOWLEDGE_COLLECTION: str = "product_knowledge_point_test_v2"
    QDRANT_STORE_KNOWLEDGE_COLLECTION: str = "store_knowledge_point_test_v2"
    QDRANT_COLLECTION_KNOWLEDGE_COLLECTION: str = "collection_knowledge_point_test_v2"
    DINGTALK_WEBHOOK: str = "https://oapi.dingtalk.com/robot/send?access_token=9de324ad985d0669df9c2c719168b20891a51f76f0308d16839c10e814890dab"  # NOQA
    COS_SECRET_ID: str = "AKIDkaM0Um5FCzb1FG7164wd8TddAfx31rVx"
    COS_SECRET_KEY: str = "Y1NZsGvSqKqCRVN8zjJghjcrzwlPrGWZ"
    COS_REGION: str = "ap-nanjing"
    COS_BUCKET: str = "shop-pupil-1252160878"
    LECHAT_PRODUCT_KV_KNOWLEDGE_COLLECTION: str = "lechat_product_kv_point_test_v2"
    LECHAT_PRODUCT_PAGE_KNOWLEDGE_COLLECTION: str = "lechat_product_page_point_test_v1"
    LECHAT_KNOWLEDGE_API_KEY: str = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuaW1Ub2tlbiI6ImZha2VOaW1Ub2tlbi0xMDQ4OSIsInJvbGVzIjpbeyJpZCI6Miwicm9sZVR5cGUiOiJTVVBFUl9BRE1JTiIsIm5hbWUiOiJTVVBFUl9BRE1JTiIsInBlcm1pc3Npb25zIjpbeyJpZCI6NywibmFtZSI6IlNVUEVSUklTRSJ9LHsiaWQiOjIsIm5hbWUiOiJRQV9DT05GSUcifSx7ImlkIjozLCJuYW1lIjoiTUFSS0VUSU5HX0NPTkZJRyJ9LHsiaWQiOjQsIm5hbWUiOiJTWVNURU1fU0VUVElORyJ9LHsiaWQiOjUsIm5hbWUiOiJTSE9QX01BTkFHRVIifSx7ImlkIjo2LCJuYW1lIjoiQVNTSVNUQU5UX01BTkFHRVIifSx7ImlkIjo4LCJuYW1lIjoiVElNRU9VVF9BVVRPX1JFUExZIn0seyJpZCI6OSwibmFtZSI6IlFVSUNLX1BIUkFTRV9URUFNX1RFTVBMQVRFX0VESVQifV19XSwiaXNzIjoib3ZlcnNlYS1zdG9yZS1ob21lIiwidHlwZSI6MSwib3JnSWQiOjExNjMsImxleWFuX3VzZXIiOnsibmlja05hbWUiOiLlvKDnjonllpxfMTA0ODkifSwicGhvbmVOdW1iZXIiOiIrODYxNTgyNzYxMTU5MyIsIm5hbWUiOiJPVSIsImlkIjoxMDQ4OSwiZXhwIjoxNzM4ODM1MDEyLCJpYXQiOjE3Mzc2MjU0MTIsImp0aSI6ImY0MzlkYzJkLWRmZDctNGQyNi05NDQ1LThhOGQyMTU3YmM0NyIsImFjY291bnQiOiJsZXlhbjIiLCJlbWFpbCI6IjEwMzE1OTIwMTIxQHFxLmNvbSJ9.ev4RP0Ruy9RPH_26dppei5-PItP51DFgkTSMNqXZpPY"  # NOQA
    TASK_CONCURRENCY: int = 8

    # SSO配置
    SSO_BASE_URL: str = "https://sso.leyantech.com"
    SSO_CLIENT_ID: str = "shop-pupil@office"
    SSO_CLIENT_SECRET: str = "vEt3qDVhlPfnGb9sF5oBZXaOHDGpHSvUIA9STBRru5BwLmGy"
    SSO_SCOPE: str = "openid profile email phone"
    SSO_TOKEN_ENDPOINT_AUTH_METHOD: str = "client_secret_post"
    SSO_TOKEN_URL: str = "https://hukou.leyanbot.com/oauth2/token"
    SSO_AUTHORIZATION_URL: str = "https://hukou.leyanbot.com/oauth2/authorize"
    SSO_REDIRECT_URI: str = "http://127.0.0.1:8000/api/v1/auth/callback"

    # JWT配置
    JWT_SECRET_KEY: str = "your-super-secret-jwt-key-change-in-production"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 7 * 24 * 60  # 7 days

    # 营销话术版本设置
    MARKETING_COPY_PROMPT_VERSION: int = 1

    class Config:
        case_sensitive = True


settings = Settings()


TORTOISE_ORM = {
    "connections": {
        "default": settings.DATABASE_URL,
    },
    "apps": {
        "models": {
            "models": ["crawl2.db.models", "aerich.models"],
            "default_connection": "default",
        }
    }
}
