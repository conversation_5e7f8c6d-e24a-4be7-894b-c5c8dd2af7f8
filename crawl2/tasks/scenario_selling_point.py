import json

from loguru import logger

from crawl2.clients import shopify_knowledge, llm
from crawl2.db import operations
from crawl2.schema import Sc<PERSON><PERSON>SellingPoint
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync, CeleryTaskTqdm

SCENARIO_KNOWLEDGE_PROMPT = """
You are a senior user experience expert of {domain} products.
Summarize some usage scenarios that users will consider when purchasing this type of product from your memory.
Try to be as practical and comprehensive as possible and output it in markdown format.
"""


SCENARIO_LABEL_PROMPT = """
You are a senior {domain} user experience expert. 
Please summarize and extract all user scenario labels from the provided materials, and generate an explanation for each label.
Each label should be within 8 words, as colloquial as possible, easy for users to understand, and output in the following json format:

[
    {{
        "label": "string",
        "explanation": "string"
    }},
    ...
]

Materials:
{docs}

Please check the integrity of commas, brackets and double quotes to ensure the standardization of the output.
"""


PRODUCT_SCENARIO_PROMPT = """
You are an excellent e-commerce operation expert, good at generating applicable scenarios that may be associated with the product based on the product's parameter characteristics and field-related applicable scenarios.

Please generate 10 specific applicable scenarios for the given product and explain the applicable reasons in combination with the specific product parameters.

Do not include the statement "specific parameters are not marked" in the reason.

Describe the applicable scenarios from the user's perspective as much as possible to attract users' purchasing interest.

The product title and applicable scenarios are given in the form of a JSON list:
[
    {{
        "name": "Applicable scenarios",
        "reason": "Explain the applicable reasons in combination with parameters"
    }}，
    ...
]

## Related applicable scenarios
{scenarios}

## Product information of the product to be generated for the applicable scenarios
{product_info}

## The applicable scenarios of the product you generated
"""


PRODUCT_TYPE_MAP = {
    "Jacket": "heated jacket",
    "Vest": "heated vest",
    "Hoodie": "heated hoodie",
    "Pants": "heated pants"
}


llm_conf = llm.LLMConfig('doubao', llm.DOUBAO_DEEPSEEK, 0.6, 10000)


async def get_docs(product_type):
    prompt = SCENARIO_KNOWLEDGE_PROMPT.format(domain=product_type)
    reasoning, response = await llm.call_llm_with_reasoning(llm_conf, prompt)
    return reasoning + response


async def get_scenario_labels(product_type):
    docs = await get_docs(product_type)
    response = None
    try:
        prompt = SCENARIO_LABEL_PROMPT.format(domain=product_type, docs=docs)
        response = await llm.call_llm(llm_conf, prompt)
        logger.info("get product type scenarios {} llm response: {}", product_type, response)
        await operations.save_product_type_scenarios(product_type, response)
        return response
    except Exception as e:
        logger.warning(response)
        logger.opt(exception=e).error(f'{product_type} scenarios总结失败')
    return None


async def get_product_type_scenarios(product_type: str):
    logger.info("get product_type {} scenarios", product_type)
    try:
        scenarios = await operations.get_product_type_scenarios(product_type)
        if not scenarios:
            scenarios = await get_scenario_labels(product_type)
        return scenarios
    except Exception as e:
        logger.opt(exception=e).error("fail to get product type scenarios")
    return None


def format_product_info(product, knowledge):
    return {
        "title": product.title,
        "price": product.price,
        "tags": product.tags,
        "product_type": product.product_type,
        "content": knowledge.markdown_content
    }


@celery_task
async def generate_product_scenarios(product_url: str):
    """生成单个商品的场景卖点"""
    product = await operations.get_product_metadata(product_url)
    product_type_scenarios = ""
    if product.product_type:
        product_type = PRODUCT_TYPE_MAP.get(product.product_type, product.product_type)
        logger.info("change product from {} to {}", product.product_type, product_type)
        product_type_scenarios = await get_product_type_scenarios(product_type)
        logger.info("get {} scenarios: {}", product_type, product_type_scenarios)
    else:
        logger.warning("product {} has no product type, use empty scenarios", product_url)
    knowledge = await operations.get_product_knowledge(product_url)
    if not knowledge:
        logger.error("fail to get {} knowledge markdown content.", product_url)
        return
    product_info = format_product_info(product, knowledge)
    prompt = PRODUCT_SCENARIO_PROMPT.format(
        scenarios=product_type_scenarios,
        product_info=json.dumps(product_info, ensure_ascii=False, indent=4)
    )
    response = await llm.call_llm(llm_conf, prompt)
    logger.info("generate {} scenarios: {}", product_url, response)
    scenario_selling_points = []

    if not isinstance(response, list):
        logger.error("scenario selling points llm response is not list: {}", response)
        return
    for point in response:
        try:
            valid_point = ScenarioSellingPoint.model_validate(point)
            scenario_selling_points.append(valid_point)
        except Exception as e:
            logger.opt(exception=e).error("fail to parse selling point: {}", point)
    if scenario_selling_points:
        await operations.delete_scenario_selling_points(product_url)
        await operations.save_product_scenario_selling_points(product_url, scenario_selling_points)


@celery_task
async def generate_product_type_scenarios(domain: str):
    """为指定站点的所有商品类型生成场景卖点"""
    all_products = await operations.list_product_metadata(domain)

    product_types = set(p.product_type for p in all_products)
    tasks = [get_product_type_scenarios(PRODUCT_TYPE_MAP.get(product_type, product_type))
             for product_type in product_types]
    await CeleryTaskTqdmAsync.gather(*tasks, desc="Generate Product Type Scenarios")


@celery_task
async def generate_all_product_scenarios(domain: str):
    all_products = await operations.list_product_metadata(domain)
    all_product_scenario_points = await operations.list_product_scenario_selling_points(domain)

    processed_product_urls = set([product.product_url for product in all_product_scenario_points])
    tasks = []
    for product in all_products:
        if product.url in processed_product_urls:
            logger.info("product {} already has scenario selling points, skip extract.", product.url)
            continue
        tasks.append(generate_product_scenarios(product.url))

    await CeleryTaskTqdmAsync.gather(*tasks, desc="Generate Product Scenario Selling Points")


@celery_task
async def sync_all_product_type_scenarios(domain: str):
    """
    同步已挖掘的产品类型场景到 shopify-knowledge.
    """
    site = await operations.get_site(domain)
    if not site:
        logger.error(f"Site {domain} not found, cannot sync product type scenarios.")
        return
    product_types = await operations.get_product_type_list(domain)
    client = shopify_knowledge.ShopifyKnowledgeClient.get_client()
    for product_type in CeleryTaskTqdm(product_types, desc="Syncing Product Type Scenarios"):
        scenarios_data = await operations.list_product_type_scenarios(product_type)
        if not scenarios_data:
            logger.warning(f"No scenarios found for product type {product_type}")
            continue
        scenarios = [shopify_knowledge.ProductTypeScenarioItem(label=s.label, explanation=s.explanation)
                     for s in scenarios_data]
        payload = shopify_knowledge.ProductTypeScenario(scenarios=scenarios)
        try:
            await client.sync_shopify_knowledge(site.shopify_domain, payload)
        except Exception as e:
            logger.opt(exception=e).error(f"Failed to sync product type scenarios for {product_type} : {e}")


@celery_task
async def sync_all_scenario_selling_points(domain: str):
    """
    同步已挖掘的场景卖点到 shopify-knowledge.
    """
    site = await operations.get_site(domain)
    if not site:
        logger.error(f"Site {domain} not found, cannot sync scenario selling points.")
        return
    all_products = await operations.list_product_scenario_selling_points(domain)
    client = shopify_knowledge.ShopifyKnowledgeClient.get_client()
    for product in CeleryTaskTqdm(all_products, desc="Syncing Scenario Selling Points"):
        try:
            points = [
                shopify_knowledge.ScenarioSellingPointItem(
                    scenario=p.scenario,
                    content=p.content,
                    element_targets=p.element_targets
                )
                for p in product.selling_points
            ]
            payload = shopify_knowledge.ProductScenarioSellingPoint(
                spuId=product.product_id,
                scenarioSellingPoints=points
            )
            logger.info("sync scenario selling points for product {}: {}", product.product_id, payload)
            await client.sync_shopify_knowledge(site.shopify_domain, payload)
        except Exception as e:
            logger.opt(exception=e).error(f"Failed to sync scenario selling point for {product.product_id} : {e}")


@celery_task
async def sync_single_product_scenario_selling_points(product_url: str):
    """
    同步指定商品的场景卖点到 shopify-knowledge.
    """
    product = await operations.get_product_metadata(product_url)
    if not product:
        logger.error(f"Product {product_url} not found, cannot sync scenario selling points.")
        return
    site = await product.site
    points = await operations.list_single_product_scenario_selling_points(product_url)
    client = shopify_knowledge.ShopifyKnowledgeClient.get_client()
    payload_points = [
        shopify_knowledge.ScenarioSellingPointItem(
            scenario=p.scenario,
            content=p.content,
            element_targets=p.element_targets
        )
        for p in points
    ]
    payload = shopify_knowledge.ProductScenarioSellingPoint(
        spuId=product.product_id,
        scenarioSellingPoints=payload_points
    )
    logger.info("sync scenario selling points for product {}: {}", product.product_id, payload)
    try:
        await client.sync_shopify_knowledge(site.shopify_domain, payload)
    except Exception as e:
        logger.opt(exception=e).error(f"Failed to sync scenario selling point for {product.url} : {e}")
