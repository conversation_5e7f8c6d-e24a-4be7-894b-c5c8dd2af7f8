<script setup>
import { useRouter } from 'vue-router'
import { ExternalLink } from 'lucide-vue-next'

const props = defineProps({
  product: {
    type: Object,
    required: true
  },
  domain: {
    type: String,
    required: true
  }
})

const router = useRouter()

const navigateToProductDetails = () => {
  router.push({
    name: 'product-details',
    params: {
      domain: props.domain,
      productId: props.product.product_id
    }
  })
}
</script>

<template>
  <div class="bg-white rounded-lg p-6 shadow cursor-pointer transition-all duration-200 hover:-translate-y-0.5 hover:shadow-lg" @click="navigateToProductDetails">
    <div class="flex justify-between items-center">
      <h2 class="m-0 text-gray-800 text-2xl flex-1">{{ product.title }}</h2>
      <a 
        v-if="product.url" 
        :href="product.url" 
        target="_blank" 
        class="flex items-center gap-2 text-gray-800 no-underline p-2 rounded bg-gray-50 hover:bg-gray-100 hover:text-blue-600 transition-all duration-200"
        title="Open original product page"
      >
        <span class="text-sm font-medium">Original Page</span>
        <ExternalLink size="16" />
      </a>
    </div>
    <p class="text-gray-600 text-sm my-2">{{ product.product_type }}</p>
    <p class="font-semibold text-gray-800 my-2">${{ product.price }}</p>
    <p class="text-gray-600 text-sm my-2">{{ product.vendor }}</p>
    <div class="flex flex-wrap gap-2 mt-2" v-if="product.tags">
      <span v-for="tag in product.tags.split(',')" :key="tag" class="bg-gray-50 text-gray-700 px-2 py-1 rounded-full text-xs">
        {{ tag.trim() }}
      </span>
    </div>
  </div>
</template> 