def create_shopify_product_crawl_and_element_detection_flow(clear_existing: bool = False):
    """从页面元素抓取到页面元素识别的完整流程

    params:
        page_url: 要抓取的页面
    """
    from pocketflow import AsyncFlow
    from crawl2.tasks.page_element import nodes
    from crawl2.tasks.page_element.flow.crawl_shopify_product_page import create_crawl_shopify_product_page_flow
    from crawl2.tasks.page_element.flow.page_element_detection import create_page_element_detection_flow

    start = nodes.Start()
    shopify_page_status = nodes.ShopifyPageStatus()
    crawl_shopify_product_page_flow = create_crawl_shopify_product_page_flow()
    update_shopify_product_page_crawl_status = nodes.UpdateShopifyPageCrawlStatus()
    shopify_page_elements_exists = nodes.ExistsShopifyPageElements()
    clear_page_elements = nodes.ClearShopifyPageElements()
    crawl_detection_bridge = nodes.CrawlAndDetectionBridge()
    page_element_detection_flow = create_page_element_detection_flow()

    end = nodes.End()

    if clear_existing:
        start >> crawl_shopify_product_page_flow
    else:
        start >> shopify_page_status
        shopify_page_status - "inited" >> end
        shopify_page_status - "not init" >> crawl_shopify_product_page_flow

    # 将页面信息保存到 oss
    crawl_shopify_product_page_flow >> update_shopify_product_page_crawl_status

    if clear_existing:
        update_shopify_product_page_crawl_status >> clear_page_elements
        clear_page_elements >> crawl_detection_bridge
    else:
        update_shopify_product_page_crawl_status >> shopify_page_elements_exists
        shopify_page_elements_exists - "inited" >> end
        shopify_page_elements_exists - "not init" >> crawl_detection_bridge

    crawl_detection_bridge >> page_element_detection_flow
    page_element_detection_flow >> end

    class CrawlAndElementDetectionFlow(AsyncFlow):
        pass

    flow = CrawlAndElementDetectionFlow(start=start)
    return flow
