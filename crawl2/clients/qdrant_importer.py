from crawl2.qdrant_index.collection_knowledge_point_index import (
    CollectionKnowledgePointIndex,
)
from crawl2.qdrant_index.product_knowledge_point_index import (
    ProductKnowledgePointIndex,
)
from crawl2.qdrant_index.store_knowledge_point_index import (
    StoreKnowledgePointIndex,
)
from crawl2.qdrant_index.base_qdrant_indexer import IndexConfig
from crawl2.web.schema.store_knowledge_point import KnowledgePointResponse
from crawl2.qdrant_index.store_knowledge_point_index import StoreKnowledgePoint
from crawl2.config import settings
from fastapi import HTTPException


class QdrantImporter:
    def __init__(
        self,
        embedding_client_url: str,
        sparse_embedding_client_url: str,
        qdrant_url: str,
        vector_size: int,
        collection_name_dict: dict[str, str],
    ):
        self.embedding_client_url = embedding_client_url
        self.sparse_embedding_client_url = sparse_embedding_client_url
        self.qdrant_url = qdrant_url
        self.vector_size = vector_size
        self.collection_name_dict = collection_name_dict
        self.product_knowledge_point_index = ProductKnowledgePointIndex(
            IndexConfig(
                collection_name=collection_name_dict["product_knowledge_point"],
                embedding_client_url=embedding_client_url,
                sparse_embedding_client_url=sparse_embedding_client_url,
                qdrant_url=qdrant_url,
                vector_size=vector_size,
            )
        )
        self.store_knowledge_point_index = StoreKnowledgePointIndex(
            IndexConfig(
                collection_name=collection_name_dict["store_knowledge_point"],
                embedding_client_url=embedding_client_url,
                sparse_embedding_client_url=sparse_embedding_client_url,
                qdrant_url=qdrant_url,
                vector_size=vector_size,
            )
        )
        self.collection_knowledge_point_index = CollectionKnowledgePointIndex(
            IndexConfig(
                collection_name=collection_name_dict["collection_knowledge_point"],
                embedding_client_url=embedding_client_url,
                sparse_embedding_client_url=sparse_embedding_client_url,
                qdrant_url=qdrant_url,
                vector_size=vector_size,
            )
        )

    async def import_all_product_knowledge_points(self, domain: str):
        await self.product_knowledge_point_index.import_all_knowledge_points(
            domain,
            batch_size=16,
            force_recreate_collection=False,
            delete_previous_shop_domain_data=True,
        )

    async def import_single_product_knowledge_points(self, url: str):
        await self.product_knowledge_point_index.import_single_product_knowledge_points(
            url
        )

    async def import_all_store_knowledge_points(self, domain: str):
        await self.store_knowledge_point_index.import_all_knowledge_points(
            domain,
            batch_size=16,
            force_recreate_collection=False,
            delete_previous_shop_domain_data=True,
        )

    async def import_all_collection_knowledge_points(self, domain: str):
        await self.collection_knowledge_point_index.import_all_knowledge_points(
            domain,
            batch_size=16,
            force_recreate_collection=False,
            delete_previous_shop_domain_data=True,
        )

    async def create_or_update_point(self, point: StoreKnowledgePoint) -> KnowledgePointResponse:
        await self.store_knowledge_point_index.upsert_point(point)
        point_in_qdrant = await self.store_knowledge_point_index.get_point(point.point_id)
        if not point_in_qdrant:
            raise HTTPException(status_code=404, detail="Faild to create or update point, point_id: " + point.point_id)
        return KnowledgePointResponse(**point_in_qdrant)

    async def get_store_knowledge_point(self, point_id: str) -> KnowledgePointResponse:
        point_in_qdrant = await self.store_knowledge_point_index.get_point(point_id)
        if not point_in_qdrant:
            raise HTTPException(status_code=404, detail="Point not found, point_id: " + point_id)
        return KnowledgePointResponse(**point_in_qdrant)

    async def delete_store_knowledge_point(self, point_id: str) -> bool:
        try:
            await self.store_knowledge_point_index.delete_point(point_id)
            return True
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to delete point: {str(e)}")

    async def update_store_knowledge_point(self, point_id: str, point: StoreKnowledgePoint) -> KnowledgePointResponse:
        if point_id != point.point_id:
            raise HTTPException(status_code=400, detail="point_id in path and body must match")

        try:
            await self.store_knowledge_point_index.update_point(point_id, point)
            updated_point = await self.store_knowledge_point_index.get_point(point_id)
            if not updated_point:
                raise HTTPException(status_code=404, detail="Point not found after update")
            return KnowledgePointResponse(**updated_point)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to update point: {str(e)}")

    async def list_store_knowledge_points(
        self, store_domain: str | None = None, source: str | None = None, source_detail: str | None = None,
        limit: int = 10, offset: int = 0
    ) -> list[KnowledgePointResponse]:
        from qdrant_client.models import Filter, FieldCondition, MatchValue

        try:
            must_conditions = []

            if store_domain:
                must_conditions.append(
                    FieldCondition(
                        key="store_domain",
                        match=MatchValue(value=store_domain.lower()),
                    )
                )

            if source:
                must_conditions.append(
                    FieldCondition(
                        key="source",
                        match=MatchValue(value=source),
                    )
                )

            if source_detail:
                must_conditions.append(
                    FieldCondition(
                        key="source_detail",
                        match=MatchValue(value=source_detail),
                    )
                )

            filter_condition = Filter(must=must_conditions) if must_conditions else None

            points = await self.store_knowledge_point_index.qdrant.scroll(
                collection_name=self.store_knowledge_point_index.config.collection_name,
                scroll_filter=filter_condition,
                limit=limit,
                offset=offset,
                with_payload=True,
                with_vectors=False,
            )

            results = []
            for point in points[0]:
                results.append(KnowledgePointResponse(**point.payload))
            return results
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to list points: {str(e)}")

    async def search_store_knowledge_points(
        self, query: str, store_domain: str | None = None, limit: int = 10
    ) -> list[KnowledgePointResponse]:
        from qdrant_client.models import Filter, FieldCondition, MatchValue

        try:
            query_vector = await self.store_knowledge_point_index.to_dense_embedding([query])

            if store_domain:
                filter_condition = Filter(
                    must=[
                        FieldCondition(
                            key="store_domain",
                            match=MatchValue(value=store_domain.lower()),
                        )
                    ]
                )
            else:
                filter_condition = None

            search_result = await self.store_knowledge_point_index.qdrant.search(
                collection_name=self.store_knowledge_point_index.config.collection_name,
                query_vector=("question-dense", query_vector[0]),
                query_filter=filter_condition,
                limit=limit,
                with_payload=True,
                with_vectors=False,
            )

            results = []
            for point in search_result:
                results.append(KnowledgePointResponse(**point.payload))
            return results
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to search points: {str(e)}")


# 这边先 hard code 些配置，后面放到 apollo 中
qdrant_importer_instance = QdrantImporter(
    embedding_client_url=settings.EMBEDDING_API_URL,
    sparse_embedding_client_url=settings.SPARSE_EMBEDDING_API_URL,
    qdrant_url=settings.QDRANT_URL,
    vector_size=settings.QDRANT_VECTOR_SIZE,
    collection_name_dict={
        "product_knowledge_point": settings.QDRANT_PRODUCT_KNOWLEDGE_COLLECTION,
        "store_knowledge_point": settings.QDRANT_STORE_KNOWLEDGE_COLLECTION,
        "collection_knowledge_point": settings.QDRANT_COLLECTION_KNOWLEDGE_COLLECTION,
    },
)
