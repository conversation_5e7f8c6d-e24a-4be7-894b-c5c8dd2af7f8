"""
将数据挖掘任务封装成 Celery 任务，并提供 FastAPI 路由以触发这些任务。

用法:
```python
from crawl2.tasks.celery_wrap import celery_task

@celery_task
def crawl_example_task(domain: str, product_url: str):
    pass
```

注意事项：
1. 所有任务参数必须是基本类型或基本类型的列表。
2. 尽量写可读性好的 函数名和 docstring，这将渲染成 UI 上的任务介绍
"""

import asyncio
import functools
import inspect
import os
import time
from typing import get_type_hints, get_origin, get_args, Dict, Generic, Type, Callable, Any, Coroutine

from celery import shared_task
from fastapi import APIRouter, HTTPException
from leyan_logging import context
from loguru import logger
from tortoise import Tortoise

from crawl2 import schema, utils
from crawl2.clients import dingtalk
from crawl2.config import settings
from crawl2.db import operations
from crawl2.db.models import CeleryTaskStatus
from crawl2.schema import CeleryTaskDetail
from crawl2.tasks.task_tqdm import CeleryTaskBoundTqdmMixin
from crawl2.utils import ModelT, extract_pydantic_model_for_function_params

# all crawl or mining tasks that can be run in celery workers
celery_tasks: Dict[str, 'CrawlTask'] = {}


def _run_async(coro):
    """运行异步函数的辅助函数"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError as e:
        if "no current event loop in thread" in str(e):
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        else:
            raise e
    if not getattr(loop, '_is_tortoise_inited', False):
        loop.run_until_complete(Tortoise.init(db_url=settings.DATABASE_URL, modules={"models": ['crawl2.db.models']}))
    loop.run_until_complete(coro)


class CrawlTask(Generic[ModelT]):
    def __init__(self, celery_shared_task, func, params_model: Type[ModelT]):
        self.celery_shared_task = celery_shared_task
        self.func = func
        self.name = func.__name__
        self.description = func.__doc__ or "No description provided"
        self.params_model = params_model
        self.authors: list[str] = []

    def __call__(self, *args, **kwargs):
        # delegate the call to the underlying function
        return self.func(*args, **kwargs)

    def si(self, **kwargs):
        """
        Create a Celery signature for the task.
        This method is used to create a Celery signature for the task.
        """
        return self.celery_shared_task.si(**kwargs)

    def has_param(self, param_name: str):
        return param_name in self.params_model.model_fields

    def create_trigger_endpoint(self) -> Callable[[ModelT], Coroutine[Any, Any, CeleryTaskDetail]]:
        """
        Generate a trigger endpoint for the task.
        This method is used to create a FastAPI endpoint for the task.
        """
        request_model = self.params_model

        async def trigger_endpoint(params: request_model) -> schema.CeleryTaskDetail:
            return await self.trigger_async(params)

        return trigger_endpoint

    async def trigger_async(self, params: ModelT) -> schema.CeleryTaskDetail:
        """
        Trigger the task asynchronously with the given parameters.
        """
        # Validate arguments
        if params and hasattr(params, 'domain') and isinstance(params.domain, str):
            params.domain = utils.extract_domain_from_shopify_site_url(params.domain)
            logger.info(f"Extracted domain {params.domain} from URL: {params}")
        kwargs = params.model_dump() if params else {}
        task_detail = await operations.create_task(self.name, kwargs)
        self.celery_shared_task.apply_async(kwargs=kwargs, task_id=task_detail.task_id)
        logger.bind(task_id=task_detail.task_id, task_name=self.name).info('已创建和触发任务')
        return task_detail

    async def trigger_async2(self, **kwargs) -> schema.CeleryTaskDetail:
        """A simple wrap of trigger_async to allow calling with keyword arguments."""
        return await self.trigger_async(self.params_model.model_validate(kwargs, strict=False))

    @classmethod
    def validate_crawl_task_func(cls, func):
        # requirements for the function to be a valid celery task:
        # 1. it must be a coroutine function
        # 2. all arguments must be of primitive types (int, str, float, bool, None) or collections(list) of them

        if not inspect.iscoroutinefunction(func):
            raise TypeError(f"Function {func} is not a coroutine function")

        sig = inspect.signature(func)
        params = list(sig.parameters.values())

        # Get full type hints
        type_hints = get_type_hints(func)

        primitive_types = (int, str, float, bool, type(None))
        # Check other argument types
        for param in params:
            arg_type = type_hints.get(param.name, None)
            if arg_type in primitive_types:
                continue
            origin = get_origin(arg_type)
            args_ = get_args(arg_type)
            if origin is list and all(t in primitive_types for t in args_):
                continue
            raise TypeError(
                f"Argument {param.name} of function {func} must be of primitive type or list of primitive types"
            )

    @classmethod
    async def _notify_task_error_via_dingtalk(cls, task_name: str, task_id: str, duration: int, ex: Exception):
        env = os.environ.get('env', 'stq').lower()
        log_url = f"https://create.infra.leyantech.com/api/v2/redirect/kibana?env=warm-{env}&contextMap.task_id={task_id}&from=now-7d&to=now"   # NOQA
        authors = celery_tasks.get(task_name, None).authors if task_name in celery_tasks else []
        authors_str = ', '.join(authors) if authors else 'Unknown Authors'
        title = f"任务 `{task_name}` 执行失败"
        message = f"""
任务 `{task_name}` 执行 {duration}秒后失败: {ex}, [日志]({log_url}), 可能相关的开发人员: {authors_str}
"""
        await dingtalk.send_dingtalk_message(settings.DINGTALK_WEBHOOK, title, message)

    @classmethod
    def from_func(cls, func) -> 'CrawlTask':
        params_model: Type[ModelT] = extract_pydantic_model_for_function_params(func)

        @shared_task(name=func.__name__, bind=True)
        @functools.wraps(func)
        def callable_in_celery_worker(self, *args, **kwargs):
            task_id, task_name = self.request.id, func.__name__
            headers = self.request.headers or {}
            workflow_id = headers.get('workflow_id', None)
            workflow_name = headers.get('workflow_name', None)
            start = time.time()

            async def _execute_task():
                with (logger.contextualize(task_id=task_id, task_name=task_name,
                                           workflow_id=str(workflow_id), workflow_name=workflow_name)):
                    params_obj = params_model.model_validate(self.request.kwargs, strict=False)
                    if hasattr(params_obj, 'domain') and isinstance(params_obj.domain, str):
                        params_obj.domain = utils.extract_domain_from_shopify_site_url(params_obj.domain)
                        context.update(domain=params_obj.domain)
                        logger.info(f"Extracted domain {params_obj.domain} from URL: {params_obj}")
                    params_dict = params_obj.model_dump()
                    logger.info(f"start running with params: {params_dict}")
                    try:
                        task_detail = await operations.mark_celery_task_start(self.request.id, workflow_id,
                                                                              func.__name__, params_dict)
                        CeleryTaskBoundTqdmMixin.mark_task_started(task_detail.id)
                        retval = await func(**params_dict)
                        duration = int((time.time() - start))
                        duration_context = {'metrics.duration': duration}
                        logger.bind(**duration_context).info(f"Task {task_name} succeeded in {duration} seconds")
                        await operations.mark_celery_task_success(task_id, str(retval))
                    except Exception as e:
                        duration = int((time.time() - start))
                        duration_context = {'metrics.duration': duration}
                        (logger
                         .bind(**duration_context)
                         .opt(exception=e)
                         ).error(f"Task {task_name} failed in {duration} seconds error: {e}")
                        await operations.mark_celery_task_failure(task_id, str(e))
                        await cls._notify_task_error_via_dingtalk(task_name, task_id, duration, e)
                        raise e
                    finally:
                        await CeleryTaskBoundTqdmMixin.mark_task_finished()

            _run_async(_execute_task())

        return CrawlTask(callable_in_celery_worker, func, params_model)


def celery_task(func):
    """
    Decorator to register a shopify crawl/mining function as a Celery task.
    """
    CrawlTask.validate_crawl_task_func(func)

    if func.__name__ in celery_tasks:
        raise ValueError(f"Function {func} is already registered as {celery_tasks[func.__name__]}")
    else:
        celery_tasks[func.__name__] = CrawlTask.from_func(func)
    return celery_tasks[func.__name__]


async def _retry_task_standalone(task_detail: schema.CeleryTaskDetail, original_task: CrawlTask):
    """Retry a standalone task (no workflow) with the same parameters."""
    # Create a new task with the same parameters


def create_fast_api_router() -> APIRouter:
    """
    Register all registered tasks as web API endpoints.
    """
    router = APIRouter(prefix="/tasks", tags=["tasks"])

    @router.get("/definitions")
    async def list_task_definitions() -> list[schema.CrawlTaskDefinition]:
        """
        List all available crawl tasks with their definitions.
        """
        res = []
        for definition in celery_tasks.values():
            res.append(schema.CrawlTaskDefinition(
                name=definition.name,
                description=definition.description.strip(),
                params=definition.params_model.model_json_schema(),
                authors=definition.authors,
            ))
        return sorted(res, key=lambda x: x.name)

    @router.get("/run-stats")
    async def get_task_run_stats() -> list[schema.TaskRunStats]:
        """
        Get execution statistics for all tasks.
        """
        stats_data = await operations.get_task_run_stats()
        return stats_data

    @router.get("/runs")
    async def list_task_runs(
        domain: str | None = None,
        product_url: str | None = None,
        page: int = 1,
        size: int = 20,
        task_name: str | None = None,
        status: str | None = None
    ) -> schema.PaginatedResponse[schema.CeleryTaskDetail]:
        """
        Find triggered tasks for a given site URL or product ID, with pagination.
        """
        return await operations.list_celery_tasks(domain=domain, product_url=product_url,
                                                  task_name=task_name, status=status, page=page, size=size)

    @router.get("/runs/{task_id}")
    async def get_task_details(task_id: str) -> schema.CeleryTaskDetail:
        """
        Get details of a specific task by its ID.
        """
        task_detail = await operations.get_celery_task(task_id)
        if not task_detail:
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")
        return task_detail

    @router.post("/runs/{task_id}/cancel")
    async def cancel_task(task_id: str) -> schema.CeleryTaskDetail:
        """
        Cancel a specific task by its ID.
        """
        # First check if task exists
        task_detail = await operations.get_celery_task(task_id)
        if not task_detail:
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

        # Check if task is already finished
        if task_detail.status in ('SUCCESS', 'FAILED', 'CANCELED'):
            raise HTTPException(status_code=400, detail=f"Task {task_id} is already finished with status {task_detail.status}")

        # Try to revoke the task in Celery
        try:
            from celery import current_app
            current_app.control.revoke(task_id, terminate=True)
            logger.info(f"Revoked Celery task {task_id}")
        except Exception as e:
            logger.warning(f"Failed to revoke Celery task {task_id}: {e}")

        canceled_task = await operations.mark_celery_task_canceled(task_id)
        if not canceled_task:
            raise HTTPException(status_code=500, detail=f"Failed to cancel task {task_id}")

        return canceled_task

    @router.post("/runs/{task_id}/retry")
    async def retry_task(task_id: str) -> schema.CeleryTaskDetail:
        """
        重试一个失败或取消的任务，如果任务有工作流，除了重试当前任务外，还会重试后续所有任务。
        """
        from crawl2.workflows import retry_workflow

        # First check if task exists
        task_detail = await operations.get_celery_task(task_id)
        if not task_detail:
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

        # Check if task can be retried
        if task_detail.status not in (CeleryTaskStatus.FAILED, CeleryTaskStatus.CANCELED):
            raise HTTPException(status_code=400, detail=f"Task {task_id} cannot be retried with status {task_detail.status}")  # NOQA

        # Get the original task from celery_tasks
        if task_detail.name not in celery_tasks:
            raise HTTPException(status_code=400, detail=f"Task {task_detail.name} is not available for retry")

        original_task = celery_tasks[task_detail.name]

        if task_detail.workflow_id is not None:
            # If task has workflow, create a new truncated workflow
            await retry_workflow(task_detail.workflow_id, task_detail.name)
            logger.info(f"Retried task {task_detail.name} with workflow ID: {task_detail.workflow_id}")
        else:
            # If task has no workflow, retry as a new standalone task
            new_task_detail = await original_task.trigger_async2(**task_detail.params)
            logger.info(f"Retried standalone task {task_detail.name} with new task ID: {new_task_detail.task_id}")

        # Mark the original task as retried
        retried_task = await operations.mark_celery_task_retried(task_id)
        if not retried_task:
            raise HTTPException(status_code=500, detail=f"Failed to mark task {task_id} as retried")

        return retried_task

    for task in celery_tasks.values():
        router.add_api_route(
            path=f"/trigger/{task.name}",
            endpoint=task.create_trigger_endpoint(),
            methods=["POST"],
            name=task.name,
            description=task.description,
        )
    return router
