import json
from collections import defaultdict

from loguru import logger

from crawl2 import schema
from crawl2.clients import llm
from crawl2.clients import shopify_knowledge
from crawl2.db import operations, models
from crawl2.tasks.celery_wrap import celery_task
from crawl2.tasks.task_tqdm import CeleryTaskTqdmAsync
from crawl2.utils import is_null_like

PRODUCT_PV_EXTRACTION_PROMPT_TEMPLATE = '''
You are a precise product information extractor. Your task is to extract product properties from the given markdown content according to the exact schema provided.

The schema defines the structure and data types that must be followed:
{schema}

The markdown content of the product is:
{markdown_content}

Requirements:
1. Extract ONLY the properties defined in the schema
2. Follow the EXACT property names as specified in the schema
3. Ensure each value strictly matches its defined data type
4. If a property cannot be found or is uncertain, use null
5. Return the result in valid JSON format wrapped in ```json ```
6. DO NOT add any properties that are not in the schema
7. DO NOT modify or rename any property names

Example format:
```json
{{
    "property_name_1": value_matching_schema_type,
    "property_name_2": value_matching_schema_type,
    ...
}}
```

Extract the properties now:
'''

llm_conf = llm.LLMConfig('usq', 'qwen2.5-32b-chat-fp8-offline', 0, 5000)


async def extract_product_pv(metadata: models.ProductMetadata, schema: schema.ProductTypePvSchema) -> dict | None:
    knowledge = await metadata.product_knowledge.order_by('-id').first()
    if knowledge is None:
        logger.warning(f"No knowledge found for {metadata.url}")
        return None
    prompt = PRODUCT_PV_EXTRACTION_PROMPT_TEMPLATE.format(
        schema=schema.properties, markdown_content=knowledge.markdown_content)
    logger.info(f"Extracting product pv for {metadata.url} with prompt: {prompt}")
    result = await llm.call_llm(llm_conf, prompt)
    logger.info(f"Extracted product pv for {metadata.url} with result: {result}")
    if result is None:
        return None
    return result


@celery_task
async def extract_product_pv_for_single_product(product_url: str, clear_existing: bool = False):
    """
    挖掘单个商品的属性和属性值（product pv）
    """
    metadata = await operations.get_product_metadata(product_url)
    if metadata is None:
        logger.warning(f"No metadata found for {product_url}")
        return
    existed = await operations.get_product_property_and_value(metadata)
    if existed and not clear_existing:
        logger.info(f"Product property and value already exists for {metadata.url}, skipping extraction.")
        return
    elif existed and clear_existing:
        logger.info(f"Clearing existing product property and value for {metadata.url}.")
        await operations.delete_product_property_and_value(metadata)
    site = await metadata.site.first()
    if site is None:
        logger.warning(f"No site found for {metadata.url}")
        return
    domain = site.domain
    schema = await operations.get_product_type_pv_schema(domain, metadata.product_type)
    if schema is None:
        logger.warning(f"No schema found for {metadata.url}, domain: {domain}, product_type: {metadata.product_type}")
        return
    result = await extract_product_pv(metadata, schema)
    if result:
        await operations.save_product_property_and_value(metadata, result)


@celery_task
async def extract_product_pv_for_all_products(domain: str, clear_existing: bool = False):
    """
    挖掘指定站点所有商品的属性和属性值（product pv）
    """
    all_products = await operations.list_product_metadata(domain)
    tasks = [extract_product_pv_for_single_product(
        product.url, clear_existing) for product in all_products]
    await CeleryTaskTqdmAsync.gather(*tasks, desc="Extracting product pv")


@celery_task
async def sync_product_pv_to_knowledge_base(domain: str):
    """
    同步产品属性和属性值到 Shopify knowledge
    """
    knowledge_client = shopify_knowledge.ShopifyKnowledgeClient.get_client()
    site = await operations.get_site(domain)
    if not site:
        logger.error(f"No site found for {domain}")
        return
    store_domain = site.shopify_domain
    product_property_and_values = await operations.list_product_property_and_value(domain)
    logger.info(f"Found {len(product_property_and_values)} product property and value for {domain}")
    store_attributes = defaultdict(lambda: defaultdict(lambda: defaultdict(set)))
    for product_property_and_value in product_property_and_values:
        metadata = await product_property_and_value.metadata
        product_type = metadata.product_type
        properties = product_property_and_value.property_values
        for key, value in properties.items():
            if not is_null_like(value):
                if isinstance(value, (list, set)):
                    # 过滤掉列表中的空值
                    filtered_values = [v for v in value if not is_null_like(v)]
                    if filtered_values:
                        store_attributes[store_domain][product_type][key].update(filtered_values)
                else:
                    if isinstance(value, (int, float, str)):
                        store_attributes[store_domain][product_type][key].add(value)
                    else:
                        logger.warning("invald value type: ", value, "ignore it")
    # 将 defaultdict 和 set 转换为普通的 dict 和 list
    result_dict = {}
    for store_domain, store_data in store_attributes.items():
        result_dict[store_domain] = {}
        for product_type, attrs in store_data.items():
            result_dict[store_domain][product_type] = {
                key: list(values) for key, values in attrs.items()
            }
    logger.info(f"Found {len(result_dict)} product attributes for {domain}")
    logger.info(f"store_attributes: {result_dict}")
    for store_domain, store_data in result_dict.items():
        items = []
        for product_type, attributes in store_data.items():
            items.append(shopify_knowledge.ProductAttributesItem(
                productType=product_type,
                attributes=json.dumps(attributes, ensure_ascii=False)
            ))
        req = shopify_knowledge.ImportProductAttributesReq(
            storeDomain=store_domain,
            items=items
        )
        result = await knowledge_client.import_product_attributes(req)
        logger.info(f"Synced product attributes to knowledge base for {store_domain}, result: {result}")
