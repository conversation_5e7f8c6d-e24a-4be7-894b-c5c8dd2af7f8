services:
  web:
    image: shop-pupil
    command: uvicorn --host 0.0.0.0 crawl2.web.main:app
    environment:
      - SENTRY_DSN=https://<EMAIL>/11563
      - SENTRY_ENVIRONMENT=staging
      - LAIN_PROCNAME=web
      - SSO_REDIRECT_URI=http://************:8000/api/v1/auth/callback
    ports:
      - "8000:8000"
    depends_on:
      - redis

  celery-worker:
    image: shop-pupil
    command: celery --app crawl2.celery_app:app worker --loglevel info --concurrency 4 --queues celery,lechat
    environment:
      - SENTRY_DSN=https://<EMAIL>/11563
      - SENTRY_ENVIRONMENT=staging
      - LAIN_PROCNAME=celery-worker
    depends_on:
      - redis

  redis:
    image: m.daocloud.io/docker.io/redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  redis_data:
