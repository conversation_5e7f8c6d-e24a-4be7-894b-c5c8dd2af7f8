import { ref, computed } from 'vue'
import { api } from '@/api/api.js'

// 全局缓存状态 - 只缓存 task definitions
const taskDefinitions = ref([])
// 新增：包含统计信息的完整任务数据
const fullTaskData = ref([])
const loading = ref(false)
const error = ref(null)
const lastFetchTime = ref(null)
// 新增：跟踪 stats 加载状态
const statsLoading = ref(false)

// 缓存过期时间：5分钟
const CACHE_DURATION = 5 * 60 * 1000

// 检查缓存是否过期
const isCacheExpired = () => {
  if (!lastFetchTime.value) return true
  return Date.now() - lastFetchTime.value > CACHE_DURATION
}

// 获取task definitions数据（带缓存）
const fetchTaskDefinitions = async (forceRefresh = false) => {
  // 如果缓存未过期且不是强制刷新，直接返回缓存数据
  if (!forceRefresh && !isCacheExpired() && taskDefinitions.value.length > 0) {
    return taskDefinitions.value
  }

  try {
    loading.value = true
    error.value = null
    
    const response = await api.get('/api/v1/tasks/definitions')
    const definitions = response.data
    
    taskDefinitions.value = definitions
    lastFetchTime.value = Date.now()
    
    return definitions
  } catch (err) {
    error.value = err.message
    console.error('Error fetching task definitions:', err)
    throw err
  } finally {
    loading.value = false
  }
}

// 获取task stats数据（实时，不缓存）
const fetchTaskStats = async () => {
  try {
    const response = await api.get('/api/v1/tasks/run-stats')
    return response.data
  } catch (err) {
    console.error('Error fetching task stats:', err)
    throw err
  }
}

// 获取完整的任务数据（definitions + stats）
const fetchFullTaskData = async (forceRefresh = false) => {
  try {
    // 先获取任务定义
    const definitions = await fetchTaskDefinitions(forceRefresh)
    
    // 立即用默认 stats 更新数据，避免页面空白
    const initialDefinitions = definitions.map(task => ({
      ...task,
      stats: {
        total_runs: 0,
        success_count: 0,
        failed_count: 0,
        running_count: 0,
        pending_count: 0,
        last_run_time: null,
        last_run_status: null
      }
    }))
    
    // 更新完整任务数据
    fullTaskData.value = initialDefinitions
    
    // 异步加载 stats，不阻塞页面渲染
    statsLoading.value = true
    fetchTaskStats().then(stats => {
      // 将统计信息合并到任务定义中
      const enrichedDefinitions = definitions.map(task => {
        const taskStats = stats.find(s => s.name === task.name)
        return {
          ...task,
          stats: taskStats || {
            total_runs: 0,
            success_count: 0,
            failed_count: 0,
            running_count: 0,
            pending_count: 0,
            last_run_time: null,
            last_run_status: null
          }
        }
      })
      
      // 更新完整任务数据
      fullTaskData.value = enrichedDefinitions
      statsLoading.value = false
    }).catch(err => {
      console.error('Error fetching task stats:', err)
      statsLoading.value = false
      // 即使 stats 加载失败，页面仍然可以显示基本的任务信息
    })
    
    return { definitions: initialDefinitions, stats: null }
  } catch (err) {
    console.error('Error fetching full task data:', err)
    throw err
  }
}

// 根据任务名称获取任务定义
const getTaskDefinition = (taskName) => {
  return taskDefinitions.value.find(task => task.name === taskName)
}

// 获取任务的中文描述
const getTaskDescription = (taskName) => {
  const task = getTaskDefinition(taskName)
  return task?.description || ''
}

// 获取任务的维护者（第一个作者）
const getTaskMaintainer = (taskName) => {
  const task = getTaskDefinition(taskName)
  return task?.authors?.[0] || null
}

// 获取任务的所有作者
const getTaskAuthors = (taskName) => {
  const task = getTaskDefinition(taskName)
  return task?.authors || []
}

// 检查是否有缓存数据
const hasCachedData = computed(() => taskDefinitions.value.length > 0)

// 清除缓存
const clearCache = () => {
  taskDefinitions.value = []
  fullTaskData.value = []
  lastFetchTime.value = null
}

// 任务相关的其他API封装
const triggerTask = async (taskName, params = {}) => {
  try {
    const response = await api.post(`/api/v1/tasks/trigger/${taskName}`, params)
    return response.data
  } catch (err) {
    console.error(`Error triggering task ${taskName}:`, err)
    throw err
  }
}

const cancelTask = async (taskId) => {
  try {
    const response = await api.post(`/api/v1/tasks/runs/${taskId}/cancel`)
    return response.data
  } catch (err) {
    console.error(`Error canceling task ${taskId}:`, err)
    throw err
  }
}

const retryTask = async (taskId) => {
  try {
    const response = await api.post(`/api/v1/tasks/runs/${taskId}/retry`)
    return response.data
  } catch (err) {
    console.error(`Error retrying task ${taskId}:`, err)
    throw err
  }
}

const getTaskDetails = async (taskId) => {
  try {
    const response = await api.get(`/api/v1/tasks/runs/${taskId}`)
    return response.data
  } catch (err) {
    console.error(`Error getting task details ${taskId}:`, err)
    throw err
  }
}

const listTaskRuns = async (params = {}) => {
  try {
    const response = await api.get('/api/v1/tasks/runs', { params })
    return response.data
  } catch (err) {
    console.error('Error listing task runs:', err)
    throw err
  }
}

export function useTasks() {
  return {
    // 状态
    taskDefinitions: computed(() => taskDefinitions.value),
    fullTaskData: computed(() => fullTaskData.value), // 新增：包含统计信息的完整数据
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    hasCachedData: computed(() => hasCachedData.value),
    statsLoading: computed(() => statsLoading.value), // 新增：stats 加载状态
    
    // 数据获取方法
    fetchTaskDefinitions,      // 获取任务定义（带缓存）
    fetchTaskStats,            // 获取任务统计（实时）
    fetchFullTaskData,         // 获取完整数据（definitions + stats）
    
    // 任务信息获取方法
    getTaskDefinition,
    getTaskDescription,
    getTaskMaintainer,
    getTaskAuthors,
    
    // 任务操作API
    triggerTask,               // 触发任务
    cancelTask,                // 取消任务
    retryTask,                 // 重试任务
    getTaskDetails,            // 获取任务详情
    listTaskRuns,              // 列出任务运行记录
    
    // 缓存管理
    clearCache,
    refresh: () => fetchTaskDefinitions(true)
  }
}
