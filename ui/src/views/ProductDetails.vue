<script setup>
import { ref, onMounted, provide } from 'vue'
import { useRoute } from 'vue-router'
import { api } from '@/api/api.js'
import ProductMetadata from '../components/product/ProductMetadata.vue'
import ProductPageMarkdown from '../components/product/ProductPageMarkdown.vue'
import ProductKnowledge from '../components/product/ProductKnowledge.vue'
import ProductSellingPoints from '../components/product/ProductSellingPoints.vue'
import ProductFaqs from '../components/product/ProductFaqs.vue'
import ProductConcernPoint from '../components/product/ProductConcernPoint.vue'
import Navigation from '../components/common/Navigation.vue'
import ProductElements from '../components/product/ProductElements.vue'
import { Clock } from 'lucide-vue-next'

const route = useRoute()
const product = ref(null)
const loading = ref(true)
const error = ref(null)
const activeTab = ref('page')

// 各tab的数据量
const tabDataCounts = ref({
  page: 0,
  knowledge: 0,
  selling: 0,
  faqs: 0,
  concerns: 0,
  elements: 0
})

// 获取指定tab的数据量
const getTabDataCount = (tab) => {
  return tabDataCounts.value[tab] || 0
}

const fetchProductDetails = async () => {
  try {
    loading.value = true
    const response = await api.get(`/api/v1/products/${route.params.domain}/${route.params.productId}`)
    product.value = response.data
    
    // 设置page和knowledge的数据量
    if (product.value) {
      // page tab - 如果有页面内容，设置为1
      tabDataCounts.value.page = product.value.page_markdown ? 1 : 0
      
      // knowledge tab - 如果有知识文档，设置为文档数量
      if (product.value.knowledge_documents && product.value.knowledge_documents.length > 0) {
        tabDataCounts.value.knowledge = product.value.knowledge_documents.length
      } else if (product.value.knowledge_markdown) {
        tabDataCounts.value.knowledge = 1
      } else {
        tabDataCounts.value.knowledge = 0
      }
      
      // elements tab - 暂时设置为0，后续可以通过API获取
      tabDataCounts.value.elements = 0
    }
  } catch (err) {
    error.value = err.message
    console.error('Error fetching product details:', err)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchProductDetails()
})
</script>

<template>
  <div class="p-8 max-w-[1200px] mx-auto pt-0">
    <div v-if="loading" class="text-center p-8 text-gray-600">
      Loading product details...
    </div>
    <div v-else-if="error" class="text-center p-8 text-red-500">
      Error: {{ error }}
    </div>
    <div v-else-if="product" class="flex flex-col gap-8">
      <Navigation 
        :breadcrumbs="[
          { text: 'Sites', path: '/' },
          { text: 'Site Details', path: `/sites/${route.params.domain}` },
          { text: 'Product Details' }
        ]"
      >
        <template #right>
          <router-link 
            :to="`/task-runs?product_url=${encodeURIComponent(product.url)}`"
            class="flex items-center gap-2 p-2 cursor-pointer text-gray-600 transition-colors duration-200 hover:text-green-500"
            title="查看任务历史"
          >
            <span class="bg-green-500 text-white px-1 py-0.5 rounded-full text-xs font-medium" v-if="false">
              0
            </span>
            <Clock size="16" />
          </router-link>
        </template>
      </Navigation>
      <ProductMetadata :product="product" :domain="route.params.domain" />
      
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="flex bg-gray-50 border-b border-gray-200 p-2 gap-2">
          <button 
            v-for="tab in ['page', 'knowledge', 'selling', 'faqs', 'concerns', 'elements']"
            :key="tab"
            :class="{ 'bg-white text-gray-800 shadow-sm': activeTab === tab, 'bg-transparent text-gray-700 hover:bg-gray-100': activeTab !== tab }"
            class="p-2 px-4 border-none cursor-pointer rounded font-medium transition-all duration-200 relative"
            @click="activeTab = tab"
          >
            {{ tab.charAt(0).toUpperCase() + tab.slice(1) }}
            <!-- 显示数据量的badge -->
            <span 
              v-if="getTabDataCount(tab) > 0"
              class="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
            >
              {{ getTabDataCount(tab) }}
            </span>
          </button>
        </div>
        
        <div class="p-6 min-h-[400px]">
          <!-- 所有子组件都渲染，但根据activeTab控制显示 -->
          <div class="w-full max-w-full overflow-x-hidden" :class="{ 'hidden': activeTab !== 'page' }">
            <ProductPageMarkdown :markdown="product.page_markdown" :productUrl="product.url" />
          </div>
          
          <div class="w-full max-w-full overflow-x-hidden" :class="{ 'hidden': activeTab !== 'knowledge' }">
            <ProductKnowledge 
              :markdown="product.knowledge_markdown" 
              :documents="product.knowledge_documents"
              :productUrl="product.url"
            />
          </div>
          
          <div class="w-full max-w-full overflow-x-hidden" :class="{ 'hidden': activeTab !== 'selling' }">
            <ProductSellingPoints 
              @dataCountChange="(count) => tabDataCounts.selling = count"
              :domain="route.params.domain"
              :productId="route.params.productId"
              :productUrl="product.url"
            />
          </div>

          <div class="w-full max-w-full overflow-x-hidden" :class="{ 'hidden': activeTab !== 'elements' }">
            <ProductElements :productUrl="product.url" />
          </div>

          <div class="w-full max-w-full overflow-x-hidden" :class="{ 'hidden': activeTab !== 'faqs' }">
            <ProductFaqs 
              @dataCountChange="(count) => tabDataCounts.faqs = count"
              :domain="route.params.domain"
              :productId="route.params.productId"
            />
          </div>
          
          <div class="w-full max-w-full overflow-x-hidden" :class="{ 'hidden': activeTab !== 'concerns' }">
            <ProductConcernPoint 
              @dataCountChange="(count) => tabDataCounts.concerns = count"
              :domain="route.params.domain"
              :productId="route.params.productId"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template> 